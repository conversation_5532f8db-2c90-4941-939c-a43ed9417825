{% extends "base.html" %}

{% block title %}Login | Applicant Tracking System{% endblock %}

{% block header_title %}Login{% endblock %}
{% block header_subtitle %}Access your account{% endblock %}

{% block description %}Login to access your account and manage your applications.{% endblock %}

{% block content %}
    <div class="auth-container fade-in">
        <div class="auth-card slide-in-bottom">
            <div class="auth-tabs">
                <button class="auth-tab-btn active" data-tab="user-login">User Login</button>
                <button class="auth-tab-btn" data-tab="admin-login">Admin Login</button>
            </div>
            
            <div class="auth-tab-content active" id="user-login">
                <h2><i class="fas fa-user"></i> User Login</h2>
                <p>Login to access your applicant dashboard and manage your job applications.</p>
                
                <form id="user-login-form" action="{{ url_for('login') }}" method="POST" class="auth-form">
                    <input type="hidden" name="role" value="user">
                    
                    <div class="form-group">
                        <label for="user-username">Username</label>
                        <div class="input-with-icon">
                            <i class="fas fa-user"></i>
                            <input type="text" id="user-username" name="username" placeholder="Enter your username" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="user-password">Password</label>
                        <div class="input-with-icon">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="user-password" name="password" placeholder="Enter your password" required>
                        </div>
                    </div>
                    
                    <div class="form-group remember-me">
                        <input type="checkbox" id="user-remember" name="remember">
                        <label for="user-remember">Remember me</label>
                    </div>
                    
                    <button type="submit" class="auth-button pulse">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                </form>
                
                <div class="auth-links">
                    <a href="{{ url_for('register') }}" class="register-link">Don't have an account? Register</a>
                    <a href="{{ url_for('forgot_password') }}" class="forgot-link">Forgot password?</a>
                </div>
            </div>
            
            <div class="auth-tab-content" id="admin-login">
                <h2><i class="fas fa-user-shield"></i> Admin Login</h2>
                <p>Login to access the admin dashboard and manage the application system.</p>
                
                <form id="admin-login-form" action="{{ url_for('login') }}" method="POST" class="auth-form">
                    <input type="hidden" name="role" value="admin">
                    
                    <div class="form-group">
                        <label for="admin-username">Admin Username</label>
                        <div class="input-with-icon">
                            <i class="fas fa-user-tie"></i>
                            <input type="text" id="admin-username" name="username" placeholder="Enter admin username" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin-password">Password</label>
                        <div class="input-with-icon">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="admin-password" name="password" placeholder="Enter admin password" required>
                        </div>
                    </div>
                    
                    <div class="form-group remember-me">
                        <input type="checkbox" id="admin-remember" name="remember">
                        <label for="admin-remember">Remember me</label>
                    </div>
                    
                    <button type="submit" class="auth-button pulse">
                        <i class="fas fa-sign-in-alt"></i> Admin Login
                    </button>
                </form>
                
                <div class="auth-note">
                    <i class="fas fa-info-circle"></i> Admin access is restricted to authorized personnel only.
                </div>
            </div>
            
            {% if error %}
            <div class="alert alert-error shake">
                <i class="fas fa-exclamation-circle"></i> {{ error }}
            </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab switching functionality
        const tabButtons = document.querySelectorAll('.auth-tab-btn');
        const tabContents = document.querySelectorAll('.auth-tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked button
                this.classList.add('active');
                
                // Show corresponding content
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });
    });
</script>
{% endblock %}

{% block extra_head %}
<style>
    .auth-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px 20px;
    }
    
    .auth-card {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        width: 100%;
        max-width: 500px;
        overflow: hidden;
    }
    
    .auth-tabs {
        display: flex;
        border-bottom: 1px solid #eee;
    }
    
    .auth-tab-btn {
        flex: 1;
        padding: 15px;
        background: none;
        border: none;
        font-size: 1rem;
        font-weight: 600;
        color: #666;
        cursor: pointer;
        transition: var(--transition);
    }
    
    .auth-tab-btn:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }
    
    .auth-tab-btn.active {
        color: var(--primary-color);
        border-bottom: 3px solid var(--primary-color);
    }
    
    .auth-tab-content {
        padding: 30px;
        display: none;
    }
    
    .auth-tab-content.active {
        display: block;
        animation: fadeIn 0.5s ease;
    }
    
    .auth-tab-content h2 {
        color: var(--secondary-color);
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .auth-tab-content p {
        color: #666;
        margin-bottom: 25px;
    }
    
    .auth-form {
        margin-top: 20px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }
    
    .input-with-icon {
        position: relative;
    }
    
    .input-with-icon i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #aaa;
    }
    
    .input-with-icon input {
        width: 100%;
        padding: 12px 15px 12px 45px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-size: 1rem;
        transition: var(--transition);
    }
    
    .input-with-icon input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        outline: none;
    }
    
    .remember-me {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .auth-button {
        display: block;
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        margin-top: 30px;
    }
    
    .auth-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .auth-links {
        margin-top: 25px;
        display: flex;
        justify-content: space-between;
        font-size: 0.9rem;
    }
    
    .auth-links a {
        color: var(--primary-color);
        text-decoration: none;
        transition: var(--transition);
    }
    
    .auth-links a:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }
    
    .auth-note {
        margin-top: 25px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        font-size: 0.9rem;
        color: #666;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .auth-note i {
        color: var(--primary-color);
        font-size: 1.2rem;
    }
    
    .alert {
        margin-top: 20px;
        padding: 15px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .alert-error {
        background-color: #ffebee;
        color: #c62828;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
</style>
{% endblock %}
