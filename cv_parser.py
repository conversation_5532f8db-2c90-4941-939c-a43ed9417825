"""
Advanced CV parsing module for the Applicant Tracking System.

This module provides functions for extracting information from CVs/resumes,
including personal details, skills, education, and work experience.
"""

import re
import os
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional, Set

# NLP libraries
try:
    from nltk.tokenize import word_tokenize, sent_tokenize
    from nltk.corpus import stopwords
    from nltk.stem import WordNetLemmatizer
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

from textblob import TextBlob

from io import BytesIO

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Document parsing libraries
try:
    import PyPDF2
    import pdfplumber
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    logger.warning("PDF parsing libraries not available. Install PyPDF2 and pdfplumber for PDF support.")

try:
    import docx
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    logger.warning("DOCX parsing library not available. Install python-docx for DOCX support.")

# Initialize NLTK components
if NLTK_AVAILABLE:
    try:
        # Try to download required NLTK data if not available
        import nltk
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            logger.info("Downloading NLTK punkt tokenizer...")
            nltk.download('punkt', quiet=True)

        try:
            nltk.data.find('corpora/stopwords')
        except LookupError:
            logger.info("Downloading NLTK stopwords...")
            nltk.download('stopwords', quiet=True)

        try:
            nltk.data.find('corpora/wordnet')
        except LookupError:
            logger.info("Downloading NLTK wordnet...")
            nltk.download('wordnet', quiet=True)

        lemmatizer = WordNetLemmatizer()
        stop_words = set(stopwords.words('english'))
        logger.info("NLTK components initialized successfully")
    except Exception as e:
        logger.warning(f"NLTK data not available: {str(e)}")
        NLTK_AVAILABLE = False
        lemmatizer = None
        stop_words = set()
else:
    lemmatizer = None
    stop_words = set()

# Define skill categories and keywords
SKILL_CATEGORIES = {
    'Programming Languages': [
        'python', 'java', 'javascript', 'c++', 'c#', 'ruby', 'php', 'swift', 'kotlin', 
        'typescript', 'go', 'rust', 'scala', 'perl', 'r', 'matlab', 'bash', 'shell',
        'html', 'css', 'sql', 'nosql', 'dart', 'objective-c', 'assembly', 'fortran'
    ],
    'Frameworks & Libraries': [
        'react', 'angular', 'vue', 'django', 'flask', 'spring', 'express', 'node.js',
        'tensorflow', 'pytorch', 'keras', 'scikit-learn', 'pandas', 'numpy', 'bootstrap',
        'jquery', 'laravel', 'symfony', 'rails', 'asp.net', '.net', 'xamarin', 'flutter'
    ],
    'Databases': [
        'mysql', 'postgresql', 'mongodb', 'sqlite', 'oracle', 'sql server', 'redis',
        'cassandra', 'elasticsearch', 'dynamodb', 'firebase', 'mariadb', 'neo4j', 'couchdb'
    ],
    'DevOps & Tools': [
        'git', 'docker', 'kubernetes', 'jenkins', 'aws', 'azure', 'gcp', 'terraform',
        'ansible', 'puppet', 'chef', 'circleci', 'travis', 'jira', 'confluence', 'bitbucket',
        'github', 'gitlab', 'linux', 'unix', 'windows', 'macos', 'bash', 'powershell'
    ],
    'Design & UI/UX': [
        'photoshop', 'illustrator', 'sketch', 'figma', 'adobe xd', 'indesign', 'ui', 'ux',
        'wireframing', 'prototyping', 'user research', 'usability testing', 'responsive design'
    ],
    'Soft Skills': [
        'communication', 'teamwork', 'leadership', 'problem solving', 'critical thinking',
        'time management', 'adaptability', 'creativity', 'emotional intelligence', 'negotiation',
        'conflict resolution', 'presentation', 'public speaking', 'writing', 'mentoring'
    ],
    'Languages': [
        'english', 'spanish', 'french', 'german', 'chinese', 'japanese', 'russian', 'arabic',
        'portuguese', 'italian', 'hindi', 'korean', 'dutch', 'swedish', 'greek', 'turkish'
    ],
    'Data Science': [
        'machine learning', 'deep learning', 'data mining', 'data analysis', 'statistics',
        'big data', 'data visualization', 'natural language processing', 'computer vision',
        'predictive modeling', 'a/b testing', 'etl', 'data warehousing', 'business intelligence'
    ],
    'Project Management': [
        'agile', 'scrum', 'kanban', 'waterfall', 'prince2', 'pmp', 'lean', 'six sigma',
        'risk management', 'budgeting', 'resource allocation', 'gantt', 'stakeholder management'
    ]
}

# Flatten the skills dictionary for easier lookup
ALL_SKILLS = {}
for category, skills in SKILL_CATEGORIES.items():
    for skill in skills:
        ALL_SKILLS[skill] = category

# Education degree patterns
EDUCATION_PATTERNS = [
    r'(?i)(?:^|\s)(B\.?S\.?|Bachelor of Science|Bachelor\'?s?)(?: degree)?(?: in )?([^.,]*)',
    r'(?i)(?:^|\s)(B\.?A\.?|Bachelor of Arts|Bachelor\'?s?)(?: degree)?(?: in )?([^.,]*)',
    r'(?i)(?:^|\s)(M\.?S\.?|Master of Science|Master\'?s?)(?: degree)?(?: in )?([^.,]*)',
    r'(?i)(?:^|\s)(M\.?A\.?|Master of Arts|Master\'?s?)(?: degree)?(?: in )?([^.,]*)',
    r'(?i)(?:^|\s)(M\.?B\.?A\.?|Master of Business Administration)(?: degree)?',
    r'(?i)(?:^|\s)(Ph\.?D\.?|Doctor of Philosophy)(?: degree)?(?: in )?([^.,]*)',
    r'(?i)(?:^|\s)(M\.?D\.?|Doctor of Medicine)(?: degree)?',
    r'(?i)(?:^|\s)(J\.?D\.?|Juris Doctor|Doctor of Law)(?: degree)?',
]

# Contact information patterns
EMAIL_PATTERN = r'[\w\.-]+@[\w\.-]+\.\w+'
PHONE_PATTERN = r'(?:\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
LINKEDIN_PATTERN = r'linkedin\.com/in/[\w\-]+'

def extract_text_from_file(file_path: str) -> str:
    """
    Extract text from a file based on its extension.

    Args:
        file_path: Path to the file

    Returns:
        Extracted text from the file
    """
    return extract_text_from_file_real(file_path)

def preprocess_text(text: str) -> str:
    """
    Preprocess the text by removing extra whitespace, converting to lowercase, etc.
    
    Args:
        text: Raw text from the CV
        
    Returns:
        Preprocessed text
    """
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove special characters
    text = re.sub(r'[^\w\s@.+-]', ' ', text)
    
    return text.strip()

def extract_contact_info(text: str) -> Dict[str, str]:
    """
    Extract contact information from the CV text.
    
    Args:
        text: Preprocessed CV text
        
    Returns:
        Dictionary containing extracted contact information
    """
    contact_info = {
        'email': None,
        'phone': None,
        'linkedin': None
    }
    
    # Extract email
    email_match = re.search(EMAIL_PATTERN, text)
    if email_match:
        contact_info['email'] = email_match.group(0)
    
    # Extract phone
    phone_match = re.search(PHONE_PATTERN, text)
    if phone_match:
        contact_info['phone'] = phone_match.group(0)
    
    # Extract LinkedIn
    linkedin_match = re.search(LINKEDIN_PATTERN, text)
    if linkedin_match:
        contact_info['linkedin'] = linkedin_match.group(0)
    
    return contact_info

def extract_name(text: str) -> str:
    """
    Extract the name from the CV text.
    This is a simplified implementation and would need more sophisticated
    techniques like Named Entity Recognition in a real application.
    
    Args:
        text: Preprocessed CV text
        
    Returns:
        Extracted name or empty string if not found
    """
    # For demonstration, we'll use a simple heuristic:
    # The name is often at the beginning of the CV
    lines = text.split('\n')
    if lines:
        # Take the first non-empty line that's not too long
        for line in lines:
            line = line.strip()
            if line and len(line.split()) <= 5:
                return line
    
    return ""

def extract_skills(text: str) -> List[Dict[str, Any]]:
    """
    Extract skills from the CV text with confidence scores.
    
    Args:
        text: Preprocessed CV text
        
    Returns:
        List of dictionaries containing skill name, category, and confidence score
    """
    skills = []
    text_lower = text.lower()
    words = word_tokenize(text_lower)
    
    # Remove stop words and lemmatize
    filtered_words = [lemmatizer.lemmatize(word) for word in words if word not in stop_words]
    
    # Check for skills in the text
    found_skills = set()
    for skill, category in ALL_SKILLS.items():
        # Check for exact matches
        if skill in text_lower:
            confidence = 1.0
            found_skills.add(skill)
            skills.append({
                'name': skill,
                'category': category,
                'confidence': confidence
            })
        # Check for partial matches (for multi-word skills)
        elif ' ' in skill and all(word in filtered_words for word in skill.split()):
            confidence = 0.8
            found_skills.add(skill)
            skills.append({
                'name': skill,
                'category': category,
                'confidence': confidence
            })
    
    # Sort skills by confidence
    skills.sort(key=lambda x: x['confidence'], reverse=True)
    
    return skills

def extract_education(text: str) -> List[Dict[str, Any]]:
    """
    Extract education information from the CV text.
    
    Args:
        text: Preprocessed CV text
        
    Returns:
        List of dictionaries containing education details
    """
    education = []
    
    # Look for education section
    education_section = None
    sections = re.split(r'\n\s*\n', text)
    for section in sections:
        if re.search(r'(?i)education|academic|degree|university|college|school', section):
            education_section = section
            break
    
    if not education_section:
        education_section = text
    
    # Extract degrees using patterns
    for pattern in EDUCATION_PATTERNS:
        matches = re.finditer(pattern, education_section)
        for match in matches:
            degree_type = match.group(1)
            field = match.group(2) if len(match.groups()) > 1 else ""
            
            education.append({
                'degree': degree_type.strip(),
                'field': field.strip() if field else "",
                'confidence': 0.9
            })
    
    return education

def extract_experience(text: str) -> List[Dict[str, Any]]:
    """
    Extract work experience information from the CV text.
    
    Args:
        text: Preprocessed CV text
        
    Returns:
        List of dictionaries containing work experience details
    """
    experience = []
    
    # Look for experience section
    experience_section = None
    sections = re.split(r'\n\s*\n', text)
    for section in sections:
        if re.search(r'(?i)experience|work|employment|career|job', section):
            experience_section = section
            break
    
    if not experience_section:
        return experience
    
    # Extract job titles and companies
    # This is a simplified implementation
    lines = experience_section.split('\n')
    current_job = {}
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # Check if this line might be a job title
        if re.search(r'(?i)engineer|developer|manager|director|analyst|designer|consultant', line):
            if current_job and 'title' in current_job:
                experience.append(current_job)
                current_job = {}
            
            current_job['title'] = line
            current_job['confidence'] = 0.8
        
        # Check if this line might be a company name
        elif re.search(r'(?i)inc\.|llc|ltd|company|corp|corporation', line):
            if 'title' in current_job:
                current_job['company'] = line
    
    # Add the last job if it exists
    if current_job and 'title' in current_job:
        experience.append(current_job)
    
    return experience

def calculate_match_score(cv_skills: List[Dict[str, Any]], job_skills: List[str]) -> float:
    """
    Calculate a match score between CV skills and job required skills.
    
    Args:
        cv_skills: List of skills extracted from the CV
        job_skills: List of skills required for the job
        
    Returns:
        Match score between 0 and 1
    """
    if not job_skills:
        return 0.0
    
    cv_skill_names = [skill['name'].lower() for skill in cv_skills]
    job_skills_lower = [skill.lower() for skill in job_skills]
    
    matches = 0
    for job_skill in job_skills_lower:
        for cv_skill in cv_skill_names:
            if job_skill == cv_skill or job_skill in cv_skill or cv_skill in job_skill:
                matches += 1
                break
    
    return min(1.0, matches / len(job_skills))

def analyze_cv(file_path: str, job_skills: List[str] = None) -> Dict[str, Any]:
    """
    Analyze a CV and extract all relevant information.
    
    Args:
        file_path: Path to the CV file
        job_skills: Optional list of skills required for a specific job
        
    Returns:
        Dictionary containing all extracted information
    """
    start_time = time.time()
    
    # Extract text from file
    raw_text = extract_text_from_file_real(file_path)

    # Check if text extraction was successful
    if not raw_text or len(raw_text.strip()) < 50:
        logger.error(f"Failed to extract meaningful text from file: {file_path}")
        return {
            'error': 'Failed to extract text from file',
            'file_path': file_path,
            'processing_time': time.time() - start_time
        }
    
    # Preprocess text
    processed_text = preprocess_text(raw_text)
    
    # Extract information
    contact_info = extract_contact_info(processed_text)
    name = extract_name(processed_text)
    skills = extract_skills(processed_text)
    education = extract_education(processed_text)
    experience = extract_experience(processed_text)
    
    # Calculate match score if job skills are provided
    match_score = 0.0
    if job_skills:
        match_score = calculate_match_score(skills, job_skills)
    
    # Calculate overall confidence score
    confidence_scores = [skill['confidence'] for skill in skills]
    confidence_scores.extend([edu['confidence'] for edu in education])
    confidence_scores.extend([exp['confidence'] for exp in experience if 'confidence' in exp])
    
    overall_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
    
    # Prepare result
    result = {
        'name': name,
        'contact_info': contact_info,
        'skills': skills,
        'education': education,
        'experience': experience,
        'raw_text': raw_text,
        'analysis_version': '1.0.0',
        'analysis_date': datetime.utcnow().isoformat(),
        'analysis_duration': time.time() - start_time,
        'confidence_score': overall_confidence,
        'match_score': match_score
    }
    
    return result


def extract_text_from_pdf(file_path: str) -> str:
    """
    Extract text from a PDF file.

    Args:
        file_path: Path to the PDF file

    Returns:
        Extracted text content
    """
    text = ""

    if not PDF_AVAILABLE:
        logger.error("PDF parsing libraries not available")
        return text

    try:
        # Try with pdfplumber first (better for complex layouts)
        import pdfplumber
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"

        if text.strip():
            logger.info(f"Successfully extracted text from PDF using pdfplumber: {len(text)} characters")
            return text

    except Exception as e:
        logger.warning(f"pdfplumber failed: {str(e)}, trying PyPDF2")

    try:
        # Fallback to PyPDF2
        import PyPDF2
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"

        logger.info(f"Successfully extracted text from PDF using PyPDF2: {len(text)} characters")

    except Exception as e:
        logger.error(f"Error extracting text from PDF: {str(e)}")

    return text


def extract_text_from_docx(file_path: str) -> str:
    """
    Extract text from a DOCX file.

    Args:
        file_path: Path to the DOCX file

    Returns:
        Extracted text content
    """
    text = ""

    if not DOCX_AVAILABLE:
        logger.error("DOCX parsing library not available")
        return text

    try:
        import docx
        doc = docx.Document(file_path)

        # Extract text from paragraphs
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"

        # Extract text from tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    text += cell.text + " "
                text += "\n"

        logger.info(f"Successfully extracted text from DOCX: {len(text)} characters")

    except Exception as e:
        logger.error(f"Error extracting text from DOCX: {str(e)}")

    return text


def extract_text_from_file_real(file_path: str) -> str:
    """
    Extract text from a file based on its extension.

    Args:
        file_path: Path to the file

    Returns:
        Extracted text content
    """
    if not os.path.exists(file_path):
        logger.error(f"File not found: {file_path}")
        return ""

    file_extension = os.path.splitext(file_path)[1].lower()

    if file_extension == '.pdf':
        return extract_text_from_pdf(file_path)
    elif file_extension in ['.docx', '.doc']:
        return extract_text_from_docx(file_path)
    elif file_extension == '.txt':
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except Exception as e:
            logger.error(f"Error reading text file: {str(e)}")
            return ""
    else:
        logger.error(f"Unsupported file format: {file_extension}")
        return ""
