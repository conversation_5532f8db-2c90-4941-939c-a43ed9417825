{% extends "base.html" %}

{% block title %}Admin Dashboard | Applicant Tracking System{% endblock %}

{% block header_title %}Admin Dashboard{% endblock %}
{% block header_subtitle %}Manage the Applicant Tracking System{% endblock %}

{% block description %}Admin dashboard for managing applications, users, and job listings.{% endblock %}

{% block content %}
    <div class="admin-dashboard fade-in">
        <div class="admin-sidebar slide-in-left">
            <div class="admin-profile">
                <div class="admin-avatar">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="admin-info">
                    <h3>{{ current_user.first_name }} {{ current_user.last_name }}</h3>
                    <p>Administrator</p>
                </div>
            </div>

            <nav class="admin-nav">
                <a href="#dashboard" class="admin-nav-item active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="#applications" class="admin-nav-item" data-section="applications">
                    <i class="fas fa-file-alt"></i> Applications
                </a>
                <a href="#users" class="admin-nav-item" data-section="users">
                    <i class="fas fa-users"></i> Users
                </a>
                <a href="#jobs" class="admin-nav-item" data-section="jobs">
                    <i class="fas fa-briefcase"></i> Job Listings
                </a>
                <a href="#settings" class="admin-nav-item" data-section="settings">
                    <i class="fas fa-cog"></i> Settings
                </a>
                <a href="{{ url_for('logout') }}" class="admin-nav-item logout">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </nav>
        </div>

        <div class="admin-content slide-in-right">
            <!-- Dashboard Section -->
            <section id="dashboard" class="admin-section active">
                <h2><i class="fas fa-tachometer-alt"></i> Dashboard Overview</h2>

                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3>{{ stats.applications }}</h3>
                            <p>Total Applications</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>{{ stats.users }}</h3>
                            <p>Registered Users</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="stat-info">
                            <h3>{{ stats.jobs }}</h3>
                            <p>Active Jobs</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3>{{ stats.conversion_rate }}%</h3>
                            <p>Conversion Rate</p>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-card">
                        <h3>Applications Over Time</h3>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-bar"></i>
                            <p>Chart will be displayed here</p>
                        </div>
                    </div>

                    <div class="chart-card">
                        <h3>Top Skills in Demand</h3>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-pie"></i>
                            <p>Chart will be displayed here</p>
                        </div>
                    </div>
                </div>

                <div class="recent-activity">
                    <h3>Recent Activity</h3>
                    <div class="activity-list">
                        {% for activity in recent_activities %}
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="{{ activity.icon }}"></i>
                            </div>
                            <div class="activity-details">
                                <p class="activity-text">{{ activity.text }}</p>
                                <p class="activity-time">{{ activity.time }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </section>

            <!-- Applications Section -->
            <section id="applications" class="admin-section">
                <h2><i class="fas fa-file-alt"></i> Applications Management</h2>

                <div class="filter-controls">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search applications...">
                    </div>

                    <div class="filter-dropdown">
                        <select>
                            <option value="all">All Applications</option>
                            <option value="pending">Pending</option>
                            <option value="reviewed">Reviewed</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                        </select>
                    </div>
                </div>

                <div class="data-table">
                    <table id="applicationsTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Applicant</th>
                                <th>Job Position</th>
                                <th>Date Applied</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Applications will be loaded dynamically -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </section>

            <!-- Users Section -->
            <section id="users" class="admin-section">
                <h2><i class="fas fa-users"></i> User Management</h2>

                <div class="filter-controls">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search users...">
                    </div>

                    <div class="filter-dropdown">
                        <select>
                            <option value="all">All Users</option>
                            <option value="admin">Admins</option>
                            <option value="user">Regular Users</option>
                        </select>
                    </div>

                    <button class="add-btn" onclick="openUserModal()">
                        <i class="fas fa-plus"></i> Add User
                    </button>
                </div>

                <div class="data-table">
                    <table id="usersTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Username</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Joined Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Users will be loaded dynamically -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </section>

            <!-- Jobs Section -->
            <section id="jobs" class="admin-section">
                <h2><i class="fas fa-briefcase"></i> Job Listings Management</h2>

                <div class="filter-controls">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search jobs..." id="jobSearch">
                    </div>

                    <div class="filter-dropdown">
                        <select id="jobFilter">
                            <option value="all">All Jobs</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>

                    <button class="add-btn" onclick="openJobModal()">
                        <i class="fas fa-plus"></i> Add Job
                    </button>
                </div>

                <div class="data-table">
                    <table id="jobsTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Job Title</th>
                                <th>Company</th>
                                <th>Location</th>
                                <th>Type</th>
                                <th>Posted Date</th>
                                <th>Status</th>
                                <th>Applications</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Jobs will be loaded dynamically -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="admin-section">
                <h2><i class="fas fa-cog"></i> System Settings</h2>

                <div class="settings-container">
                    <div class="settings-card">
                        <h3>General Settings</h3>
                        <div class="setting-item">
                            <label>Site Name</label>
                            <input type="text" value="Applicant Tracking System">
                        </div>
                        <div class="setting-item">
                            <label>Max File Size (MB)</label>
                            <input type="number" value="16">
                        </div>
                        <div class="setting-item">
                            <label>Allowed File Types</label>
                            <input type="text" value="pdf, docx">
                        </div>
                    </div>

                    <div class="settings-card">
                        <h3>Email Settings</h3>
                        <div class="setting-item">
                            <label>SMTP Server</label>
                            <input type="text" placeholder="smtp.example.com">
                        </div>
                        <div class="setting-item">
                            <label>SMTP Port</label>
                            <input type="number" placeholder="587">
                        </div>
                        <div class="setting-item">
                            <label>Email Username</label>
                            <input type="email" placeholder="<EMAIL>">
                        </div>
                    </div>
                </div>

                <button class="save-settings-btn" onclick="saveSettings()">
                    <i class="fas fa-save"></i> Save Settings
                </button>
            </section>
        </div>
    </div>

    <!-- Job Modal -->
    <div id="jobModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Add New Job</h3>
                <span class="close" onclick="closeJobModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="jobForm">
                    <input type="hidden" id="jobId" name="job_id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="jobTitle">Job Title *</label>
                            <input type="text" id="jobTitle" name="title" required>
                        </div>
                        <div class="form-group">
                            <label for="company">Company *</label>
                            <input type="text" id="company" name="company" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="location">Location *</label>
                            <input type="text" id="location" name="location" required>
                        </div>
                        <div class="form-group">
                            <label for="jobType">Job Type</label>
                            <select id="jobType" name="job_type">
                                <option value="Full-time">Full-time</option>
                                <option value="Part-time">Part-time</option>
                                <option value="Contract">Contract</option>
                                <option value="Internship">Internship</option>
                                <option value="Remote">Remote</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="experienceLevel">Experience Level</label>
                            <select id="experienceLevel" name="experience_level">
                                <option value="Entry">Entry Level</option>
                                <option value="Mid">Mid Level</option>
                                <option value="Senior">Senior Level</option>
                                <option value="Executive">Executive</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="isActive">Status</label>
                            <select id="isActive" name="is_active">
                                <option value="true">Active</option>
                                <option value="false">Inactive</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="salaryMin">Minimum Salary</label>
                            <input type="number" id="salaryMin" name="salary_min" placeholder="50000">
                        </div>
                        <div class="form-group">
                            <label for="salaryMax">Maximum Salary</label>
                            <input type="number" id="salaryMax" name="salary_max" placeholder="80000">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="closingDate">Closing Date</label>
                        <input type="date" id="closingDate" name="closing_date">
                    </div>

                    <div class="form-group">
                        <label for="description">Job Description *</label>
                        <textarea id="description" name="description" rows="4" required placeholder="Enter detailed job description..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="requirements">Requirements</label>
                        <textarea id="requirements" name="requirements" rows="4" placeholder="Enter job requirements..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="skills">Required Skills (comma-separated)</label>
                        <input type="text" id="skills" name="skills" placeholder="Python, JavaScript, React, SQL">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeJobModal()">Cancel</button>
                <button type="button" class="btn-primary" onclick="saveJob()">Save Job</button>
            </div>
        </div>
    </div>
        </div>
    </div>

    <!-- User Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="userModalTitle">Add New User</h3>
                <span class="close" onclick="closeUserModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <input type="hidden" id="userId" name="user_id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">Username *</label>
                            <input type="text" id="username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">First Name</label>
                            <input type="text" id="firstName" name="first_name">
                        </div>
                        <div class="form-group">
                            <label for="lastName">Last Name</label>
                            <input type="text" id="lastName" name="last_name">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="password">Password *</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        <div class="form-group">
                            <label for="role">Role</label>
                            <select id="role" name="role">
                                <option value="user">User</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeUserModal()">Cancel</button>
                <button type="button" class="btn-primary" onclick="saveUser()">Save User</button>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Navigation functionality
        const navItems = document.querySelectorAll('.admin-nav-item');
        const sections = document.querySelectorAll('.admin-section');

        navItems.forEach(item => {
            if (item.classList.contains('logout')) return;

            item.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all items and sections
                navItems.forEach(nav => nav.classList.remove('active'));
                sections.forEach(section => section.classList.remove('active'));

                // Add active class to clicked item
                this.classList.add('active');

                // Show corresponding section
                const sectionId = this.getAttribute('data-section');
                const section = document.getElementById(sectionId);
                if (section) {
                    section.classList.add('active');

                    // Load data when sections are activated
                    if (sectionId === 'jobs') {
                        loadJobs();
                    } else if (sectionId === 'users') {
                        loadUsers();
                    } else if (sectionId === 'applications') {
                        loadApplications();
                    } else if (sectionId === 'settings') {
                        loadSettings();
                    }
                }
            });
        });

        // Load jobs on page load if jobs section is active
        if (document.getElementById('jobs').classList.contains('active')) {
            loadJobs();
        }
    });

    // Job Management Functions
    function loadJobs() {
        fetch('/api/admin/jobs')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    displayJobs(data.jobs);
                } else {
                    console.error('Error loading jobs:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading jobs:', error);
                // Display sample data if API fails
                displaySampleJobs();
            });
    }

    function displayJobs(jobs) {
        const tbody = document.querySelector('#jobsTable tbody');
        tbody.innerHTML = '';

        jobs.forEach(job => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${job.id}</td>
                <td>${job.title}</td>
                <td>${job.company}</td>
                <td>${job.location}</td>
                <td>${job.job_type || 'N/A'}</td>
                <td>${formatDate(job.posted_date)}</td>
                <td><span class="status-badge ${job.is_active ? 'approved' : 'rejected'}">${job.is_active ? 'Active' : 'Inactive'}</span></td>
                <td>${job.application_count || 0}</td>
                <td class="actions">
                    <button class="action-btn view" title="View Details" onclick="viewJob(${job.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" title="Edit" onclick="editJob(${job.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" title="Delete" onclick="deleteJob(${job.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    function displaySampleJobs() {
        const sampleJobs = [
            {
                id: 1,
                title: 'Senior Python Developer',
                company: 'Tech Innovations Inc.',
                location: 'New York, NY',
                job_type: 'Full-time',
                posted_date: '2023-11-15',
                is_active: true,
                application_count: 12
            },
            {
                id: 2,
                title: 'Data Scientist',
                company: 'Data Analytics Co.',
                location: 'San Francisco, CA',
                job_type: 'Full-time',
                posted_date: '2023-11-10',
                is_active: true,
                application_count: 8
            },
            {
                id: 3,
                title: 'Frontend Developer',
                company: 'Web Solutions',
                location: 'Remote',
                job_type: 'Contract',
                posted_date: '2023-11-05',
                is_active: false,
                application_count: 5
            }
        ];
        displayJobs(sampleJobs);
    }

    function openJobModal(jobId = null) {
        const modal = document.getElementById('jobModal');
        const modalTitle = document.getElementById('modalTitle');
        const form = document.getElementById('jobForm');

        if (jobId) {
            modalTitle.textContent = 'Edit Job';
            loadJobData(jobId);
        } else {
            modalTitle.textContent = 'Add New Job';
            form.reset();
            document.getElementById('jobId').value = '';
        }

        modal.style.display = 'block';
    }

    function closeJobModal() {
        document.getElementById('jobModal').style.display = 'none';
    }

    function loadJobData(jobId) {
        fetch(`/api/admin/jobs/${jobId}`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    const job = data.job;
                    document.getElementById('jobId').value = job.id;
                    document.getElementById('jobTitle').value = job.title;
                    document.getElementById('company').value = job.company;
                    document.getElementById('location').value = job.location;
                    document.getElementById('jobType').value = job.job_type || '';
                    document.getElementById('experienceLevel').value = job.experience_level || '';
                    document.getElementById('isActive').value = job.is_active.toString();
                    document.getElementById('salaryMin').value = job.salary_min || '';
                    document.getElementById('salaryMax').value = job.salary_max || '';
                    document.getElementById('closingDate').value = job.closing_date || '';
                    document.getElementById('description').value = job.description;
                    document.getElementById('requirements').value = job.requirements || '';
                    document.getElementById('skills').value = job.skills ? job.skills.map(s => s.name).join(', ') : '';
                }
            })
            .catch(error => {
                console.error('Error loading job data:', error);
                alert('Error loading job data');
            });
    }

    function saveJob() {
        const form = document.getElementById('jobForm');
        const formData = new FormData(form);
        const jobId = document.getElementById('jobId').value;

        const url = jobId ? `/api/admin/jobs/${jobId}` : '/api/admin/jobs';
        const method = jobId ? 'PUT' : 'POST';

        fetch(url, {
            method: method,
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                closeJobModal();
                loadJobs();
                alert(jobId ? 'Job updated successfully!' : 'Job created successfully!');
            } else {
                alert('Error saving job: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error saving job:', error);
            alert('Error saving job');
        });
    }

    function editJob(jobId) {
        openJobModal(jobId);
    }

    function viewJob(jobId) {
        window.open(`/job-details/${jobId}`, '_blank');
    }

    function deleteJob(jobId) {
        if (confirm('Are you sure you want to delete this job? This action cannot be undone.')) {
            fetch(`/api/admin/jobs/${jobId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    loadJobs();
                    alert('Job deleted successfully!');
                } else {
                    alert('Error deleting job: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error deleting job:', error);
                alert('Error deleting job');
            });
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    // User Management Functions
    function loadUsers() {
        fetch('/api/admin/users')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    displayUsers(data.users);
                } else {
                    console.error('Error loading users:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading users:', error);
            });
    }

    function displayUsers(users) {
        const tbody = document.querySelector('#usersTable tbody');
        tbody.innerHTML = '';

        users.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.first_name || ''} ${user.last_name || ''}</td>
                <td>${user.email}</td>
                <td><span class="role-badge ${user.role}">${user.role}</span></td>
                <td>${formatDate(user.created_at)}</td>
                <td class="actions">
                    <button class="action-btn view" title="View Details" onclick="viewUser(${user.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" title="Edit" onclick="editUser(${user.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" title="Delete" onclick="deleteUser(${user.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    function openUserModal(userId = null) {
        const modal = document.getElementById('userModal');
        const modalTitle = document.getElementById('userModalTitle');
        const form = document.getElementById('userForm');

        if (userId) {
            modalTitle.textContent = 'Edit User';
            loadUserData(userId);
        } else {
            modalTitle.textContent = 'Add New User';
            form.reset();
            document.getElementById('userId').value = '';
            document.getElementById('password').required = true;
        }

        modal.style.display = 'block';
    }

    function closeUserModal() {
        document.getElementById('userModal').style.display = 'none';
    }

    function loadUserData(userId) {
        fetch(`/api/admin/users/${userId}`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    const user = data.user;
                    document.getElementById('userId').value = user.id;
                    document.getElementById('username').value = user.username;
                    document.getElementById('email').value = user.email;
                    document.getElementById('firstName').value = user.first_name || '';
                    document.getElementById('lastName').value = user.last_name || '';
                    document.getElementById('role').value = user.role;
                    document.getElementById('password').required = false;
                    document.getElementById('password').placeholder = 'Leave blank to keep current password';
                }
            })
            .catch(error => {
                console.error('Error loading user data:', error);
                alert('Error loading user data');
            });
    }

    function saveUser() {
        const form = document.getElementById('userForm');
        const formData = new FormData(form);
        const userId = document.getElementById('userId').value;

        const url = userId ? `/api/admin/users/${userId}` : '/api/admin/users';
        const method = userId ? 'PUT' : 'POST';

        fetch(url, {
            method: method,
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                closeUserModal();
                loadUsers();
                alert(userId ? 'User updated successfully!' : 'User created successfully!');
            } else {
                alert('Error saving user: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error saving user:', error);
            alert('Error saving user');
        });
    }

    function editUser(userId) {
        openUserModal(userId);
    }

    function viewUser(userId) {
        // For now, just edit the user
        editUser(userId);
    }

    function deleteUser(userId) {
        if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            fetch(`/api/admin/users/${userId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    loadUsers();
                    alert('User deleted successfully!');
                } else {
                    alert('Error deleting user: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error deleting user:', error);
                alert('Error deleting user');
            });
        }
    }

    // Application Management Functions
    function loadApplications() {
        fetch('/api/admin/applications')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    displayApplications(data.applications);
                } else {
                    console.error('Error loading applications:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading applications:', error);
            });
    }

    function displayApplications(applications) {
        const tbody = document.querySelector('#applicationsTable tbody');
        tbody.innerHTML = '';

        applications.forEach(app => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${app.id}</td>
                <td>${app.applicant_name}</td>
                <td>${app.job_title}</td>
                <td>${formatDate(app.application_date)}</td>
                <td><span class="status-badge ${app.status.toLowerCase()}">${app.status}</span></td>
                <td class="actions">
                    <button class="action-btn view" title="View Details" onclick="viewApplication(${app.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" title="Update Status" onclick="updateApplicationStatus(${app.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" title="Delete" onclick="deleteApplication(${app.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    function viewApplication(applicationId) {
        // For now, just show an alert
        alert('Application details view not implemented yet');
    }

    function updateApplicationStatus(applicationId) {
        const newStatus = prompt('Enter new status (Applied, Screening, Interview, Offer, Rejected):');
        if (newStatus) {
            const formData = new FormData();
            formData.append('status', newStatus);

            fetch(`/api/admin/applications/${applicationId}`, {
                method: 'PUT',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    loadApplications();
                    alert('Application status updated successfully!');
                } else {
                    alert('Error updating application: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error updating application:', error);
                alert('Error updating application');
            });
        }
    }

    function deleteApplication(applicationId) {
        if (confirm('Are you sure you want to delete this application? This action cannot be undone.')) {
            fetch(`/api/admin/applications/${applicationId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    loadApplications();
                    alert('Application deleted successfully!');
                } else {
                    alert('Error deleting application: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error deleting application:', error);
                alert('Error deleting application');
            });
        }
    }

    // Settings Management Functions
    function loadSettings() {
        fetch('/api/admin/settings')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    populateSettings(data.settings);
                } else {
                    console.error('Error loading settings:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading settings:', error);
            });
    }

    function populateSettings(settings) {
        // Populate general settings
        const siteNameInput = document.querySelector('input[value="Applicant Tracking System"]');
        if (siteNameInput) siteNameInput.value = settings.site_name || 'Applicant Tracking System';

        const maxFileSizeInput = document.querySelector('input[value="16"]');
        if (maxFileSizeInput) maxFileSizeInput.value = settings.max_file_size || '16';

        const allowedTypesInput = document.querySelector('input[value="pdf, docx"]');
        if (allowedTypesInput) allowedTypesInput.value = settings.allowed_file_types || 'pdf, docx';

        // Populate email settings
        const smtpServerInput = document.querySelector('input[placeholder="smtp.example.com"]');
        if (smtpServerInput) smtpServerInput.value = settings.smtp_server || '';

        const smtpPortInput = document.querySelector('input[placeholder="587"]');
        if (smtpPortInput) smtpPortInput.value = settings.smtp_port || '587';

        const emailUsernameInput = document.querySelector('input[placeholder="<EMAIL>"]');
        if (emailUsernameInput) emailUsernameInput.value = settings.email_username || '';
    }

    function saveSettings() {
        const formData = new FormData();

        // Get general settings
        const siteNameInput = document.querySelector('input[value="Applicant Tracking System"]');
        if (siteNameInput) formData.append('site_name', siteNameInput.value);

        const maxFileSizeInput = document.querySelector('input[value="16"]');
        if (maxFileSizeInput) formData.append('max_file_size', maxFileSizeInput.value);

        const allowedTypesInput = document.querySelector('input[value="pdf, docx"]');
        if (allowedTypesInput) formData.append('allowed_file_types', allowedTypesInput.value);

        // Get email settings
        const smtpServerInput = document.querySelector('input[placeholder="smtp.example.com"]');
        if (smtpServerInput) formData.append('smtp_server', smtpServerInput.value);

        const smtpPortInput = document.querySelector('input[placeholder="587"]');
        if (smtpPortInput) formData.append('smtp_port', smtpPortInput.value);

        const emailUsernameInput = document.querySelector('input[placeholder="<EMAIL>"]');
        if (emailUsernameInput) formData.append('email_username', emailUsernameInput.value);

        fetch('/api/admin/settings', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('Settings saved successfully!');
            } else {
                alert('Error saving settings: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error saving settings:', error);
            alert('Error saving settings');
        });
    }

    // Close modal when clicking outside
    window.onclick = function(event) {
        const jobModal = document.getElementById('jobModal');
        const userModal = document.getElementById('userModal');
        if (event.target === jobModal) {
            closeJobModal();
        } else if (event.target === userModal) {
            closeUserModal();
        }
    }
</script>
{% endblock %}

{% block extra_head %}
<style>
    .admin-dashboard {
        display: grid;
        grid-template-columns: 250px 1fr;
        gap: 30px;
        min-height: calc(100vh - 200px);
    }

    .admin-sidebar {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 20px 0;
    }

    .admin-profile {
        display: flex;
        align-items: center;
        padding: 0 20px 20px;
        border-bottom: 1px solid #eee;
        margin-bottom: 20px;
    }

    .admin-avatar {
        width: 50px;
        height: 50px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-right: 15px;
    }

    .admin-info h3 {
        margin: 0;
        font-size: 1.1rem;
        color: var(--secondary-color);
    }

    .admin-info p {
        margin: 5px 0 0;
        font-size: 0.9rem;
        color: #666;
    }

    .admin-nav {
        display: flex;
        flex-direction: column;
    }

    .admin-nav-item {
        padding: 12px 20px;
        color: #333;
        text-decoration: none;
        display: flex;
        align-items: center;
        transition: var(--transition);
    }

    .admin-nav-item i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    .admin-nav-item:hover {
        background-color: rgba(52, 152, 219, 0.1);
        color: var(--primary-color);
    }

    .admin-nav-item.active {
        background-color: var(--primary-color);
        color: white;
    }

    .admin-nav-item.logout {
        margin-top: auto;
        border-top: 1px solid #eee;
        color: #e74c3c;
    }

    .admin-content {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 30px;
    }

    .admin-section {
        display: none;
    }

    .admin-section.active {
        display: block;
        animation: fadeIn 0.5s ease;
    }

    .admin-section h2 {
        color: var(--secondary-color);
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
        display: flex;
        align-items: center;
        transition: var(--transition);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-right: 15px;
    }

    .stat-info h3 {
        margin: 0;
        font-size: 1.5rem;
        color: var(--secondary-color);
    }

    .stat-info p {
        margin: 5px 0 0;
        font-size: 0.9rem;
        color: #666;
    }

    .chart-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .chart-card {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
    }

    .chart-card h3 {
        margin-top: 0;
        margin-bottom: 20px;
        color: var(--secondary-color);
        font-size: 1.2rem;
    }

    .chart-placeholder {
        height: 200px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #aaa;
    }

    .chart-placeholder i {
        font-size: 3rem;
        margin-bottom: 10px;
    }

    .recent-activity h3 {
        margin-top: 0;
        margin-bottom: 20px;
        color: var(--secondary-color);
        font-size: 1.2rem;
    }

    .activity-list {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
    }

    .activity-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #eee;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        background-color: rgba(52, 152, 219, 0.1);
        color: var(--primary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        margin-right: 15px;
    }

    .activity-details {
        flex: 1;
    }

    .activity-text {
        margin: 0;
        color: #333;
    }

    .activity-time {
        margin: 5px 0 0;
        font-size: 0.8rem;
        color: #999;
    }

    .filter-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .search-box {
        position: relative;
        flex: 1;
        max-width: 300px;
    }

    .search-box i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #aaa;
    }

    .search-box input {
        width: 100%;
        padding: 10px 15px 10px 40px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-size: 0.9rem;
    }

    .filter-dropdown select {
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-size: 0.9rem;
        background-color: white;
    }

    .add-btn {
        padding: 10px 15px;
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-size: 0.9rem;
        cursor: pointer;
        transition: var(--transition);
    }

    .add-btn:hover {
        background-color: var(--secondary-color);
    }

    .data-table {
        overflow-x: auto;
        margin-bottom: 20px;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    th, td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    th {
        background-color: #f8f9fa;
        color: var(--secondary-color);
        font-weight: 600;
    }

    tr:hover {
        background-color: rgba(52, 152, 219, 0.05);
    }

    .status-badge, .role-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-badge.pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-badge.reviewed {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    .status-badge.approved {
        background-color: #d4edda;
        color: #155724;
    }

    .status-badge.rejected {
        background-color: #f8d7da;
        color: #721c24;
    }

    .role-badge.admin {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    .role-badge.user {
        background-color: #d4edda;
        color: #155724;
    }

    .actions {
        display: flex;
        gap: 5px;
    }

    .action-btn {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition);
    }

    .action-btn.view {
        background-color: rgba(52, 152, 219, 0.1);
        color: var(--primary-color);
    }

    .action-btn.edit {
        background-color: rgba(39, 174, 96, 0.1);
        color: #27ae60;
    }

    .action-btn.delete {
        background-color: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }

    .action-btn:hover {
        transform: scale(1.1);
    }

    .pagination {
        display: flex;
        justify-content: center;
        gap: 5px;
    }

    .pagination-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 1px solid #ddd;
        background-color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition);
    }

    .pagination-btn.active {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .pagination-btn:hover:not(.active):not([disabled]) {
        background-color: #f8f9fa;
    }

    .pagination-btn[disabled] {
        opacity: 0.5;
        cursor: not-allowed;
    }

    @media (max-width: 992px) {
        .admin-dashboard {
            grid-template-columns: 1fr;
        }

        .admin-sidebar {
            display: none;
        }
    }

    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        animation: fadeIn 0.3s ease;
    }

    .modal-content {
        background-color: white;
        margin: 2% auto;
        padding: 0;
        border-radius: var(--border-radius);
        width: 90%;
        max-width: 800px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        animation: slideIn 0.3s ease;
    }

    .modal-header {
        padding: 20px 30px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #f8f9fa;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    .modal-header h3 {
        margin: 0;
        color: var(--secondary-color);
        font-size: 1.3rem;
    }

    .close {
        color: #aaa;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        transition: var(--transition);
    }

    .close:hover {
        color: #e74c3c;
    }

    .modal-body {
        padding: 30px;
    }

    .modal-footer {
        padding: 20px 30px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        background-color: #f8f9fa;
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-group label {
        margin-bottom: 8px;
        font-weight: 600;
        color: var(--secondary-color);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-size: 0.9rem;
        transition: var(--transition);
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .form-group textarea {
        resize: vertical;
        min-height: 100px;
    }

    .btn-primary {
        background-color: var(--primary-color);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: var(--border-radius);
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        transition: var(--transition);
    }

    .btn-primary:hover {
        background-color: var(--secondary-color);
        transform: translateY(-2px);
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: var(--border-radius);
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        transition: var(--transition);
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        transform: translateY(-2px);
    }

    /* Settings Styles */
    .settings-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .settings-card {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
    }

    .settings-card h3 {
        margin-top: 0;
        margin-bottom: 20px;
        color: var(--secondary-color);
        font-size: 1.2rem;
    }

    .setting-item {
        margin-bottom: 15px;
    }

    .setting-item label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: var(--secondary-color);
    }

    .setting-item input {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-size: 0.9rem;
    }

    .save-settings-btn {
        background-color: var(--primary-color);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: var(--border-radius);
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .save-settings-btn:hover {
        background-color: var(--secondary-color);
        transform: translateY(-2px);
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }

        .modal-content {
            width: 95%;
            margin: 5% auto;
        }

        .settings-container {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}
