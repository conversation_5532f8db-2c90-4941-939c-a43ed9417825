{% extends "base.html" %}

{% block title %}Register | Applicant Tracking System{% endblock %}

{% block header_title %}Register{% endblock %}
{% block header_subtitle %}Create a new account{% endblock %}

{% block description %}Register to create an account and start applying for jobs.{% endblock %}

{% block content %}
    <div class="auth-container fade-in">
        <div class="auth-card slide-in-bottom">
            <h2><i class="fas fa-user-plus"></i> Create an Account</h2>
            <p>Register to create your applicant profile and start applying for jobs.</p>
            
            <form id="register-form" action="{{ url_for('register') }}" method="POST" class="auth-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="first-name">First Name</label>
                        <div class="input-with-icon">
                            <i class="fas fa-user"></i>
                            <input type="text" id="first-name" name="first_name" placeholder="Enter your first name" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="last-name">Last Name</label>
                        <div class="input-with-icon">
                            <i class="fas fa-user"></i>
                            <input type="text" id="last-name" name="last_name" placeholder="Enter your last name" required>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="username">Username</label>
                    <div class="input-with-icon">
                        <i class="fas fa-user-circle"></i>
                        <input type="text" id="username" name="username" placeholder="Choose a username" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <div class="input-with-icon">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="email" name="email" placeholder="Enter your email address" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="input-with-icon">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="password" name="password" placeholder="Create a password" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm-password">Confirm Password</label>
                        <div class="input-with-icon">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="confirm-password" name="confirm_password" placeholder="Confirm your password" required>
                        </div>
                    </div>
                </div>
                
                <div class="form-group terms-checkbox">
                    <input type="checkbox" id="terms" name="terms" required>
                    <label for="terms">I agree to the <a href="{{ url_for('terms') }}">Terms of Service</a> and <a href="{{ url_for('privacy') }}">Privacy Policy</a></label>
                </div>
                
                <button type="submit" class="auth-button pulse">
                    <i class="fas fa-user-plus"></i> Create Account
                </button>
            </form>
            
            <div class="auth-links">
                <a href="{{ url_for('login') }}" class="login-link">Already have an account? Login</a>
            </div>
            
            {% if error %}
            <div class="alert alert-error shake">
                <i class="fas fa-exclamation-circle"></i> {{ error }}
            </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_head %}
<style>
    .auth-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px 20px;
    }
    
    .auth-card {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        width: 100%;
        max-width: 600px;
        padding: 30px;
    }
    
    .auth-card h2 {
        color: var(--secondary-color);
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .auth-card p {
        color: #666;
        margin-bottom: 25px;
    }
    
    .auth-form {
        margin-top: 20px;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }
    
    .input-with-icon {
        position: relative;
    }
    
    .input-with-icon i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #aaa;
    }
    
    .input-with-icon input {
        width: 100%;
        padding: 12px 15px 12px 45px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-size: 1rem;
        transition: var(--transition);
    }
    
    .input-with-icon input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        outline: none;
    }
    
    .terms-checkbox {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .terms-checkbox label {
        margin-bottom: 0;
        font-weight: normal;
    }
    
    .terms-checkbox a {
        color: var(--primary-color);
        text-decoration: none;
        transition: var(--transition);
    }
    
    .terms-checkbox a:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }
    
    .auth-button {
        display: block;
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        margin-top: 30px;
    }
    
    .auth-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .auth-links {
        margin-top: 25px;
        text-align: center;
        font-size: 0.9rem;
    }
    
    .auth-links a {
        color: var(--primary-color);
        text-decoration: none;
        transition: var(--transition);
    }
    
    .auth-links a:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }
    
    .alert {
        margin-top: 20px;
        padding: 15px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .alert-error {
        background-color: #ffebee;
        color: #c62828;
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
            gap: 0;
        }
    }
</style>
{% endblock %}
