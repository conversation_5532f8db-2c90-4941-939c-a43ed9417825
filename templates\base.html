<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Resume Scanner | Optimize Your Resume{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <meta name="description" content="{% block description %}Optimize your resume for job applications with our resume scanner. Get a match score and personalized suggestions to improve your chances of landing interviews.{% endblock %}">
    {% block extra_head %}{% endblock %}
</head>
<body>
    <div class="container fade-in">
        <div class="header slide-in-down">
            <div class="header-content">
                <div class="header-text">
                    <h1 class="logo">{% block header_title %}Resume Scanner{% endblock %}</h1>
                    <h2>{% block header_subtitle %}Optimize Your Resume for Job Success{% endblock %}</h2>
                </div>
                <div class="user-nav">
                    {% if session.user_id %}
                        <div class="user-info">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="user-details">
                                <span class="username">{{ current_user.first_name if current_user else 'User' }}</span>
                                <span class="user-role">{{ current_user.role.title() if current_user else 'User' }}</span>
                            </div>
                            <div class="user-dropdown">
                                <button class="dropdown-toggle" onclick="toggleUserDropdown()">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                <div class="dropdown-menu" id="userDropdown">
                                    {% if current_user and current_user.is_admin() %}
                                        <a href="{{ url_for('admin_dashboard') }}" class="dropdown-item">
                                            <i class="fas fa-tachometer-alt"></i> Admin Dashboard
                                        </a>
                                    {% else %}
                                        <a href="{{ url_for('user_dashboard') }}" class="dropdown-item">
                                            <i class="fas fa-home"></i> My Dashboard
                                        </a>
                                    {% endif %}
                                    <a href="{{ url_for('logout') }}" class="dropdown-item logout">
                                        <i class="fas fa-sign-out-alt"></i> Logout
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="auth-buttons">
                            <a href="{{ url_for('login') }}" class="auth-btn login-btn">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                            <a href="{{ url_for('register') }}" class="auth-btn register-btn">
                                <i class="fas fa-user-plus"></i> Register
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="nav slide-in-left">
            <ul class="staggered-animation">
                <li><a href="{{ url_for('index') }}" {% if request.path == url_for('index') %}class="active"{% endif %}>Home</a></li>
                {% if session.user_id %}
                    <li><a href="{{ url_for('resume_scanner') }}" {% if request.path == url_for('resume_scanner') %}class="active"{% endif %}>Resume Scanner</a></li>
                    <li><a href="{{ url_for('applicant_portal') }}" {% if request.path == url_for('applicant_portal') %}class="active"{% endif %}>Applicant Portal</a></li>
                    <li><a href="{{ url_for('job_listings') }}" {% if request.path == url_for('job_listings') %}class="active"{% endif %}>Job Listings</a></li>
                    {% if current_user and current_user.is_admin() %}
                        <li><a href="{{ url_for('admin_panel') }}" {% if request.path == url_for('admin_panel') %}class="active"{% endif %}>Admin Panel</a></li>
                    {% endif %}
                {% endif %}
                <li><a href="{{ url_for('about') }}" {% if request.path == url_for('about') %}class="active"{% endif %}>About</a></li>
                <li><a href="{{ url_for('contact') }}" {% if request.path == url_for('contact') %}class="active"{% endif %}>Contact</a></li>
            </ul>
        </div>

        <div class="content slide-in-right">
            {% block content %}{% endblock %}
        </div>
    </div>

    {% if request.path != url_for('index') %}
    <a href="{{ url_for('index') }}" class="home-button bounce-in" title="Back to Home">
        <i class="fas fa-home home-icon"></i>
    </a>
    {% endif %}

    <footer class="slide-in-up">
        <p>&copy; {{ current_year|default(2023) }} Applicant Tracking System. All rights reserved.</p>
    </footer>

    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script>
        // User dropdown functionality
        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdown');
            const toggle = document.querySelector('.dropdown-toggle');

            if (dropdown && !dropdown.contains(event.target) && !toggle.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });
    </script>
    {% block extra_scripts %}{% endblock %}
</body>
</html>
