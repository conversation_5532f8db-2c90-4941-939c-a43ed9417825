/* Modern CSS with variables and advanced styling */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;
    --font-main: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-heading: 'Montserrat', sans-serif;
}

/* Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-main);
    background-color: #f8f9fa;
    color: var(--dark-color);
    line-height: 1.6;
    position: relative;
    min-height: 100vh;
    padding-bottom: 60px; /* Space for footer */
}

.container {
    max-width: 1200px;
    margin: 40px auto;
    padding: 30px;
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: #fff;
    padding: 30px;
    text-align: center;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    margin: -30px -30px 30px -30px;
}

.header h1 {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    margin-bottom: 10px;
    letter-spacing: 1px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.header h2 {
    font-size: 1.5rem;
    font-weight: 300;
    margin-bottom: 20px;
    opacity: 0.9;
}

/* Navigation */
.nav {
    margin-bottom: 30px;
}

.nav ul {
    display: flex;
    list-style: none;
    background-color: var(--secondary-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.nav li {
    flex: 1;
}

.nav a {
    display: block;
    padding: 15px 20px;
    color: #fff;
    text-decoration: none;
    text-align: center;
    transition: var(--transition);
    font-weight: 500;
}

.nav a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* Content Styles */
.content {
    padding: 20px;
}

.content h2 {
    color: var(--secondary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-color);
    font-family: var(--font-heading);
}

.content p {
    margin-bottom: 20px;
    color: #555;
}

/* Form Styles */
form {
    margin: 20px 0;
}

input[type="text"],
input[type="email"],
input[type="file"],
textarea,
select {
    width: 100%;
    padding: 12px 15px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-family: var(--font-main);
    font-size: 1rem;
    transition: var(--transition);
}

input[type="text"]:focus,
input[type="email"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

button[type="submit"],
.button {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
    text-decoration: none;
}

button[type="submit"]:hover,
.button:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    margin: 30px 0;
}

.table {
    width: 100%;
    border-collapse: collapse;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th,
.table td {
    padding: 15px;
    text-align: left;
}

.table th {
    background-color: var(--secondary-color);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

.table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.table tr {
    transition: var(--transition);
}

.table tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

/* Card Styles */
.card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 20px;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: var(--secondary-color);
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Alert Messages */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.alert-success {
    background-color: rgba(46, 204, 113, 0.2);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-error {
    background-color: rgba(231, 76, 60, 0.2);
    color: var(--error-color);
    border-left: 4px solid var(--error-color);
}

.alert-warning {
    background-color: rgba(243, 156, 18, 0.2);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

/* Footer */
footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    background-color: var(--secondary-color);
    color: white;
    text-align: center;
    padding: 15px;
    font-size: 0.9rem;
}

/* Job Listings Styles */
.job-listings {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.job-card {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.job-details {
    flex-grow: 1;
    padding: 15px 0;
}

.job-company, .job-location, .job-date {
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.job-description {
    margin: 15px 0;
    color: #555;
}

.job-actions {
    display: flex;
    gap: 10px;
    margin-top: auto;
}

.button-secondary {
    display: inline-block;
    background-color: white;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    padding: 12px 24px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
    text-decoration: none;
}

.button-secondary:hover {
    background-color: rgba(52, 152, 219, 0.1);
    transform: translateY(-2px);
}

/* Search Section Styles */
.search-section {
    background-color: var(--light-color);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
}

.search-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.search-container input {
    flex-grow: 1;
    margin-bottom: 0;
}

.filter-options {
    display: flex;
    gap: 10px;
}

.filter-options select {
    flex: 1;
}

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.pagination-link {
    display: inline-block;
    padding: 8px 16px;
    margin: 0 5px;
    border-radius: var(--border-radius);
    background-color: white;
    color: var(--primary-color);
    text-decoration: none;
    border: 1px solid #ddd;
    transition: var(--transition);
}

.pagination-link:hover:not(.active):not(.disabled) {
    background-color: #f5f5f5;
    border-color: var(--primary-color);
}

.pagination-link.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-link.disabled {
    color: #aaa;
    cursor: not-allowed;
}

/* Active Navigation Link */
.nav a.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 700;
}

/* Analysis Indicator */
.analysis-indicator {
    display: inline-block;
    margin-left: 8px;
    color: #2ecc71;
    font-size: 0.9em;
    animation: pulse 2s infinite;
}

.analysis-indicator i {
    filter: drop-shadow(0 0 3px rgba(46, 204, 113, 0.5));
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Loading Indicator */
.loading {
    text-align: center;
    padding: 20px;
    color: var(--secondary-color);
    font-style: italic;
}

.loading::after {
    content: "...";
    animation: dots 1.5s infinite;
}

@keyframes dots {
    0%, 20% { content: "."; }
    40% { content: ".."; }
    60%, 100% { content: "..."; }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.8s ease-in-out;
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-in-out;
}

.slide-in-up {
    animation: slideInUp 0.8s ease-in-out;
}

.slide-in-down {
    animation: slideInDown 0.8s ease-in-out;
}

.bounce-in {
    animation: bounceIn 0.8s ease-in-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.shake {
    animation: shake 0.5s ease-in-out;
}

.rotate {
    animation: rotate 1s ease-in-out;
}

.scale-in {
    animation: scaleIn 0.5s ease-in-out;
}

/* Animation Keyframes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInUp {
    from { transform: translateY(50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInDown {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); opacity: 0.8; }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes scaleIn {
    from { transform: scale(0); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Logo Animation */
.logo {
    display: inline-block;
    transition: var(--transition);
}

.logo:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 0 5px rgba(52, 152, 219, 0.5));
}

/* Button Animations */
.button, button[type="submit"] {
    position: relative;
    overflow: hidden;
}

.button::after, button[type="submit"]::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.button:hover::after, button[type="submit"]:hover::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.3;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

/* Card Hover Animation */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 6px rgba(0, 0, 0, 0.1);
}

/* Navigation Animation */
.nav a {
    position: relative;
}

.nav a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: white;
    transition: width 0.3s ease;
}

.nav a:hover::after {
    width: 100%;
}

/* Home Button */
.home-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    z-index: 1000;
}

.home-button:hover {
    transform: scale(1.1);
    background-color: var(--secondary-color);
}

.home-icon {
    font-size: 24px;
}

/* Staggered Animation for Lists */
.staggered-animation li {
    opacity: 0;
    animation: fadeIn 0.5s ease-in-out forwards;
}

.staggered-animation li:nth-child(1) { animation-delay: 0.1s; }
.staggered-animation li:nth-child(2) { animation-delay: 0.2s; }
.staggered-animation li:nth-child(3) { animation-delay: 0.3s; }
.staggered-animation li:nth-child(4) { animation-delay: 0.4s; }
.staggered-animation li:nth-child(5) { animation-delay: 0.5s; }
.staggered-animation li:nth-child(6) { animation-delay: 0.6s; }
.staggered-animation li:nth-child(7) { animation-delay: 0.7s; }
.staggered-animation li:nth-child(8) { animation-delay: 0.8s; }
.staggered-animation li:nth-child(9) { animation-delay: 0.9s; }
.staggered-animation li:nth-child(10) { animation-delay: 1.0s; }

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 20px;
        padding: 20px;
    }

    .header {
        padding: 20px;
        margin: -20px -20px 20px -20px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .nav ul {
        flex-direction: column;
    }

    .nav li {
        margin-bottom: 1px;
    }

    .job-listings {
        grid-template-columns: 1fr;
    }

    .search-container, .filter-options {
        flex-direction: column;
    }

    .pagination {
        flex-wrap: wrap;
    }
}

/* User Navigation Styles */
.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.header-text {
    flex: 1;
}

.user-nav {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.username {
    font-weight: 600;
    font-size: 1rem;
    color: white;
}

.user-role {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    text-transform: capitalize;
}

.user-dropdown {
    position: relative;
}

.dropdown-toggle {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
}

.dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    margin-top: 10px;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
    border-bottom: 1px solid #f0f0f0;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
}

.dropdown-item.logout {
    color: var(--error-color);
}

.dropdown-item.logout:hover {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--error-color);
}

.auth-buttons {
    display: flex;
    gap: 10px;
}

.auth-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    font-size: 0.9rem;
}

.login-btn {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.login-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.register-btn {
    background-color: white;
    color: var(--primary-color);
}

.register-btn:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Mobile responsive styles for user navigation */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .header-text {
        order: 2;
    }

    .user-nav {
        order: 1;
        width: 100%;
        justify-content: center;
    }

    .user-info {
        flex: 1;
        justify-content: center;
        max-width: 300px;
    }

    .auth-buttons {
        flex: 1;
        justify-content: center;
        max-width: 300px;
    }

    .dropdown-menu {
        right: auto;
        left: 50%;
        transform: translateX(-50%) translateY(-10px);
    }

    .dropdown-menu.show {
        transform: translateX(-50%) translateY(0);
    }
}
