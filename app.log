2025-05-18 20:09:49,391 - __main__ - INFO - Starting application
2025-05-18 20:09:49,438 - werkzeug - WARNING -  * Debugger is active!
2025-05-18 20:09:49,447 - werkzeug - INFO -  * Debugger PIN: 365-196-414
2025-05-18 20:13:44,126 - __main__ - INFO - About page accessed
2025-05-18 20:13:44,136 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:13:44] "GET /about HTTP/1.1" 200 -
2025-05-18 20:13:44,313 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:13:44] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-18 20:14:27,494 - __main__ - INFO - Landing page accessed
2025-05-18 20:14:27,509 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:14:27] "GET / HTTP/1.1" 200 -
2025-05-18 20:14:27,568 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:14:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:14:34,464 - __main__ - INFO - Job listings page accessed
2025-05-18 20:14:34,471 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:14:34] "GET /job_listings HTTP/1.1" 200 -
2025-05-18 20:14:34,515 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:14:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:14:34,525 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:14:34] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-18 20:15:06,105 - __main__ - INFO - Landing page accessed
2025-05-18 20:15:06,106 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:06] "GET / HTTP/1.1" 200 -
2025-05-18 20:15:06,166 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:15:10,087 - __main__ - INFO - About page accessed
2025-05-18 20:15:10,088 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:10] "GET /about HTTP/1.1" 200 -
2025-05-18 20:15:10,130 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:15:14,808 - __main__ - INFO - Landing page accessed
2025-05-18 20:15:14,809 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:14] "GET / HTTP/1.1" 200 -
2025-05-18 20:15:14,853 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:15:20,656 - __main__ - INFO - Admin panel accessed
2025-05-18 20:15:20,659 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:20] "GET /admin_panel HTTP/1.1" 200 -
2025-05-18 20:15:20,693 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:15:26,963 - __main__ - INFO - Landing page accessed
2025-05-18 20:15:26,963 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:26] "GET / HTTP/1.1" 200 -
2025-05-18 20:15:27,009 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:15:29,882 - __main__ - INFO - Contact page accessed
2025-05-18 20:15:29,885 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:29] "GET /contact HTTP/1.1" 200 -
2025-05-18 20:15:29,929 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:15:37,066 - __main__ - INFO - Landing page accessed
2025-05-18 20:15:37,068 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:37] "GET / HTTP/1.1" 200 -
2025-05-18 20:15:37,125 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:15:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:22:24,827 - __main__ - INFO - Landing page accessed
2025-05-18 20:22:24,829 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:22:24] "GET / HTTP/1.1" 200 -
2025-05-18 20:22:24,894 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:22:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:23:47,080 - __main__ - INFO - Landing page accessed
2025-05-18 20:23:47,118 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:23:47] "GET / HTTP/1.1" 200 -
2025-05-18 20:23:47,158 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:23:47] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-18 20:23:47,178 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:23:47] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:24:17,701 - __main__ - INFO - Applicant portal page accessed
2025-05-18 20:24:17,736 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:24:17] "GET /applicant_portal HTTP/1.1" 200 -
2025-05-18 20:24:17,779 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:24:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:24:17,786 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:24:17] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:24:23,472 - __main__ - INFO - Landing page accessed
2025-05-18 20:24:23,481 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:24:23] "GET / HTTP/1.1" 200 -
2025-05-18 20:24:23,559 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:24:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:24:23,578 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:24:23] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:24:31,931 - __main__ - INFO - About page accessed
2025-05-18 20:24:31,939 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:24:31] "GET /about HTTP/1.1" 200 -
2025-05-18 20:24:31,985 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:24:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:24:31,993 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:24:31] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:24:53,231 - __main__ - INFO - Landing page accessed
2025-05-18 20:24:53,232 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:24:53] "GET / HTTP/1.1" 200 -
2025-05-18 20:24:53,288 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:24:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:24:53,289 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:24:53] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:27:31,341 - __main__ - INFO - Landing page accessed
2025-05-18 20:27:31,344 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:27:31] "GET / HTTP/1.1" 200 -
2025-05-18 20:27:31,423 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:27:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:27:31,433 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:27:31] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:28:28,240 - __main__ - INFO - Landing page accessed
2025-05-18 20:28:28,241 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:28:28] "GET / HTTP/1.1" 200 -
2025-05-18 20:28:28,298 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:28:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:28:28,312 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:28:28] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:30:13,513 - __main__ - INFO - Landing page accessed
2025-05-18 20:30:13,515 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:13] "GET / HTTP/1.1" 200 -
2025-05-18 20:30:13,598 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:30:13,620 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:13] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:30:20,981 - __main__ - INFO - Applicant portal page accessed
2025-05-18 20:30:20,992 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:20] "GET /applicant_portal HTTP/1.1" 200 -
2025-05-18 20:30:21,045 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:30:21,074 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:21] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:30:33,639 - __main__ - INFO - CV upload attempt
2025-05-18 20:30:33,645 - __main__ - INFO - File saved: static\files\Adam_Ayomide_Agbaje_CV.pdf
2025-05-18 20:30:33,646 - __main__ - INFO - Processing CV text of length: 38
2025-05-18 20:30:33,652 - __main__ - ERROR - Error extracting information from CV: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Users\\Adam\\Desktop\\project\\.venv\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\share\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-18 20:30:33,662 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:33] "POST /applicant_portal HTTP/1.1" 200 -
2025-05-18 20:30:33,729 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:30:33,734 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:33] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:30:43,728 - __main__ - INFO - Job listings page accessed
2025-05-18 20:30:43,739 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:43] "GET /job_listings HTTP/1.1" 200 -
2025-05-18 20:30:43,783 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:30:43,790 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:43] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:30:57,500 - __main__ - INFO - Contact page accessed
2025-05-18 20:30:57,515 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:57] "GET /contact HTTP/1.1" 200 -
2025-05-18 20:30:57,561 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:30:57,568 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:30:57] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:31:09,715 - __main__ - INFO - Admin panel accessed
2025-05-18 20:31:09,730 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:31:09] "GET /admin_panel HTTP/1.1" 200 -
2025-05-18 20:31:09,777 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:31:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:31:09,790 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:31:09] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:31:51,818 - __main__ - INFO - Landing page accessed
2025-05-18 20:31:51,819 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:31:51] "GET / HTTP/1.1" 200 -
2025-05-18 20:31:51,911 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:31:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:31:51,937 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:31:51] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:31:56,551 - __main__ - INFO - Applicant portal page accessed
2025-05-18 20:31:56,552 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:31:56] "GET /applicant_portal HTTP/1.1" 200 -
2025-05-18 20:31:56,611 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:31:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:31:56,626 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:31:56] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:33:18,213 - __main__ - INFO - CV upload attempt
2025-05-18 20:33:18,216 - __main__ - WARNING - No file selected
2025-05-18 20:33:18,218 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:33:18] "POST /applicant_portal HTTP/1.1" 200 -
2025-05-18 20:33:18,271 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:33:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:33:18,287 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:33:18] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:33:31,574 - __main__ - INFO - CV upload attempt
2025-05-18 20:33:31,579 - __main__ - INFO - File saved: static\files\Adam_Ayomide_Agbaje_CV.pdf
2025-05-18 20:33:31,580 - __main__ - INFO - Processing CV text of length: 38
2025-05-18 20:33:31,582 - __main__ - ERROR - Error extracting information from CV: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Users\\Adam\\Desktop\\project\\.venv\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\share\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-18 20:33:31,585 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:33:31] "POST /applicant_portal HTTP/1.1" 200 -
2025-05-18 20:33:31,644 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:33:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:33:31,653 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:33:31] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:33:57,322 - __main__ - INFO - CV upload attempt
2025-05-18 20:33:57,403 - __main__ - INFO - File saved: static\files\Adam_Ayomide_Agbaje_CV.pdf
2025-05-18 20:33:57,403 - __main__ - INFO - Processing CV text of length: 38
2025-05-18 20:33:57,406 - __main__ - ERROR - Error extracting information from CV: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Users\\Adam\\Desktop\\project\\.venv\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\share\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-18 20:33:57,409 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:33:57] "POST /applicant_portal HTTP/1.1" 200 -
2025-05-18 20:33:57,469 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:33:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:33:57,474 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:33:57] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:34:07,037 - __main__ - INFO - Admin panel accessed
2025-05-18 20:34:07,041 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:07] "GET /admin_panel HTTP/1.1" 200 -
2025-05-18 20:34:07,146 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:34:07,165 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:07] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:34:15,338 - __main__ - INFO - Applicant portal page accessed
2025-05-18 20:34:15,340 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:15] "GET /applicant_portal HTTP/1.1" 200 -
2025-05-18 20:34:15,387 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:34:15,391 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:15] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:34:19,197 - __main__ - INFO - Job listings page accessed
2025-05-18 20:34:19,198 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:19] "GET /job_listings HTTP/1.1" 200 -
2025-05-18 20:34:19,244 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:34:19,248 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:19] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:34:29,510 - __main__ - INFO - API job search with query: Graphic design
2025-05-18 20:34:29,511 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:29] "GET /api/job_search?query=Graphic%20design HTTP/1.1" 200 -
2025-05-18 20:34:36,936 - __main__ - INFO - Applicant portal page accessed
2025-05-18 20:34:36,937 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:36] "GET /applicant_portal?job_id=1 HTTP/1.1" 200 -
2025-05-18 20:34:37,017 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:34:37,019 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:37] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:34:49,382 - __main__ - INFO - CV upload attempt
2025-05-18 20:34:49,386 - __main__ - INFO - File saved: static\files\Adam_Ayomide_Agbaje_CV.pdf
2025-05-18 20:34:49,386 - __main__ - INFO - Processing CV text of length: 38
2025-05-18 20:34:49,388 - __main__ - ERROR - Error extracting information from CV: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Users\\Adam\\Desktop\\project\\.venv\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\share\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-18 20:34:49,392 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:49] "POST /applicant_portal HTTP/1.1" 200 -
2025-05-18 20:34:49,458 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:34:49,466 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:49] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:34:53,747 - __main__ - INFO - Admin panel accessed
2025-05-18 20:34:53,749 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:53] "GET /admin_panel HTTP/1.1" 200 -
2025-05-18 20:34:53,810 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:34:53,818 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:34:53] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:35:17,667 - __main__ - INFO - Admin panel accessed
2025-05-18 20:35:17,669 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:17] "GET /admin_panel HTTP/1.1" 200 -
2025-05-18 20:35:17,713 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:35:17,724 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:17] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:35:21,934 - __main__ - INFO - Applicant portal page accessed
2025-05-18 20:35:21,936 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:21] "GET /applicant_portal HTTP/1.1" 200 -
2025-05-18 20:35:22,020 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:35:22,031 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:35:26,299 - __main__ - INFO - About page accessed
2025-05-18 20:35:26,301 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:26] "GET /about HTTP/1.1" 200 -
2025-05-18 20:35:26,348 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:35:26,355 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:26] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:35:47,305 - __main__ - INFO - Job listings page accessed
2025-05-18 20:35:47,306 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:47] "GET /job_listings HTTP/1.1" 200 -
2025-05-18 20:35:47,346 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:35:47,355 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:47] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:35:50,172 - __main__ - INFO - Landing page accessed
2025-05-18 20:35:50,173 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:50] "GET / HTTP/1.1" 200 -
2025-05-18 20:35:50,214 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:35:50,227 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:50] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:35:54,263 - __main__ - INFO - Job listings page accessed
2025-05-18 20:35:54,264 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:54] "GET /job_listings HTTP/1.1" 200 -
2025-05-18 20:35:54,335 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:35:54,340 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:35:54] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:39:17,558 - __main__ - INFO - Admin panel accessed
2025-05-18 20:39:17,559 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:39:17] "GET /admin_panel HTTP/1.1" 200 -
2025-05-18 20:39:17,611 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:39:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:39:17,625 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:39:17] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:39:23,696 - __main__ - INFO - Landing page accessed
2025-05-18 20:39:23,698 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:39:23] "GET / HTTP/1.1" 200 -
2025-05-18 20:39:23,766 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:39:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:39:23,778 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:39:23] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:39:35,113 - __main__ - INFO - Contact page accessed
2025-05-18 20:39:35,115 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:39:35] "GET /contact HTTP/1.1" 200 -
2025-05-18 20:39:35,169 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:39:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:39:35,176 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:39:35] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:39:56,071 - __main__ - INFO - Contact form submission from Adam Agbaje (<EMAIL>)
2025-05-18 20:39:56,071 - __main__ - INFO - Contact message: {'name': 'Adam Agbaje', 'email': '<EMAIL>', 'message': 'dhbs.fdsfdfjbdfkbvd lkjgbkejdflkjefk fkebkefkenrbfkjnrf.kr  vkmfd v', 'timestamp': '2025-05-18 20:39:56', 'ip_address': '127.0.0.1'}
2025-05-18 20:39:56,073 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:39:56] "POST /contact HTTP/1.1" 200 -
2025-05-18 20:39:56,127 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:39:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:39:56,139 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:39:56] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:40:01,938 - __main__ - INFO - Admin panel accessed
2025-05-18 20:40:01,940 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:40:01] "GET /admin_panel HTTP/1.1" 200 -
2025-05-18 20:40:02,016 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:40:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:40:02,025 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:40:02] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:40:15,657 - __main__ - INFO - Landing page accessed
2025-05-18 20:40:15,658 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:40:15] "GET / HTTP/1.1" 200 -
2025-05-18 20:40:15,701 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:40:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:40:15,721 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:40:15] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:45:03,029 - __main__ - INFO - Landing page accessed
2025-05-18 20:45:03,038 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:45:03] "GET / HTTP/1.1" 200 -
2025-05-18 20:45:03,160 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:45:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:45:03,212 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:45:03] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:45:11,470 - __main__ - INFO - Applicant portal page accessed
2025-05-18 20:45:11,473 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:45:11] "GET /applicant_portal HTTP/1.1" 200 -
2025-05-18 20:45:11,616 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:45:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:45:11,617 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:45:11] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:45:27,394 - __main__ - INFO - CV upload attempt
2025-05-18 20:45:27,483 - __main__ - INFO - File saved: static\files\Adam_Ayomide_Agbaje_CV.pdf
2025-05-18 20:45:27,513 - __main__ - INFO - Processing CV text of length: 38
2025-05-18 20:45:27,551 - __main__ - ERROR - Error extracting information from CV: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Users\\Adam\\Desktop\\project\\.venv\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\share\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-18 20:45:27,589 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:45:27] "POST /applicant_portal HTTP/1.1" 200 -
2025-05-18 20:45:27,836 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:45:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:45:27,893 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:45:27] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:46:16,360 - __main__ - INFO - Admin panel accessed
2025-05-18 20:46:16,363 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:46:16] "GET /admin_panel HTTP/1.1" 200 -
2025-05-18 20:46:16,450 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:46:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:46:16,478 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:46:16] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:46:45,438 - __main__ - INFO - Job listings page accessed
2025-05-18 20:46:45,439 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:46:45] "GET /job_listings HTTP/1.1" 200 -
2025-05-18 20:46:45,521 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:46:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:46:45,532 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:46:45] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:47:14,067 - __main__ - INFO - Contact page accessed
2025-05-18 20:47:14,069 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:47:14] "GET /contact HTTP/1.1" 200 -
2025-05-18 20:47:14,134 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:47:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:47:14,149 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:47:14] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 20:47:37,765 - __main__ - INFO - Contact page accessed
2025-05-18 20:47:37,767 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:47:37] "GET /contact HTTP/1.1" 200 -
2025-05-18 20:47:37,863 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:47:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 20:47:37,872 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 20:47:37] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 21:30:20,445 - __main__ - INFO - Landing page accessed
2025-05-18 21:30:20,449 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:30:20] "GET / HTTP/1.1" 200 -
2025-05-18 21:30:20,584 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:30:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 21:30:20,897 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:30:20] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 21:30:25,062 - __main__ - INFO - About page accessed
2025-05-18 21:30:25,066 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:30:25] "GET /about HTTP/1.1" 200 -
2025-05-18 21:30:25,297 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:30:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 21:30:25,319 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:30:25] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 21:30:29,221 - __main__ - INFO - Landing page accessed
2025-05-18 21:30:29,240 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:30:29] "GET / HTTP/1.1" 200 -
2025-05-18 21:30:29,366 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:30:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 21:30:29,401 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:30:29] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 21:30:42,723 - __main__ - INFO - About page accessed
2025-05-18 21:30:42,724 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:30:42] "GET /about HTTP/1.1" 200 -
2025-05-18 21:30:42,773 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:30:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 21:30:42,776 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:30:42] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 21:31:23,759 - __main__ - INFO - Applicant portal page accessed
2025-05-18 21:31:23,761 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:31:23] "GET /applicant_portal HTTP/1.1" 200 -
2025-05-18 21:31:23,828 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:31:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 21:31:23,859 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:31:23] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 21:31:31,371 - __main__ - INFO - Admin panel accessed
2025-05-18 21:31:31,372 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:31:31] "GET /admin_panel HTTP/1.1" 200 -
2025-05-18 21:31:31,428 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:31:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 21:31:31,439 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:31:31] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 21:31:50,534 - __main__ - INFO - Job listings page accessed
2025-05-18 21:31:50,538 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:31:50] "GET /job_listings HTTP/1.1" 200 -
2025-05-18 21:31:50,580 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:31:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 21:31:50,586 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:31:50] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 21:32:01,421 - __main__ - INFO - Contact page accessed
2025-05-18 21:32:01,422 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:32:01] "GET /contact HTTP/1.1" 200 -
2025-05-18 21:32:01,485 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:32:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 21:32:01,495 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:32:01] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 21:32:27,289 - __main__ - INFO - Landing page accessed
2025-05-18 21:32:27,290 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:32:27] "GET / HTTP/1.1" 200 -
2025-05-18 21:32:27,334 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:32:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 21:32:27,349 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 21:32:27] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 22:13:44,632 - __main__ - INFO - About page accessed
2025-05-18 22:13:44,638 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:13:44] "GET /about HTTP/1.1" 200 -
2025-05-18 22:13:44,721 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:13:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 22:13:44,747 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:13:44] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 22:13:49,156 - __main__ - INFO - Applicant portal page accessed
2025-05-18 22:13:49,159 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:13:49] "GET /applicant_portal HTTP/1.1" 200 -
2025-05-18 22:13:49,242 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:13:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 22:13:49,249 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:13:49] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 22:13:53,004 - __main__ - INFO - About page accessed
2025-05-18 22:13:53,005 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:13:53] "GET /about HTTP/1.1" 200 -
2025-05-18 22:13:53,095 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:13:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 22:13:53,106 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:13:53] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 22:13:58,637 - __main__ - INFO - Applicant portal page accessed
2025-05-18 22:13:58,637 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:13:58] "GET /applicant_portal HTTP/1.1" 200 -
2025-05-18 22:13:58,682 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:13:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 22:13:58,688 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:13:58] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 22:14:09,889 - __main__ - INFO - CV upload attempt
2025-05-18 22:14:09,922 - __main__ - INFO - File saved: static\files\Adam_Ayomide_Agbaje_CV.pdf
2025-05-18 22:14:09,931 - __main__ - INFO - Processing CV text of length: 38
2025-05-18 22:14:09,946 - __main__ - ERROR - Error extracting information from CV: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Users\\Adam\\Desktop\\project\\.venv\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\share\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-18 22:14:09,971 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:09] "POST /applicant_portal HTTP/1.1" 200 -
2025-05-18 22:14:10,117 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 22:14:10,127 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:10] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 22:14:24,150 - __main__ - INFO - Job listings page accessed
2025-05-18 22:14:24,153 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:24] "GET /job_listings HTTP/1.1" 200 -
2025-05-18 22:14:24,220 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 22:14:24,225 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:24] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 22:14:35,224 - __main__ - INFO - API job search with query: Data
2025-05-18 22:14:35,225 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:35] "GET /api/job_search?query=Data HTTP/1.1" 200 -
2025-05-18 22:14:39,662 - __main__ - INFO - API job search with query: Data
2025-05-18 22:14:39,662 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:39] "GET /api/job_search?query=Data HTTP/1.1" 200 -
2025-05-18 22:14:43,258 - __main__ - INFO - Applicant portal page accessed
2025-05-18 22:14:43,260 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:43] "GET /applicant_portal?job_id=3 HTTP/1.1" 200 -
2025-05-18 22:14:43,333 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 22:14:43,343 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:43] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 22:14:56,272 - __main__ - INFO - CV upload attempt
2025-05-18 22:14:56,286 - __main__ - INFO - File saved: static\files\Eventio_Chakam_trend_250517_041847.pdf
2025-05-18 22:14:56,287 - __main__ - INFO - Processing CV text of length: 38
2025-05-18 22:14:56,292 - __main__ - ERROR - Error extracting information from CV: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Users\\Adam\\Desktop\\project\\.venv\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\share\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-18 22:14:56,300 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:56] "POST /applicant_portal HTTP/1.1" 200 -
2025-05-18 22:14:56,415 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 22:14:56,426 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:14:56] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 22:15:37,370 - __main__ - INFO - Admin panel accessed
2025-05-18 22:15:37,373 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:15:37] "GET /admin_panel HTTP/1.1" 200 -
2025-05-18 22:15:37,512 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:15:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 22:15:37,537 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:15:37] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 22:17:22,244 - __main__ - INFO - Job listings page accessed
2025-05-18 22:17:22,246 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:17:22] "GET /job_listings HTTP/1.1" 200 -
2025-05-18 22:17:22,380 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:17:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 22:17:22,424 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:17:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 22:17:26,981 - __main__ - INFO - Contact page accessed
2025-05-18 22:17:26,982 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:17:26] "GET /contact HTTP/1.1" 200 -
2025-05-18 22:17:27,063 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:17:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 22:17:27,081 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:17:27] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-18 22:17:42,452 - __main__ - INFO - Landing page accessed
2025-05-18 22:17:42,454 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:17:42] "GET / HTTP/1.1" 200 -
2025-05-18 22:17:42,524 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:17:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-18 22:17:42,532 - werkzeug - INFO - 127.0.0.1 - - [18/May/2025 22:17:42] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 11:47:55,388 - __main__ - INFO - Starting application
2025-05-19 11:47:55,713 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-19 11:47:55,714 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-19 11:47:55,722 - werkzeug - INFO -  * Restarting with stat
2025-05-19 11:47:58,122 - __main__ - INFO - Starting application
2025-05-19 11:47:58,164 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 11:47:58,202 - werkzeug - INFO -  * Debugger PIN: 365-196-414
2025-05-19 11:51:44,929 - __main__ - INFO - Landing page accessed
2025-05-19 11:51:45,006 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:51:45] "GET / HTTP/1.1" 200 -
2025-05-19 11:51:45,332 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:51:45] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-19 11:51:45,394 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:51:45] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-19 11:51:49,785 - __main__ - WARNING - 404 error: /favicon.ico
2025-05-19 11:51:49,801 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:51:49] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-19 11:52:08,573 - __main__ - INFO - Resume scanner page accessed
2025-05-19 11:52:08,666 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:08] "GET /resume-scanner HTTP/1.1" 200 -
2025-05-19 11:52:08,880 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 11:52:09,054 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:09] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 11:52:20,270 - __main__ - INFO - Landing page accessed
2025-05-19 11:52:20,276 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:20] "GET / HTTP/1.1" 200 -
2025-05-19 11:52:20,502 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 11:52:20,559 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:20] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 11:52:25,659 - __main__ - INFO - Resume scanner page accessed
2025-05-19 11:52:25,663 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:25] "GET /resume-scanner HTTP/1.1" 200 -
2025-05-19 11:52:25,825 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 11:52:25,909 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:25] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 11:52:44,165 - __main__ - INFO - Resume scan request received
2025-05-19 11:52:44,180 - __main__ - INFO - Resume saved: static\files\Adam_Ayomide_Agbaje_CV.pdf
2025-05-19 11:52:44,181 - __main__ - INFO - Analyzing resume and job description match
2025-05-19 11:52:44,185 - __main__ - INFO - Analysis complete. Match score: 0.0%
2025-05-19 11:52:44,187 - __main__ - INFO - Resume scanner page accessed
2025-05-19 11:52:44,354 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:44] "[35m[1mPOST /resume-scanner HTTP/1.1[0m" 500 -
2025-05-19 11:52:44,428 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:44] "GET /resume-scanner?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-05-19 11:52:44,508 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:44] "GET /resume-scanner?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-05-19 11:52:45,594 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:45] "GET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1" 200 -
2025-05-19 11:52:45,601 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:45] "GET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-05-19 11:52:45,865 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:52:45] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 11:53:15,042 - __main__ - INFO - Resume scan request received
2025-05-19 11:53:15,062 - __main__ - INFO - Resume saved: static\files\Adam_Ayomide_Agbaje_CV.pdf
2025-05-19 11:53:15,067 - __main__ - INFO - Analyzing resume and job description match
2025-05-19 11:53:15,069 - __main__ - INFO - Analysis complete. Match score: 0.0%
2025-05-19 11:53:15,070 - __main__ - INFO - Resume scanner page accessed
2025-05-19 11:53:15,184 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:53:15] "[35m[1mPOST /resume-scanner HTTP/1.1[0m" 500 -
2025-05-19 11:53:15,287 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:53:15] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-19 11:53:15,599 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:53:15] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-19 11:53:16,434 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:53:16] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1[0m" 304 -
2025-05-19 11:53:16,436 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:53:16] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 11:53:16,697 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:53:16] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 11:58:21,691 - __main__ - INFO - Resume scanner page accessed
2025-05-19 11:58:21,701 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:58:21] "GET /resume-scanner HTTP/1.1" 200 -
2025-05-19 11:58:21,786 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:58:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 11:58:21,944 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:58:21] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 11:58:42,694 - __main__ - INFO - Resume scan request received
2025-05-19 11:58:42,703 - __main__ - WARNING - No file selected
2025-05-19 11:58:42,704 - __main__ - INFO - Resume scanner page accessed
2025-05-19 11:58:42,707 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:58:42] "POST /resume-scanner HTTP/1.1" 200 -
2025-05-19 11:58:42,874 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:58:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 11:58:42,948 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:58:42] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 11:59:04,503 - __main__ - INFO - Resume scan request received
2025-05-19 11:59:04,516 - __main__ - INFO - Resume saved: static\files\Adam_Ayomide_Agbaje_CV.pdf
2025-05-19 11:59:04,523 - __main__ - INFO - Analyzing resume and job description match
2025-05-19 11:59:04,534 - __main__ - INFO - Analysis complete. Match score: 0.0%
2025-05-19 11:59:04,542 - __main__ - INFO - Resume scanner page accessed
2025-05-19 11:59:04,751 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:59:04] "[35m[1mPOST /resume-scanner HTTP/1.1[0m" 500 -
2025-05-19 11:59:04,820 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:59:04] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-19 11:59:04,823 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:59:04] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-19 11:59:05,956 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:59:05] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1[0m" 304 -
2025-05-19 11:59:05,958 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:59:05] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 11:59:06,198 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:59:06] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 11:59:15,752 - __main__ - INFO - Starting application
2025-05-19 11:59:15,910 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-19 11:59:15,911 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-19 11:59:15,917 - werkzeug - INFO -  * Restarting with stat
2025-05-19 11:59:18,337 - __main__ - INFO - Starting application
2025-05-19 11:59:18,405 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 11:59:18,411 - werkzeug - INFO -  * Debugger PIN: 365-196-414
2025-05-19 12:04:55,583 - __main__ - INFO - Landing page accessed
2025-05-19 12:04:55,615 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:04:55] "GET / HTTP/1.1" 200 -
2025-05-19 12:04:55,891 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:04:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 12:04:58,982 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:04:58] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 12:05:06,968 - __main__ - INFO - Landing page accessed
2025-05-19 12:05:06,973 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:05:06] "GET / HTTP/1.1" 200 -
2025-05-19 12:05:07,392 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:05:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 12:05:08,365 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:05:08] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 12:07:37,968 - __main__ - INFO - About page accessed
2025-05-19 12:07:37,977 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:07:37] "GET /about HTTP/1.1" 200 -
2025-05-19 12:07:38,200 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:07:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 12:07:38,214 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:07:38] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 12:07:46,421 - __main__ - INFO - Job listings page accessed
2025-05-19 12:07:46,436 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:07:46] "GET /job_listings HTTP/1.1" 200 -
2025-05-19 12:07:46,734 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:07:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 12:07:46,796 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:07:46] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 12:59:24,847 - __main__ - INFO - Resume scanner page accessed
2025-05-19 12:59:24,863 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:59:24] "GET /resume-scanner HTTP/1.1" 200 -
2025-05-19 12:59:25,253 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:59:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 12:59:25,716 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 12:59:25] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 13:00:42,843 - __main__ - INFO - Resume scan request received
2025-05-19 13:00:42,854 - __main__ - INFO - Resume saved: static\files\Color_block_resume.docx
2025-05-19 13:00:42,854 - __main__ - INFO - Analyzing resume and job description match
2025-05-19 13:00:42,858 - __main__ - INFO - Analysis complete. Match score: 5.1%
2025-05-19 13:00:42,858 - __main__ - INFO - Resume scanner page accessed
2025-05-19 13:00:42,887 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:00:42] "[35m[1mPOST /resume-scanner HTTP/1.1[0m" 500 -
2025-05-19 13:00:43,018 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:00:43] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-19 13:00:43,154 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:00:43] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-19 13:00:44,126 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:00:44] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1[0m" 304 -
2025-05-19 13:00:44,134 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:00:44] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:00:44,380 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:00:44] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:00:49,323 - werkzeug - INFO -  * To enable the debugger you need to enter the security pin:
2025-05-19 13:00:49,324 - werkzeug - INFO -  * Debugger pin code: 365-196-414
2025-05-19 13:00:49,326 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:00:49] "GET /resume-scanner?__debugger__=yes&cmd=printpin&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1" 200 -
2025-05-19 13:00:57,129 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:00:57] "GET /resume-scanner?__debugger__=yes&cmd=pinauth&pin=1234&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1" 200 -
2025-05-19 13:01:03,292 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:01:03] "GET /resume-scanner?__debugger__=yes&cmd=pinauth&pin=0000&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1" 200 -
2025-05-19 13:01:59,961 - __main__ - INFO - Resume scan request received
2025-05-19 13:01:59,966 - __main__ - INFO - Resume saved: static\files\Color_block_resume.docx
2025-05-19 13:01:59,966 - __main__ - INFO - Analyzing resume and job description match
2025-05-19 13:01:59,969 - __main__ - INFO - Analysis complete. Match score: 5.1%
2025-05-19 13:01:59,971 - __main__ - INFO - Resume scanner page accessed
2025-05-19 13:02:00,000 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:02:00] "[35m[1mPOST /resume-scanner HTTP/1.1[0m" 500 -
2025-05-19 13:02:00,057 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:02:00] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-19 13:02:00,374 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:02:00] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-19 13:02:00,808 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:02:00] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1[0m" 304 -
2025-05-19 13:02:00,814 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:02:00] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:02:01,065 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:02:01] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:05:48,276 - __main__ - INFO - Resume scan request received
2025-05-19 13:05:48,282 - __main__ - INFO - Resume saved: static\files\Color_block_resume.docx
2025-05-19 13:05:48,282 - __main__ - INFO - Analyzing resume and job description match
2025-05-19 13:05:48,283 - __main__ - INFO - Analysis complete. Match score: 5.1%
2025-05-19 13:05:48,284 - __main__ - INFO - Resume scanner page accessed
2025-05-19 13:05:48,337 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:05:48] "[35m[1mPOST /resume-scanner HTTP/1.1[0m" 500 -
2025-05-19 13:05:48,400 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:05:48] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-19 13:05:48,716 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:05:48] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-19 13:05:49,343 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:05:49] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1[0m" 304 -
2025-05-19 13:05:49,344 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:05:49] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:05:49,605 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:05:49] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:09:20,552 - __main__ - INFO - Resume scan request received
2025-05-19 13:09:20,558 - __main__ - INFO - Resume saved: static\files\Color_block_resume.docx
2025-05-19 13:09:20,559 - __main__ - INFO - Analyzing resume and job description match
2025-05-19 13:09:20,561 - __main__ - INFO - Analysis complete. Match score: 5.1%
2025-05-19 13:09:20,562 - __main__ - INFO - Resume scanner page accessed
2025-05-19 13:09:20,583 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:09:20] "[35m[1mPOST /resume-scanner HTTP/1.1[0m" 500 -
2025-05-19 13:09:20,651 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:09:20] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-19 13:09:20,925 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:09:20] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-19 13:09:22,140 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:09:22] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1[0m" 304 -
2025-05-19 13:09:22,147 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:09:22] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:09:22,403 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:09:22] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:09:23,815 - werkzeug - INFO -  * To enable the debugger you need to enter the security pin:
2025-05-19 13:09:23,817 - werkzeug - INFO -  * Debugger pin code: 365-196-414
2025-05-19 13:09:23,818 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:09:23] "GET /resume-scanner?__debugger__=yes&cmd=printpin&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1" 200 -
2025-05-19 13:09:26,509 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:09:26] "GET /resume-scanner?__debugger__=yes&cmd=pinauth&pin=365-196-414&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1" 200 -
2025-05-19 13:10:07,103 - __main__ - INFO - Resume scanner page accessed
2025-05-19 13:10:07,105 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:10:07] "GET /resume-scanner HTTP/1.1" 200 -
2025-05-19 13:10:07,183 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:10:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 13:10:07,504 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:10:07] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 13:10:32,512 - __main__ - INFO - Resume scan request received
2025-05-19 13:10:32,520 - __main__ - INFO - Resume saved: static\files\Color_block_resume.docx
2025-05-19 13:10:32,520 - __main__ - INFO - Analyzing resume and job description match
2025-05-19 13:10:32,521 - __main__ - INFO - Analysis complete. Match score: 5.1%
2025-05-19 13:10:32,521 - __main__ - INFO - Resume scanner page accessed
2025-05-19 13:10:32,545 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:10:32] "[35m[1mPOST /resume-scanner HTTP/1.1[0m" 500 -
2025-05-19 13:10:32,588 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:10:32] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-19 13:10:32,918 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:10:32] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-19 13:10:33,340 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:10:33] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1[0m" 304 -
2025-05-19 13:10:33,652 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:10:33] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:10:33,917 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:10:33] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:12:49,032 - __main__ - INFO - Resume scan request received
2025-05-19 13:12:49,039 - __main__ - INFO - Resume saved: static\files\Color_block_resume.docx
2025-05-19 13:12:49,040 - __main__ - INFO - Analyzing resume and job description match
2025-05-19 13:12:49,042 - __main__ - INFO - Analysis complete. Match score: 5.1%
2025-05-19 13:12:49,043 - __main__ - INFO - Resume scanner page accessed
2025-05-19 13:12:49,070 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:12:49] "[35m[1mPOST /resume-scanner HTTP/1.1[0m" 500 -
2025-05-19 13:12:49,130 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:12:49] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-19 13:12:49,448 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:12:49] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-19 13:12:50,066 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:12:50] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1[0m" 304 -
2025-05-19 13:12:50,069 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:12:50] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:12:50,306 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:12:50] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:13:16,073 - __main__ - INFO - Applicant portal page accessed
2025-05-19 13:13:16,144 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:13:16] "GET /applicant_portal HTTP/1.1" 200 -
2025-05-19 13:13:16,205 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:13:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 13:13:16,526 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:13:16] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 13:13:27,780 - __main__ - INFO - CV upload attempt
2025-05-19 13:13:27,793 - __main__ - INFO - File saved: static\files\Color_block_resume.docx
2025-05-19 13:13:27,795 - __main__ - INFO - Processing CV text of length: 38
2025-05-19 13:13:27,804 - __main__ - ERROR - Error extracting information from CV: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Users\\Adam\\Desktop\\project\\.venv\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\share\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-19 13:13:27,820 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:13:27] "POST /applicant_portal HTTP/1.1" 200 -
2025-05-19 13:13:27,942 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:13:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 13:13:28,061 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:13:28] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 13:13:38,959 - __main__ - INFO - Job listings page accessed
2025-05-19 13:13:38,960 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:13:38] "GET /job_listings HTTP/1.1" 200 -
2025-05-19 13:13:39,213 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:13:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 13:13:39,320 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:13:39] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 13:14:49,332 - __main__ - INFO - Resume scanner page accessed
2025-05-19 13:14:49,334 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:14:49] "GET /resume-scanner HTTP/1.1" 200 -
2025-05-19 13:14:49,423 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:14:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 13:14:49,607 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:14:49] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 13:14:53,994 - __main__ - INFO - Resume scan request received
2025-05-19 13:14:53,999 - __main__ - WARNING - No job description provided
2025-05-19 13:14:54,000 - __main__ - INFO - Resume scanner page accessed
2025-05-19 13:14:54,003 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:14:54] "POST /resume-scanner HTTP/1.1" 200 -
2025-05-19 13:14:54,099 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:14:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 13:14:54,253 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:14:54] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 13:15:08,407 - __main__ - INFO - Resume scan request received
2025-05-19 13:15:08,419 - __main__ - INFO - Resume saved: static\files\Color_block_resume.docx
2025-05-19 13:15:08,422 - __main__ - INFO - Analyzing resume and job description match
2025-05-19 13:15:08,424 - __main__ - INFO - Analysis complete. Match score: 5.1%
2025-05-19 13:15:08,426 - __main__ - INFO - Resume scanner page accessed
2025-05-19 13:15:08,470 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:15:08] "[35m[1mPOST /resume-scanner HTTP/1.1[0m" 500 -
2025-05-19 13:15:08,557 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:15:08] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-19 13:15:08,676 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:15:08] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-19 13:15:09,652 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:15:09] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png&s=xfbkC1skNBV7c7sG5lv1 HTTP/1.1[0m" 304 -
2025-05-19 13:15:09,669 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:15:09] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 13:15:09,922 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 13:15:09] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 14:29:15,297 - __main__ - INFO - Starting application
2025-05-19 14:29:15,460 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-19 14:29:15,461 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-19 14:29:15,464 - werkzeug - INFO -  * Restarting with stat
2025-05-19 14:29:17,114 - __main__ - INFO - Starting application
2025-05-19 14:29:17,143 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 14:29:17,149 - werkzeug - INFO -  * Debugger PIN: 365-196-414
2025-05-19 14:31:09,175 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\project\\app.py', reloading
2025-05-19 14:31:09,593 - werkzeug - INFO -  * Restarting with stat
2025-05-19 14:31:11,171 - __main__ - INFO - Starting application
2025-05-19 14:31:11,197 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 14:31:11,202 - werkzeug - INFO -  * Debugger PIN: 365-196-414
2025-05-19 14:35:44,044 - __main__ - INFO - Resume scan request received
2025-05-19 14:35:44,049 - __main__ - WARNING - No job description provided
2025-05-19 14:35:44,050 - __main__ - INFO - Resume scanner page accessed
2025-05-19 14:35:44,140 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 14:35:44] "POST /resume-scanner HTTP/1.1" 200 -
2025-05-19 14:35:44,560 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 14:35:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 14:35:44,747 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 14:35:44] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 14:35:59,852 - __main__ - INFO - Resume scan request received
2025-05-19 14:35:59,863 - __main__ - INFO - Resume saved: static\files\Color_block_resume.docx
2025-05-19 14:35:59,864 - __main__ - INFO - Analyzing resume and job description match
2025-05-19 14:35:59,865 - __main__ - INFO - Analysis complete. Match score: 5.1%
2025-05-19 14:35:59,865 - __main__ - INFO - Resume scanner page accessed
2025-05-19 14:36:00,010 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 14:36:00] "[35m[1mPOST /resume-scanner HTTP/1.1[0m" 500 -
2025-05-19 14:36:00,105 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 14:36:00] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-05-19 14:36:00,188 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 14:36:00] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-05-19 14:36:01,257 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 14:36:01] "GET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png&s=X9UDCWLzPOBQuSXqyveT HTTP/1.1" 200 -
2025-05-19 14:36:01,259 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 14:36:01] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 14:36:01,499 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 14:36:01] "[36mGET /resume-scanner?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-05-19 16:36:09,500 - __main__ - INFO - Landing page accessed
2025-05-19 16:36:09,604 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:09] "GET / HTTP/1.1" 200 -
2025-05-19 16:36:09,778 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 16:36:09,826 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:09] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 16:36:20,155 - __main__ - INFO - Resume scanner page accessed
2025-05-19 16:36:20,169 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:20] "GET /resume-scanner HTTP/1.1" 200 -
2025-05-19 16:36:20,300 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 16:36:20,306 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:20] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 16:36:26,554 - __main__ - INFO - Applicant portal page accessed
2025-05-19 16:36:26,751 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:26] "GET /applicant_portal HTTP/1.1" 200 -
2025-05-19 16:36:26,959 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 16:36:26,964 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:26] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 16:36:30,728 - __main__ - INFO - Job listings page accessed
2025-05-19 16:36:30,941 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:30] "GET /job_listings HTTP/1.1" 200 -
2025-05-19 16:36:31,205 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 16:36:31,214 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:31] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 16:36:40,776 - __main__ - INFO - Admin panel accessed
2025-05-19 16:36:40,912 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:40] "GET /admin_panel HTTP/1.1" 200 -
2025-05-19 16:36:41,139 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 16:36:41,157 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:41] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 16:36:52,257 - __main__ - INFO - Resume scanner page accessed
2025-05-19 16:36:52,258 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:52] "GET /resume-scanner HTTP/1.1" 200 -
2025-05-19 16:36:52,378 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 16:36:53,320 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:36:53] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 16:37:21,490 - __main__ - INFO - Resume scan request received
2025-05-19 16:37:21,506 - __main__ - WARNING - No job description provided
2025-05-19 16:37:21,506 - __main__ - INFO - Resume scanner page accessed
2025-05-19 16:37:21,509 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:37:21] "POST /resume-scanner HTTP/1.1" 200 -
2025-05-19 16:37:21,634 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:37:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 16:37:21,702 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:37:21] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 16:37:31,744 - __main__ - INFO - Resume scan request received
2025-05-19 16:37:31,758 - __main__ - WARNING - No file selected
2025-05-19 16:37:31,760 - __main__ - INFO - Resume scanner page accessed
2025-05-19 16:37:31,774 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:37:31] "POST /resume-scanner HTTP/1.1" 200 -
2025-05-19 16:37:31,878 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:37:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 16:37:31,895 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:37:31] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 16:37:41,764 - __main__ - INFO - Contact page accessed
2025-05-19 16:37:41,839 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:37:41] "GET /contact HTTP/1.1" 200 -
2025-05-19 16:37:41,911 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:37:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 16:37:41,921 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:37:41] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 16:39:24,314 - __main__ - INFO - Admin panel accessed
2025-05-19 16:39:24,317 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:39:24] "GET /admin_panel HTTP/1.1" 200 -
2025-05-19 16:39:24,405 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:39:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 16:39:24,421 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:39:24] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 16:39:50,515 - __main__ - INFO - Job listings page accessed
2025-05-19 16:39:50,522 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:39:50] "GET /job_listings HTTP/1.1" 200 -
2025-05-19 16:39:50,643 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:39:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 16:39:50,668 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 16:39:50] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 20:32:27,098 - __main__ - INFO - Landing page accessed
2025-05-19 20:32:27,127 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:32:27] "GET / HTTP/1.1" 200 -
2025-05-19 20:32:27,378 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:32:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 20:32:27,433 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:32:27] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 20:43:52,391 - __main__ - INFO - Starting application
2025-05-19 20:43:52,708 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-19 20:43:52,709 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-19 20:43:52,715 - werkzeug - INFO -  * Restarting with stat
2025-05-19 20:43:59,004 - __main__ - INFO - Starting application
2025-05-19 20:43:59,163 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 20:43:59,189 - werkzeug - INFO -  * Debugger PIN: 365-196-414
2025-05-19 20:44:09,510 - __main__ - INFO - Resume scanner page accessed
2025-05-19 20:44:09,525 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:09] "GET /resume-scanner HTTP/1.1" 200 -
2025-05-19 20:44:10,189 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:10] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-19 20:44:10,729 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:10] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-19 20:44:16,771 - __main__ - WARNING - 404 error: /favicon.ico
2025-05-19 20:44:17,008 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:17] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-19 20:44:21,214 - __main__ - INFO - Job listings page accessed
2025-05-19 20:44:21,255 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:21] "GET /job_listings HTTP/1.1" 200 -
2025-05-19 20:44:21,466 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 20:44:22,975 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 20:44:26,273 - __main__ - INFO - Applicant portal page accessed
2025-05-19 20:44:26,401 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:26] "GET /applicant_portal HTTP/1.1" 200 -
2025-05-19 20:44:26,848 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 20:44:26,860 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:26] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 20:44:47,454 - __main__ - INFO - CV upload attempt
2025-05-19 20:44:47,542 - __main__ - INFO - File saved: static\files\Color_block_resume.docx
2025-05-19 20:44:47,546 - __main__ - INFO - Processing CV text of length: 38
2025-05-19 20:44:47,584 - __main__ - ERROR - Error extracting information from CV: 
**********************************************************************
  Resource [93mpunkt_tab[0m not found.
  Please use the NLTK Downloader to obtain the resource:

  [31m>>> import nltk
  >>> nltk.download('punkt_tab')
  [0m
  For more information see: https://www.nltk.org/data.html

  Attempted to load [93mtokenizers/punkt_tab/english/[0m

  Searched in:
    - 'C:\\Users\\<USER>\\Users\\Adam\\Desktop\\project\\.venv\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\share\\nltk_data'
    - 'C:\\Users\\<USER>\\Desktop\\project\\.venv\\lib\\nltk_data'
    - 'C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data'
    - 'C:\\nltk_data'
    - 'D:\\nltk_data'
    - 'E:\\nltk_data'
**********************************************************************

2025-05-19 20:44:47,603 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:47] "POST /applicant_portal HTTP/1.1" 200 -
2025-05-19 20:44:47,764 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-19 20:44:47,878 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:44:47] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-19 20:51:40,363 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:51:40] "[31m[1mGET / HTTP/1.1[0m" 400 -
2025-05-19 20:51:42,268 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:51:42] "[31m[1mGET / HTTP/1.1[0m" 400 -
2025-05-19 20:51:48,689 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:51:48] "[31m[1mGET / HTTP/1.1[0m" 400 -
2025-05-19 20:51:58,602 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:51:58] "[31m[1mGET / HTTP/1.1[0m" 400 -
2025-05-19 20:52:19,043 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 20:52:19] "[31m[1mGET / HTTP/1.1[0m" 400 -
