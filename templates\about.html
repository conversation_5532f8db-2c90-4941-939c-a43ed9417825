{% extends "base.html" %}

{% block title %}About Us | Applicant Tracking System{% endblock %}

{% block header_title %}About Us{% endblock %}
{% block header_subtitle %}Learn More About Our Technology and Mission{% endblock %}

{% block description %}Learn about our mission, technology, and the team behind our Applicant Tracking System.{% endblock %}

{% block content %}
    <div class="about-section fade-in">
        <div class="about-card slide-in-left">
            <h2><i class="fas fa-info-circle"></i> Overview</h2>
            <p>Our Applicant Tracking System is designed to automate the hiring process by parsing CVs and extracting relevant information. We use cutting-edge AI and machine learning technologies to make recruitment faster, more efficient, and more accurate.</p>
            <p>Founded in 2023, our mission is to revolutionize the way companies find and hire talent by leveraging the power of artificial intelligence and natural language processing.</p>
        </div>

        <div class="about-card slide-in-right" style="animation-delay: 0.2s;">
            <h2><i class="fas fa-bullseye"></i> Objectives</h2>
            <ul class="staggered-animation">
                <li>Automate the hiring process by parsing CVs and extracting relevant information.</li>
                <li>Provide a user-friendly interface for applicants to upload their CVs.</li>
                <li>Allow recruiters to view and filter CVs based on skills, experience, and education.</li>
                <li>Reduce bias in the hiring process through objective data analysis.</li>
                <li>Save time and resources for HR departments and recruitment agencies.</li>
                <li>Improve the candidate experience through faster processing and feedback.</li>
            </ul>
        </div>

        <div class="about-card slide-in-left" style="animation-delay: 0.4s;">
            <h2><i class="fas fa-laptop-code"></i> Technologies Used</h2>
            <div class="tech-grid">
                <div class="tech-item">
                    <i class="fab fa-python"></i>
                    <span>Python</span>
                </div>
                <div class="tech-item">
                    <i class="fas fa-flask"></i>
                    <span>Flask</span>
                </div>
                <div class="tech-item">
                    <i class="fas fa-language"></i>
                    <span>NLTK (Natural Language Toolkit)</span>
                </div>
                <div class="tech-item">
                    <i class="fas fa-brain"></i>
                    <span>Machine Learning</span>
                </div>
                <div class="tech-item">
                    <i class="fas fa-file-alt"></i>
                    <span>Resume Parser APIs</span>
                </div>
                <div class="tech-item">
                    <i class="fas fa-database"></i>
                    <span>SQL Database</span>
                </div>
                <div class="tech-item">
                    <i class="fab fa-js"></i>
                    <span>JavaScript</span>
                </div>
                <div class="tech-item">
                    <i class="fab fa-html5"></i>
                    <span>HTML5/CSS3</span>
                </div>
            </div>
        </div>

        <div class="about-card slide-in-right" style="animation-delay: 0.6s;">
            <h2><i class="fas fa-users"></i> Our Team</h2>
            <div class="team-grid">
                <div class="team-member">
                    <div class="team-photo" style="background-color: var(--primary-color);">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3>Daudu John</h3>
                    <p>Founder & CEO</p>
                </div>
                <div class="team-member">
                    <div class="team-photo" style="background-color: var(--secondary-color);">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3>Ayomide Courage</h3>
                    <p>CTO</p>
                </div>
                <div class="team-member">
                    <div class="team-photo" style="background-color: var(--accent-color);">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3>Omoru Jeffery</h3>
                    <p>Lead Developer</p>
                </div>
                <div class="team-member">
                    <div class="team-photo" style="background-color: var(--success-color);">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3>Adebayo Philip</h3>
                    <p>AI Specialist</p>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_head %}
<style>
    .about-section {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .about-card {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .about-card h2 {
        color: var(--secondary-color);
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .about-card h2 i {
        color: var(--primary-color);
    }

    .about-card ul {
        padding-left: 20px;
    }

    .about-card li {
        margin-bottom: 10px;
    }

    .tech-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .tech-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        transition: transform 0.3s ease;
    }

    .tech-item:hover {
        transform: translateY(-5px);
        box-shadow: var(--box-shadow);
    }

    .tech-item i {
        font-size: 2.5rem;
        margin-bottom: 10px;
        color: var(--primary-color);
    }

    .team-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 30px;
        margin-top: 20px;
    }

    .team-member {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .team-photo {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 15px;
        color: white;
        font-size: 2.5rem;
        transition: transform 0.3s ease;
    }

    .team-member:hover .team-photo {
        transform: scale(1.1);
    }

    .team-member h3 {
        margin-bottom: 5px;
        color: var(--secondary-color);
    }

    .team-member p {
        color: #666;
        font-style: italic;
    }

    @media (max-width: 768px) {
        .tech-grid, .team-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 480px) {
        .tech-grid, .team-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}
