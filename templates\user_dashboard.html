{% extends "base.html" %}

{% block title %}User Dashboard | Applicant Tracking System{% endblock %}

{% block header_title %}My Dashboard{% endblock %}
{% block header_subtitle %}Manage your applications and profile{% endblock %}

{% block description %}View and manage your job applications, update your profile, and upload your CV.{% endblock %}

{% block content %}
    <div class="user-dashboard fade-in">
        <div class="dashboard-sidebar slide-in-left">
            <div class="user-profile">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-info">
                    <h3>{{ current_user.first_name }} {{ current_user.last_name }}</h3>
                    <p>{{ current_user.email }}</p>
                </div>
            </div>
            
            <nav class="dashboard-nav">
                <a href="#overview" class="dashboard-nav-item active" data-section="overview">
                    <i class="fas fa-home"></i> Overview
                </a>
                <a href="#applications" class="dashboard-nav-item" data-section="applications">
                    <i class="fas fa-file-alt"></i> My Applications
                </a>
                <a href="#cv" class="dashboard-nav-item" data-section="cv">
                    <i class="fas fa-file-upload"></i> Upload CV
                </a>
                <a href="#profile" class="dashboard-nav-item" data-section="profile">
                    <i class="fas fa-user-edit"></i> Edit Profile
                </a>
                <a href="#jobs" class="dashboard-nav-item" data-section="jobs">
                    <i class="fas fa-briefcase"></i> Job Listings
                </a>
                <a href="{{ url_for('logout') }}" class="dashboard-nav-item logout">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </nav>
        </div>
        
        <div class="dashboard-content slide-in-right">
            <!-- Overview Section -->
            <section id="overview" class="dashboard-section active">
                <h2><i class="fas fa-home"></i> Dashboard Overview</h2>
                
                <div class="welcome-card">
                    <h3>Welcome back, {{ current_user.first_name }}!</h3>
                    <p>Here's a summary of your activity and applications.</p>
                </div>
                
                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3>{{ stats.applications }}</h3>
                            <p>Applications</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-info">
                            <h3>{{ stats.views }}</h3>
                            <p>Profile Views</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3>{{ stats.interviews }}</h3>
                            <p>Interviews</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-info">
                            <h3>{{ stats.match_score }}%</h3>
                            <p>Avg. Match Score</p>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-cards">
                    <div class="dashboard-card">
                        <h3><i class="fas fa-file-alt"></i> Recent Applications</h3>
                        {% if recent_applications %}
                            <div class="application-list">
                                {% for application in recent_applications %}
                                <div class="application-item">
                                    <div class="application-company">
                                        <div class="company-logo">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="company-info">
                                            <h4>{{ application.job_title }}</h4>
                                            <p>{{ application.company }}</p>
                                        </div>
                                    </div>
                                    <div class="application-status">
                                        <span class="status-badge {{ application.status.lower() }}">{{ application.status }}</span>
                                        <p class="application-date">{{ application.date_applied }}</p>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <a href="#applications" class="view-all" data-section="applications">View all applications</a>
                        {% else %}
                            <div class="empty-state">
                                <i class="fas fa-file-alt"></i>
                                <p>You haven't submitted any applications yet.</p>
                                <a href="{{ url_for('job_listings') }}" class="btn-primary">Browse Jobs</a>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="dashboard-card">
                        <h3><i class="fas fa-briefcase"></i> Recommended Jobs</h3>
                        {% if recommended_jobs %}
                            <div class="job-list">
                                {% for job in recommended_jobs %}
                                <div class="job-item">
                                    <div class="job-company">
                                        <div class="company-logo">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="job-info">
                                            <h4>{{ job.title }}</h4>
                                            <p>{{ job.company }} • {{ job.location }}</p>
                                        </div>
                                    </div>
                                    <div class="job-match">
                                        <div class="match-score">
                                            <span>{{ job.match_score }}%</span>
                                            <div class="match-bar" style="width: {{ job.match_score }}%"></div>
                                        </div>
                                        <a href="{{ url_for('job_details', job_id=job.id) }}" class="btn-secondary">View</a>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <a href="{{ url_for('job_listings') }}" class="view-all">View all jobs</a>
                        {% else %}
                            <div class="empty-state">
                                <i class="fas fa-briefcase"></i>
                                <p>Upload your CV to get job recommendations.</p>
                                <a href="#cv" class="btn-primary" data-section="cv">Upload CV</a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </section>
            
            <!-- CV Upload Section -->
            <section id="cv" class="dashboard-section">
                <h2><i class="fas fa-file-upload"></i> Upload Your CV</h2>
                
                <div class="cv-upload-container">
                    <div class="cv-upload-card">
                        <h3>Upload a New CV</h3>
                        <p>Upload your CV in PDF or DOCX format to get instant analysis and job recommendations.</p>
                        
                        <form id="cv-upload-form" action="{{ url_for('upload_cv') }}" method="POST" enctype="multipart/form-data" class="upload-form">
                            <div class="file-upload-container">
                                <label for="cv-file" class="file-upload-label">
                                    <i class="fas fa-file-upload"></i>
                                    <span>Choose a file</span>
                                </label>
                                <input type="file" id="cv-file" name="file" class="file-upload-input">
                                <div class="file-name">No file chosen</div>
                            </div>
                            
                            <button type="submit" class="upload-button pulse">
                                <i class="fas fa-cloud-upload-alt"></i> Upload & Analyze CV
                            </button>
                        </form>
                    </div>
                    
                    <div class="cv-history-card">
                        <h3>CV History</h3>
                        {% if cv_history %}
                            <div class="cv-list">
                                {% for cv in cv_history %}
                                <div class="cv-item">
                                    <div class="cv-info">
                                        <i class="fas fa-file-pdf"></i>
                                        <div>
                                            <h4>{{ cv.filename }}</h4>
                                            <p>Uploaded on {{ cv.uploaded_at }}</p>
                                        </div>
                                    </div>
                                    <div class="cv-actions">
                                        <a href="{{ url_for('download_cv', cv_id=cv.id) }}" class="action-btn download" title="Download">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <a href="{{ url_for('view_cv_analysis', cv_id=cv.id) }}" class="action-btn view" title="View Analysis">
                                            <i class="fas fa-chart-bar"></i>
                                        </a>
                                        <button class="action-btn delete" title="Delete" data-cv-id="{{ cv.id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="empty-state">
                                <i class="fas fa-file-alt"></i>
                                <p>You haven't uploaded any CVs yet.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </section>
            
            <!-- Other sections would go here -->
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Navigation functionality
        const navItems = document.querySelectorAll('.dashboard-nav-item');
        const sections = document.querySelectorAll('.dashboard-section');
        
        navItems.forEach(item => {
            if (item.classList.contains('logout')) return;
            
            item.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all items and sections
                navItems.forEach(nav => nav.classList.remove('active'));
                sections.forEach(section => section.classList.remove('active'));
                
                // Add active class to clicked item
                this.classList.add('active');
                
                // Show corresponding section
                const sectionId = this.getAttribute('data-section');
                document.getElementById(sectionId).classList.add('active');
            });
        });
        
        // Section links within the dashboard
        const sectionLinks = document.querySelectorAll('[data-section]');
        sectionLinks.forEach(link => {
            if (link.classList.contains('dashboard-nav-item')) return;
            
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                const sectionId = this.getAttribute('data-section');
                const navItem = document.querySelector(`.dashboard-nav-item[data-section="${sectionId}"]`);
                
                if (navItem) {
                    navItem.click();
                }
            });
        });
        
        // File upload functionality
        const fileInput = document.getElementById('cv-file');
        const fileName = document.querySelector('.file-name');
        
        if (fileInput && fileName) {
            fileInput.addEventListener('change', function() {
                if (fileInput.files.length > 0) {
                    fileName.textContent = fileInput.files[0].name;
                    fileName.classList.add('file-selected');
                } else {
                    fileName.textContent = 'No file chosen';
                    fileName.classList.remove('file-selected');
                }
            });
        }
    });
</script>
{% endblock %}

{% block extra_head %}
<style>
    .user-dashboard {
        display: grid;
        grid-template-columns: 250px 1fr;
        gap: 30px;
        min-height: calc(100vh - 200px);
    }
    
    .dashboard-sidebar {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 20px 0;
    }
    
    .user-profile {
        display: flex;
        align-items: center;
        padding: 0 20px 20px;
        border-bottom: 1px solid #eee;
        margin-bottom: 20px;
    }
    
    .user-avatar {
        width: 50px;
        height: 50px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-right: 15px;
    }
    
    .user-info h3 {
        margin: 0;
        font-size: 1.1rem;
        color: var(--secondary-color);
    }
    
    .user-info p {
        margin: 5px 0 0;
        font-size: 0.9rem;
        color: #666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 150px;
    }
    
    .dashboard-nav {
        display: flex;
        flex-direction: column;
    }
    
    .dashboard-nav-item {
        padding: 12px 20px;
        color: #333;
        text-decoration: none;
        display: flex;
        align-items: center;
        transition: var(--transition);
    }
    
    .dashboard-nav-item i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }
    
    .dashboard-nav-item:hover {
        background-color: rgba(52, 152, 219, 0.1);
        color: var(--primary-color);
    }
    
    .dashboard-nav-item.active {
        background-color: var(--primary-color);
        color: white;
    }
    
    .dashboard-nav-item.logout {
        margin-top: auto;
        border-top: 1px solid #eee;
        color: #e74c3c;
    }
    
    .dashboard-content {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 30px;
    }
    
    .dashboard-section {
        display: none;
    }
    
    .dashboard-section.active {
        display: block;
        animation: fadeIn 0.5s ease;
    }
    
    .dashboard-section h2 {
        color: var(--secondary-color);
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .welcome-card {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .welcome-card h3 {
        margin-top: 0;
        color: var(--secondary-color);
    }
    
    .welcome-card p {
        margin-bottom: 0;
        color: #666;
    }
    
    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
        display: flex;
        align-items: center;
        transition: var(--transition);
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-right: 15px;
    }
    
    .stat-info h3 {
        margin: 0;
        font-size: 1.5rem;
        color: var(--secondary-color);
    }
    
    .stat-info p {
        margin: 5px 0 0;
        font-size: 0.9rem;
        color: #666;
    }
    
    .dashboard-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }
    
    .dashboard-card {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
    }
    
    .dashboard-card h3 {
        margin-top: 0;
        margin-bottom: 20px;
        color: var(--secondary-color);
        font-size: 1.2rem;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .application-list, .job-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .application-item, .job-item {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        transition: var(--transition);
    }
    
    .application-item:hover, .job-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .application-company, .job-company {
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .company-logo {
        width: 40px;
        height: 40px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }
    
    .company-info h4, .job-info h4 {
        margin: 0;
        font-size: 1rem;
        color: var(--secondary-color);
    }
    
    .company-info p, .job-info p {
        margin: 5px 0 0;
        font-size: 0.8rem;
        color: #666;
    }
    
    .application-status {
        text-align: right;
    }
    
    .status-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-badge.applied {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-badge.screening {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    
    .status-badge.interview {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-badge.rejected {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .application-date {
        margin: 5px 0 0;
        font-size: 0.8rem;
        color: #999;
    }
    
    .job-match {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 10px;
    }
    
    .match-score {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
    }
    
    .match-score span {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--secondary-color);
    }
    
    .match-bar {
        height: 5px;
        background-color: var(--primary-color);
        border-radius: 5px;
    }
    
    .view-all {
        display: block;
        text-align: center;
        margin-top: 20px;
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        transition: var(--transition);
    }
    
    .view-all:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }
    
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 30px 0;
        color: #aaa;
        text-align: center;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #ddd;
    }
    
    .empty-state p {
        margin-bottom: 15px;
    }
    
    .btn-primary, .btn-secondary {
        display: inline-block;
        padding: 8px 15px;
        border-radius: var(--border-radius);
        text-decoration: none;
        transition: var(--transition);
        font-size: 0.9rem;
        font-weight: 500;
    }
    
    .btn-primary {
        background-color: var(--primary-color);
        color: white;
    }
    
    .btn-primary:hover {
        background-color: var(--secondary-color);
    }
    
    .btn-secondary {
        background-color: rgba(52, 152, 219, 0.1);
        color: var(--primary-color);
    }
    
    .btn-secondary:hover {
        background-color: rgba(52, 152, 219, 0.2);
    }
    
    .cv-upload-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }
    
    .cv-upload-card, .cv-history-card {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
    }
    
    .cv-upload-card h3, .cv-history-card h3 {
        margin-top: 0;
        margin-bottom: 15px;
        color: var(--secondary-color);
    }
    
    .file-upload-container {
        margin: 20px 0;
    }
    
    .file-upload-label {
        display: inline-block;
        padding: 12px 20px;
        background-color: white;
        color: var(--secondary-color);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: var(--transition);
    }
    
    .file-upload-label:hover {
        background-color: #eee;
    }
    
    .file-upload-label i {
        margin-right: 10px;
    }
    
    .file-upload-input {
        display: none;
    }
    
    .file-name {
        margin-top: 10px;
        font-size: 0.9rem;
        color: #666;
    }
    
    .file-selected {
        color: var(--primary-color);
        font-weight: 600;
    }
    
    .upload-button {
        display: block;
        width: 100%;
        padding: 12px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
    }
    
    .upload-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .cv-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .cv-item {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    
    .cv-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .cv-info i {
        font-size: 1.5rem;
        color: #e74c3c;
    }
    
    .cv-info h4 {
        margin: 0;
        font-size: 1rem;
        color: var(--secondary-color);
    }
    
    .cv-info p {
        margin: 5px 0 0;
        font-size: 0.8rem;
        color: #999;
    }
    
    .cv-actions {
        display: flex;
        gap: 5px;
    }
    
    .action-btn {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition);
        text-decoration: none;
    }
    
    .action-btn.download {
        background-color: rgba(52, 152, 219, 0.1);
        color: var(--primary-color);
    }
    
    .action-btn.view {
        background-color: rgba(39, 174, 96, 0.1);
        color: #27ae60;
    }
    
    .action-btn.delete {
        background-color: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }
    
    .action-btn:hover {
        transform: scale(1.1);
    }
    
    @media (max-width: 992px) {
        .user-dashboard {
            grid-template-columns: 1fr;
        }
        
        .dashboard-sidebar {
            display: none;
        }
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
</style>
{% endblock %}
