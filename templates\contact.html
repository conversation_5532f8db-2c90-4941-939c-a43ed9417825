{% extends "base.html" %}

{% block title %}Contact Us | Applicant Tracking System{% endblock %}

{% block header_title %}Contact Us{% endblock %}
{% block header_subtitle %}Get in Touch with Our Team{% endblock %}

{% block description %}Contact our team for support, inquiries, or feedback about our Applicant Tracking System.{% endblock %}

{% block content %}
    <div class="contact-section fade-in">
        <div class="contact-info slide-in-left">
            <h2><i class="fas fa-info-circle"></i> Contact Information</h2>

            <div class="contact-details">
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="contact-text">
                        <h3>Address</h3>
                        <p>123 Tech Street, Suite 456<br>San Francisco, CA 94107</p>
                    </div>
                </div>

                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="contact-text">
                        <h3>Phone</h3>
                        <p>+****************</p>
                    </div>
                </div>

                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="contact-text">
                        <h3>Email</h3>
                        <p><EMAIL></p>
                    </div>
                </div>

                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="contact-text">
                        <h3>Business Hours</h3>
                        <p>Monday - Friday: 9am - 5pm<br>Saturday & Sunday: Closed</p>
                    </div>
                </div>
            </div>

            <div class="social-media">
                <h3><i class="fas fa-share-alt"></i> Follow Us</h3>
                <div class="social-icons">
                    <a href="#" class="social-icon bounce-in" style="animation-delay: 0.1s;">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="social-icon bounce-in" style="animation-delay: 0.2s;">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="social-icon bounce-in" style="animation-delay: 0.3s;">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="#" class="social-icon bounce-in" style="animation-delay: 0.4s;">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="contact-form-container slide-in-right">
            <h2><i class="fas fa-paper-plane"></i> Send Us a Message</h2>

            <form id="contact-form" action="{{ url_for('contact') }}" method="POST" class="contact-form">
                <div class="form-group">
                    <label for="name">Your Name</label>
                    <input type="text" id="name" name="name" required class="form-control">
                </div>

                <div class="form-group">
                    <label for="email">Your Email</label>
                    <input type="email" id="email" name="email" required class="form-control">
                </div>

                <div class="form-group">
                    <label for="subject">Subject</label>
                    <select id="subject" name="subject" class="form-control">
                        <option value="general">General Inquiry</option>
                        <option value="support">Technical Support</option>
                        <option value="sales">Sales Information</option>
                        <option value="feedback">Feedback</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="message">Your Message</label>
                    <textarea id="message" name="message" required class="form-control" rows="5"></textarea>
                </div>

                <button type="submit" class="submit-button pulse">
                    <i class="fas fa-paper-plane"></i> Send Message
                </button>
            </form>

            {% if message_sent %}
            <div class="alert alert-success bounce-in">
                <i class="fas fa-check-circle"></i> {{ message_sent }}
                {% if submission_time %}
                <p class="submission-time">Submitted on {{ submission_time }}</p>
                {% endif %}
            </div>
            {% endif %}

            {% if error %}
            <div class="alert alert-error shake">
                <i class="fas fa-exclamation-circle"></i> {{ error }}
            </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const contactForm = document.getElementById('contact-form');

        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                const nameInput = document.getElementById('name');
                const emailInput = document.getElementById('email');
                const messageInput = document.getElementById('message');

                let isValid = true;

                // Simple validation
                if (!nameInput.value.trim()) {
                    nameInput.classList.add('input-error');
                    isValid = false;
                } else {
                    nameInput.classList.remove('input-error');
                }

                if (!emailInput.value.trim() || !emailInput.value.includes('@')) {
                    emailInput.classList.add('input-error');
                    isValid = false;
                } else {
                    emailInput.classList.remove('input-error');
                }

                if (!messageInput.value.trim()) {
                    messageInput.classList.add('input-error');
                    isValid = false;
                } else {
                    messageInput.classList.remove('input-error');
                }

                if (!isValid) {
                    e.preventDefault();

                    // Show error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'alert alert-error shake';
                    errorDiv.innerHTML = '<i class="fas fa-exclamation-circle"></i> Please fill out all required fields correctly.';

                    // Remove any existing error messages
                    const existingError = contactForm.querySelector('.alert-error');
                    if (existingError) {
                        existingError.remove();
                    }

                    // Add the error message to the form
                    contactForm.appendChild(errorDiv);
                }
            });
        }
    });
</script>
{% endblock %}

{% block extra_head %}
<style>
    .contact-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }

    .contact-info, .contact-form-container {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .contact-info h2, .contact-form-container h2 {
        color: var(--secondary-color);
        margin-bottom: 30px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .contact-info h2 i, .contact-form-container h2 i {
        color: var(--primary-color);
    }

    .contact-details {
        margin-bottom: 30px;
    }

    .contact-item {
        display: flex;
        align-items: flex-start;
        gap: 20px;
        margin-bottom: 20px;
    }

    .contact-icon {
        width: 50px;
        height: 50px;
        background-color: rgba(52, 152, 219, 0.1);
        color: var(--primary-color);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .contact-text h3 {
        margin-bottom: 5px;
        color: var(--secondary-color);
        font-size: 1.1rem;
    }

    .contact-text p {
        color: #666;
        line-height: 1.5;
    }

    .social-media h3 {
        margin-bottom: 15px;
        color: var(--secondary-color);
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .social-media h3 i {
        color: var(--primary-color);
    }

    .social-icons {
        display: flex;
        gap: 15px;
    }

    .social-icon {
        width: 40px;
        height: 40px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        text-decoration: none;
        transition: var(--transition);
    }

    .social-icon:hover {
        transform: translateY(-5px);
        background-color: var(--secondary-color);
    }

    .contact-form {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .form-group label {
        font-weight: 600;
        color: var(--secondary-color);
    }

    .form-control {
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-family: var(--font-main);
        font-size: 1rem;
        transition: var(--transition);
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
    }

    .input-error {
        border-color: var(--error-color) !important;
        box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.2) !important;
    }

    .submit-button {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        padding: 15px;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
    }

    .submit-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .submission-time {
        font-size: 0.8rem;
        margin-top: 5px;
        color: #666;
    }

    @media (max-width: 768px) {
        .contact-section {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}
