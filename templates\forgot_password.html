{% extends "base.html" %}

{% block title %}Forgot Password | Applicant Tracking System{% endblock %}

{% block header_title %}Forgot Password{% endblock %}
{% block header_subtitle %}Reset your password{% endblock %}

{% block description %}Reset your password to regain access to your account.{% endblock %}

{% block content %}
    <div class="auth-container fade-in">
        <div class="auth-card slide-in-bottom">
            <h2><i class="fas fa-key"></i> Reset Your Password</h2>
            <p>Enter your email address below and we'll send you instructions to reset your password.</p>
            
            <form id="forgot-password-form" action="{{ url_for('forgot_password') }}" method="POST" class="auth-form">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <div class="input-with-icon">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="email" name="email" placeholder="Enter your email address" required>
                    </div>
                </div>
                
                <button type="submit" class="auth-button pulse">
                    <i class="fas fa-paper-plane"></i> Send Reset Instructions
                </button>
            </form>
            
            <div class="auth-links">
                <a href="{{ url_for('login') }}" class="login-link">Remember your password? Login</a>
            </div>
            
            {% if error %}
            <div class="alert alert-error shake">
                <i class="fas fa-exclamation-circle"></i> {{ error }}
            </div>
            {% endif %}
            
            {% if success %}
            <div class="alert alert-success bounce-in">
                <i class="fas fa-check-circle"></i> {{ success }}
            </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_head %}
<style>
    .auth-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px 20px;
    }
    
    .auth-card {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        width: 100%;
        max-width: 500px;
        padding: 30px;
    }
    
    .auth-card h2 {
        color: var(--secondary-color);
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .auth-card p {
        color: #666;
        margin-bottom: 25px;
    }
    
    .auth-form {
        margin-top: 20px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }
    
    .input-with-icon {
        position: relative;
    }
    
    .input-with-icon i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #aaa;
    }
    
    .input-with-icon input {
        width: 100%;
        padding: 12px 15px 12px 45px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-size: 1rem;
        transition: var(--transition);
    }
    
    .input-with-icon input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        outline: none;
    }
    
    .auth-button {
        display: block;
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        margin-top: 30px;
    }
    
    .auth-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .auth-links {
        margin-top: 25px;
        text-align: center;
        font-size: 0.9rem;
    }
    
    .auth-links a {
        color: var(--primary-color);
        text-decoration: none;
        transition: var(--transition);
    }
    
    .auth-links a:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }
    
    .alert {
        margin-top: 20px;
        padding: 15px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .alert-error {
        background-color: #ffebee;
        color: #c62828;
    }
    
    .alert-success {
        background-color: #e8f5e9;
        color: #2e7d32;
    }
</style>
{% endblock %}
