{% extends "base.html" %}

{% block title %}Resume Scanner | JobScan-like Tool{% endblock %}

{% block header_title %}Resume Scanner{% endblock %}
{% block header_subtitle %}Optimize Your Resume for Job Applications{% endblock %}

{% block description %}Upload your resume and paste a job description to get a match score and optimization suggestions.{% endblock %}

{% block content %}
    <div class="scanner-section fade-in">
        <div class="scanner-intro slide-in-down">
            <h2><i class="fas fa-search"></i> Resume Optimization Tool</h2>
            <p>Our resume scanner analyzes your resume against a job description to help you optimize your application. Get a match score and personalized suggestions to improve your chances of getting an interview.</p>

            <div class="scanner-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>Upload Your Resume</h3>
                        <p>Upload your resume in PDF or DOCX format.</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Paste Job Description</h3>
                        <p>Copy and paste the full job description.</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Get Analysis</h3>
                        <p>Receive a match score and optimization suggestions.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="scanner-form-container slide-in-up">
            <form id="scanner-form" action="{{ url_for('resume_scanner') }}" method="POST" enctype="multipart/form-data" class="scanner-form">
                <div class="form-group">
                    <label for="resume"><i class="fas fa-file-alt"></i> Upload Your Resume</label>
                    <div class="file-upload-container">
                        <label for="resume" class="file-upload-label">
                            <i class="fas fa-file-upload"></i>
                            <span>Choose a file</span>
                        </label>
                        <input type="file" id="resume" name="resume" class="file-upload-input">
                        <div class="file-name">No file chosen</div>
                    </div>
                    <p class="form-hint">Accepted formats: PDF, DOCX</p>
                </div>

                <div class="form-group">
                    <label for="job_description"><i class="fas fa-briefcase"></i> Job Description</label>
                    <textarea id="job_description" name="job_description" rows="10" placeholder="Paste the full job description here..."></textarea>
                    <p class="form-hint">For best results, include the entire job description.</p>
                </div>

                <button type="submit" class="scanner-button pulse">
                    <i class="fas fa-search"></i> Analyze Resume
                </button>
            </form>

            <div id="scanner-status" class="scanner-status">
                {% if error %}
                <div class="alert alert-error shake">
                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                </div>
                {% endif %}
                {% if success %}
                <div class="alert alert-success bounce-in">
                    <i class="fas fa-check-circle"></i> {{ success }}
                </div>
                {% endif %}
            </div>
        </div>

        {% if analysis_result %}
        <div class="analysis-container scale-in">
            <div class="analysis-header">
                <h2><i class="fas fa-chart-pie"></i> Analysis Results</h2>
                <p class="analysis-date">Analysis Date: {{ analysis_result.analysis_date }}</p>
            </div>

            <div class="score-container">
                <div class="score-circle" data-score="{{ analysis_result.match_score }}">
                    <div class="score-value">{{ analysis_result.match_score }}%</div>
                    <div class="score-label">Keyword Match</div>
                </div>

                <div class="score-circle ml-score" data-score="{{ analysis_result.ml_prediction.fit_score }}">
                    <div class="score-value">{{ analysis_result.ml_prediction.fit_score }}%</div>
                    <div class="score-label">AI Fit Score</div>
                </div>

                <div class="score-details">
                    {% if analysis_result.matched_keywords is iterable and analysis_result.matched_keywords is not string %}
                        <p><strong>{{ analysis_result.matched_keywords|length }}</strong> matched keywords out of <strong>{{ analysis_result.job_keywords|length }}</strong> job requirements</p>
                    {% else %}
                        <p><strong>0</strong> matched keywords out of <strong>0</strong> job requirements</p>
                    {% endif %}

                    {% if analysis_result.missing_keywords is iterable and analysis_result.missing_keywords is not string %}
                        <p><strong>{{ analysis_result.missing_keywords|length }}</strong> missing keywords</p>
                    {% else %}
                        <p><strong>0</strong> missing keywords</p>
                    {% endif %}

                    <p class="ml-prediction">AI Prediction: <strong>{{ analysis_result.ml_prediction.fit_label }}</strong> ({{ analysis_result.ml_prediction.confidence }}% confidence)</p>
                </div>
            </div>

            <div class="keywords-section">
                <div class="keywords-column">
                    <h3><i class="fas fa-check-circle"></i> Matched Keywords</h3>
                    <div class="keywords-list matched">
                        {% if analysis_result.matched_keywords is iterable and analysis_result.matched_keywords is not string %}
                            {% for keyword in analysis_result.matched_keywords %}
                                <span class="keyword-tag matched">{{ keyword }}</span>
                            {% else %}
                                <p class="no-keywords">No matched keywords found.</p>
                            {% endfor %}
                        {% else %}
                            <p class="no-keywords">No matched keywords found.</p>
                        {% endif %}
                    </div>
                </div>
                <div class="keywords-column">
                    <h3><i class="fas fa-times-circle"></i> Missing Keywords</h3>
                    <div class="keywords-list missing">
                        {% if analysis_result.missing_keywords is iterable and analysis_result.missing_keywords is not string %}
                            {% for keyword in analysis_result.missing_keywords %}
                                <span class="keyword-tag missing">{{ keyword }}</span>
                            {% else %}
                                <p class="no-keywords">No missing keywords found.</p>
                            {% endfor %}
                        {% else %}
                            <p class="no-keywords">No missing keywords found.</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="suggestions-section">
                <h3><i class="fas fa-lightbulb"></i> Optimization Suggestions</h3>
                {% if analysis_result.suggestions is iterable and analysis_result.suggestions is not string %}
                    {% for suggestion in analysis_result.suggestions %}
                        <div class="suggestion-card">
                            <h4>{{ suggestion.title }}</h4>
                            <p>{{ suggestion.description }}</p>
                            <ul class="suggestion-list">
                                {% if suggestion.items is defined and suggestion.items is iterable and suggestion.items is not string %}
                                    {% for item in suggestion.items %}
                                        <li>{{ item }}</li>
                                    {% endfor %}
                                {% endif %}
                            </ul>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>

            <!-- CV Information Section -->
            {% if analysis_result.cv_info %}
            <div class="cv-info-section">
                <h3><i class="fas fa-user-circle"></i> Extracted CV Information</h3>

                <!-- Personal Information -->
                {% if analysis_result.cv_info.personal_info %}
                <div class="info-card">
                    <h4><i class="fas fa-user"></i> Personal Information</h4>
                    <div class="info-grid">
                        {% if analysis_result.cv_info.personal_info.name %}
                        <div class="info-item">
                            <strong>Name:</strong> {{ analysis_result.cv_info.personal_info.name }}
                        </div>
                        {% endif %}
                        {% if analysis_result.cv_info.personal_info.email %}
                        <div class="info-item">
                            <strong>Email:</strong> {{ analysis_result.cv_info.personal_info.email }}
                        </div>
                        {% endif %}
                        {% if analysis_result.cv_info.personal_info.phone %}
                        <div class="info-item">
                            <strong>Phone:</strong> {{ analysis_result.cv_info.personal_info.phone }}
                        </div>
                        {% endif %}
                        {% if analysis_result.cv_info.personal_info.linkedin %}
                        <div class="info-item">
                            <strong>LinkedIn:</strong> <a href="{{ analysis_result.cv_info.personal_info.linkedin }}" target="_blank">{{ analysis_result.cv_info.personal_info.linkedin }}</a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Educational Background -->
                {% if analysis_result.cv_info.educational_background %}
                <div class="info-card">
                    <h4><i class="fas fa-graduation-cap"></i> Educational Background</h4>
                    {% for edu in analysis_result.cv_info.educational_background %}
                    <div class="education-item">
                        <div class="edu-header">
                            <strong>{{ edu.degree or 'Degree' }}</strong>
                            {% if edu.field %} in {{ edu.field }}{% endif %}
                        </div>
                        {% if edu.institution %}
                        <div class="edu-institution">{{ edu.institution }}</div>
                        {% endif %}
                        <div class="edu-details">
                            {% if edu.graduation_year %}
                            <span class="edu-year">{{ edu.graduation_year }}</span>
                            {% endif %}
                            {% if edu.grade %}
                            <span class="edu-grade">{{ edu.grade }}</span>
                            {% endif %}
                            {% if edu.gpa %}
                            <span class="edu-gpa">GPA: {{ edu.gpa }}</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Work Experience -->
                {% if analysis_result.cv_info.work_experience %}
                <div class="info-card">
                    <h4><i class="fas fa-briefcase"></i> Work Experience</h4>
                    {% for exp in analysis_result.cv_info.work_experience %}
                    <div class="experience-item">
                        <div class="exp-header">
                            <strong>{{ exp.job_title or 'Position' }}</strong>
                            {% if exp.company %} at {{ exp.company }}{% endif %}
                        </div>
                        {% if exp.duration %}
                        <div class="exp-duration">{{ exp.duration }}</div>
                        {% endif %}
                        {% if exp.responsibilities %}
                        <div class="exp-responsibilities">
                            <strong>Key Responsibilities:</strong>
                            <ul>
                                {% for resp in exp.responsibilities[:3] %}
                                <li>{{ resp }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Technical Skills -->
                {% if analysis_result.cv_info.technical_skills %}
                <div class="info-card">
                    <h4><i class="fas fa-code"></i> Technical Skills</h4>
                    <div class="skills-grid">
                        {% if analysis_result.cv_info.technical_skills.programming_languages %}
                        <div class="skill-category">
                            <strong>Programming Languages:</strong>
                            <div class="skill-tags">
                                {% for skill in analysis_result.cv_info.technical_skills.programming_languages %}
                                <span class="skill-tag">{{ skill }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        {% if analysis_result.cv_info.technical_skills.frameworks %}
                        <div class="skill-category">
                            <strong>Frameworks:</strong>
                            <div class="skill-tags">
                                {% for skill in analysis_result.cv_info.technical_skills.frameworks %}
                                <span class="skill-tag">{{ skill }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        {% if analysis_result.cv_info.technical_skills.databases %}
                        <div class="skill-category">
                            <strong>Databases:</strong>
                            <div class="skill-tags">
                                {% for skill in analysis_result.cv_info.technical_skills.databases %}
                                <span class="skill-tag">{{ skill }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        {% if analysis_result.cv_info.technical_skills.tools %}
                        <div class="skill-category">
                            <strong>Tools:</strong>
                            <div class="skill-tags">
                                {% for skill in analysis_result.cv_info.technical_skills.tools %}
                                <span class="skill-tag">{{ skill }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Projects -->
                {% if analysis_result.cv_info.projects %}
                <div class="info-card">
                    <h4><i class="fas fa-project-diagram"></i> Projects</h4>
                    {% for project in analysis_result.cv_info.projects %}
                    <div class="project-item">
                        <div class="project-header">
                            <strong>{{ project.name or 'Project' }}</strong>
                        </div>
                        {% if project.description %}
                        <div class="project-description">{{ project.description }}</div>
                        {% endif %}
                        {% if project.technologies %}
                        <div class="project-tech">
                            <strong>Technologies:</strong>
                            {% for tech in project.technologies %}
                            <span class="tech-tag">{{ tech }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% endif %}

            <div class="action-buttons">
                <button class="action-button" onclick="window.print()">
                    <i class="fas fa-print"></i> Print Results
                </button>
                <a href="{{ url_for('resume_scanner') }}" class="action-button">
                    <i class="fas fa-redo"></i> New Analysis
                </a>
                {% if analysis_result.cv_info %}
                <a href="{{ url_for('job_listings') }}" class="action-button primary">
                    <i class="fas fa-search"></i> Find Matching Jobs
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // State management functions
        function checkAnalysisStatus() {
            fetch('/api/get-analysis-status')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.has_analysis && data.last_analysis) {
                        showAnalysisStatusBanner(data.last_analysis);
                    }
                })
                .catch(error => {
                    console.error('Error checking analysis status:', error);
                });
        }

        function showAnalysisStatusBanner(lastAnalysis) {
            const banner = document.createElement('div');
            banner.className = 'analysis-status-banner';
            banner.innerHTML = `
                <div class="banner-content">
                    <i class="fas fa-info-circle"></i>
                    <span>Previous analysis available: ${lastAnalysis.resume_filename}
                    (${lastAnalysis.match_score}% match, ${lastAnalysis.extracted_skills_count} skills detected)</span>
                    <button class="clear-analysis-btn" onclick="clearAnalysisState()">
                        <i class="fas fa-times"></i> Clear & Start Fresh
                    </button>
                </div>
            `;

            const container = document.querySelector('.container');
            if (container && !document.querySelector('.analysis-status-banner')) {
                container.insertBefore(banner, container.firstChild);
            }
        }

        window.clearAnalysisState = function() {
            if (confirm('This will clear your previous analysis. Are you sure?')) {
                fetch('/api/clear-analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        location.reload(); // Refresh the page to show clean state
                    } else {
                        alert('Error clearing analysis: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error clearing analysis:', error);
                    alert('Error clearing analysis. Please try again.');
                });
            }
        };

        // Check for existing analysis on page load
        {% if not analysis_result %}
        checkAnalysisStatus();
        {% endif %}

        // Update file name display when file is selected
        const fileInput = document.getElementById('resume');
        const fileName = document.querySelector('.file-name');

        if (fileInput && fileName) {
            fileInput.addEventListener('change', function() {
                if (fileInput.files.length > 0) {
                    fileName.textContent = fileInput.files[0].name;
                    fileName.classList.add('file-selected');
                } else {
                    fileName.textContent = 'No file chosen';
                    fileName.classList.remove('file-selected');
                }
            });
        }

        // Function to get color based on score
        function getScoreColor(score) {
            if (score >= 80) return '#4caf50'; // Green
            if (score >= 60) return '#8bc34a'; // Light Green
            if (score >= 40) return '#ffc107'; // Yellow
            if (score >= 20) return '#ff9800'; // Orange
            return '#f44336'; // Red
        }

        // Function to animate a score circle
        function animateScoreCircle(circleElement, labelText) {
            const score = parseFloat(circleElement.getAttribute('data-score'));
            const circumference = 2 * Math.PI * 45; // 45 is the radius

            // Add SVG for circle animation
            circleElement.innerHTML = `
                <svg width="120" height="120" viewBox="0 0 120 120">
                    <circle cx="60" cy="60" r="45" fill="none" stroke="#e0e0e0" stroke-width="10" />
                    <circle cx="60" cy="60" r="45" fill="none" stroke="${getScoreColor(score)}" stroke-width="10"
                            stroke-dasharray="${circumference}" stroke-dashoffset="${circumference - (score / 100) * circumference}"
                            transform="rotate(-90 60 60)" />
                </svg>
                <div class="score-value">${score}%</div>
                <div class="score-label">${labelText}</div>
            `;

            // Animate the score circle
            setTimeout(() => {
                const circle = circleElement.querySelector('circle:nth-child(2)');
                circle.style.transition = 'stroke-dashoffset 1.5s ease-in-out';
                circle.style.strokeDashoffset = circumference - (score / 100) * circumference;
            }, 100);
        }

        // Animate keyword match score circle
        const keywordScoreCircle = document.querySelector('.score-circle:not(.ml-score)');
        if (keywordScoreCircle) {
            animateScoreCircle(keywordScoreCircle, 'Keyword Match');
        }

        // Animate ML fit score circle
        const mlScoreCircle = document.querySelector('.score-circle.ml-score');
        if (mlScoreCircle) {
            animateScoreCircle(mlScoreCircle, 'AI Fit Score');
        }
    });
</script>
{% endblock %}

{% block extra_head %}
<style>
    .scanner-section {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .scanner-intro {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .scanner-intro h2 {
        color: var(--secondary-color);
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .scanner-intro h2 i {
        color: var(--primary-color);
    }

    .scanner-steps {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
        flex-wrap: wrap;
        gap: 20px;
    }

    .step {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        flex: 1;
        min-width: 250px;
    }

    .step-number {
        width: 40px;
        height: 40px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        font-size: 1.2rem;
    }

    .step-content h3 {
        margin: 0 0 10px 0;
        color: var(--secondary-color);
    }

    .scanner-form-container {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .scanner-form {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .form-group label {
        font-weight: 600;
        color: var(--secondary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-group label i {
        color: var(--primary-color);
    }

    .form-hint {
        font-size: 0.8rem;
        color: #666;
        margin-top: 5px;
    }

    textarea {
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        resize: vertical;
        font-family: inherit;
        font-size: 1rem;
    }

    .scanner-button {
        display: block;
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
    }

    .scanner-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .scanner-status {
        margin-top: 20px;
    }

    .analysis-container {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .analysis-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--primary-color);
    }

    .analysis-header h2 {
        color: var(--secondary-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .analysis-header h2 i {
        color: var(--primary-color);
    }

    .analysis-date {
        color: #666;
        font-size: 0.9rem;
    }

    .score-container {
        display: flex;
        align-items: center;
        gap: 30px;
        margin-bottom: 30px;
        flex-wrap: wrap;
    }

    .score-circle {
        position: relative;
        width: 120px;
        height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .score-circle.ml-score {
        background-color: rgba(0, 123, 255, 0.05);
        border-radius: 50%;
        padding: 5px;
    }

    .score-value {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--secondary-color);
        position: absolute;
    }

    .score-label {
        font-size: 0.9rem;
        color: #666;
        margin-top: 30px;
        position: absolute;
    }

    .score-details {
        flex: 1;
        min-width: 250px;
    }

    .score-details p {
        margin: 10px 0;
    }

    .ml-prediction {
        margin-top: 15px !important;
        padding: 10px;
        background-color: rgba(0, 123, 255, 0.05);
        border-radius: 5px;
        border-left: 3px solid #007bff;
    }

    .keywords-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }

    .keywords-column h3 {
        color: var(--secondary-color);
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .keywords-column h3 i {
        color: var(--primary-color);
    }

    .keywords-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        min-height: 100px;
    }

    .keyword-tag {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.9rem;
    }

    .keyword-tag.matched {
        background-color: rgba(76, 175, 80, 0.1);
        color: #4caf50;
    }

    .keyword-tag.missing {
        background-color: rgba(244, 67, 54, 0.1);
        color: #f44336;
    }

    .no-keywords {
        color: #999;
        font-style: italic;
    }

    .suggestions-section {
        margin-bottom: 30px;
    }

    .suggestions-section h3 {
        color: var(--secondary-color);
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .suggestions-section h3 i {
        color: var(--primary-color);
    }

    .suggestion-card {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
        margin-bottom: 20px;
    }

    .suggestion-card h4 {
        color: var(--secondary-color);
        margin-top: 0;
        margin-bottom: 10px;
    }

    .suggestion-list {
        padding-left: 20px;
    }

    .suggestion-list li {
        margin-bottom: 5px;
    }

    .action-buttons {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .action-button {
        padding: 12px 20px;
        background-color: var(--light-color);
        color: var(--secondary-color);
        border: none;
        border-radius: var(--border-radius);
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 10px;
        text-decoration: none;
    }

    .action-button:hover {
        background-color: var(--primary-color);
        color: white;
    }

    .action-button.primary {
        background-color: #28a745;
    }

    .action-button.primary:hover {
        background-color: #218838;
    }

    /* CV Information Styles */
    .cv-info-section {
        margin-top: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
        border: 1px solid #dee2e6;
    }

    .cv-info-section h3 {
        color: #495057;
        margin-bottom: 20px;
        font-size: 1.4em;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
    }

    .info-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #007bff;
    }

    .info-card h4 {
        color: #007bff;
        margin-bottom: 15px;
        font-size: 1.2em;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }

    .info-item {
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }

    .education-item, .experience-item, .project-item {
        padding: 15px;
        border: 1px solid #e9ecef;
        border-radius: 5px;
        margin-bottom: 15px;
        background-color: #fdfdfd;
    }

    .edu-header, .exp-header, .project-header {
        font-size: 1.1em;
        color: #495057;
        margin-bottom: 8px;
    }

    .edu-institution, .exp-duration, .project-description {
        color: #6c757d;
        margin-bottom: 8px;
    }

    .edu-details {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .edu-year, .edu-grade, .edu-gpa {
        background-color: #e9ecef;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.9em;
    }

    .exp-responsibilities ul {
        margin: 8px 0;
        padding-left: 20px;
    }

    .exp-responsibilities li {
        margin-bottom: 5px;
        color: #495057;
    }

    .skills-grid {
        display: grid;
        gap: 15px;
    }

    .skill-category {
        margin-bottom: 15px;
    }

    .skill-tags, .project-tech {
        margin-top: 8px;
    }

    .skill-tag, .tech-tag {
        display: inline-block;
        background-color: #007bff;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.85em;
        margin: 2px 4px 2px 0;
    }

    .tech-tag {
        background-color: #28a745;
    }

    /* Analysis Status Banner */
    .analysis-status-banner {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        animation: slideInDown 0.5s ease-out;
    }

    .banner-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 10px;
    }

    .banner-content i {
        margin-right: 10px;
        font-size: 1.2em;
    }

    .clear-analysis-btn {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 8px 15px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 0.9em;
        transition: all 0.3s ease;
    }

    .clear-analysis-btn:hover {
        background-color: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
    }

    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @media (max-width: 768px) {
        .keywords-section {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}
