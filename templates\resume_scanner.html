{% extends "base.html" %}

{% block title %}Resume Scanner | JobScan-like Tool{% endblock %}

{% block header_title %}Resume Scanner{% endblock %}
{% block header_subtitle %}Optimize Your Resume for Job Applications{% endblock %}

{% block description %}Upload your resume and paste a job description to get a match score and optimization suggestions.{% endblock %}

{% block content %}
    <div class="scanner-section fade-in">
        <div class="scanner-intro slide-in-down">
            <h2><i class="fas fa-search"></i> Resume Optimization Tool</h2>
            <p>Our resume scanner analyzes your resume against a job description to help you optimize your application. Get a match score and personalized suggestions to improve your chances of getting an interview.</p>

            <div class="scanner-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>Upload Your Resume</h3>
                        <p>Upload your resume in PDF or DOCX format.</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Paste Job Description</h3>
                        <p>Copy and paste the full job description.</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Get Analysis</h3>
                        <p>Receive a match score and optimization suggestions.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="scanner-form-container slide-in-up">
            <form id="scanner-form" action="{{ url_for('resume_scanner') }}" method="POST" enctype="multipart/form-data" class="scanner-form">
                <div class="form-group">
                    <label for="resume"><i class="fas fa-file-alt"></i> Upload Your Resume</label>
                    <div class="file-upload-container">
                        <label for="resume" class="file-upload-label">
                            <i class="fas fa-file-upload"></i>
                            <span>Choose a file</span>
                        </label>
                        <input type="file" id="resume" name="resume" class="file-upload-input">
                        <div class="file-name">No file chosen</div>
                    </div>
                    <p class="form-hint">Accepted formats: PDF, DOCX</p>
                </div>

                <div class="form-group">
                    <label for="job_description"><i class="fas fa-briefcase"></i> Job Description</label>
                    <textarea id="job_description" name="job_description" rows="10" placeholder="Paste the full job description here..."></textarea>
                    <p class="form-hint">For best results, include the entire job description.</p>
                </div>

                <button type="submit" class="scanner-button pulse">
                    <i class="fas fa-search"></i> Analyze Resume
                </button>
            </form>

            <div id="scanner-status" class="scanner-status">
                {% if error %}
                <div class="alert alert-error shake">
                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                </div>
                {% endif %}
                {% if success %}
                <div class="alert alert-success bounce-in">
                    <i class="fas fa-check-circle"></i> {{ success }}
                </div>
                {% endif %}
            </div>
        </div>

        {% if analysis_result %}
        <div class="analysis-container scale-in">
            <div class="analysis-header">
                <h2><i class="fas fa-chart-pie"></i> Analysis Results</h2>
                <p class="analysis-date">Analysis Date: {{ analysis_result.analysis_date }}</p>
            </div>

            <div class="score-container">
                <div class="score-circle" data-score="{{ analysis_result.match_score }}">
                    <div class="score-value">{{ analysis_result.match_score }}%</div>
                    <div class="score-label">Keyword Match</div>
                </div>

                <div class="score-circle ml-score" data-score="{{ analysis_result.ml_prediction.fit_score }}">
                    <div class="score-value">{{ analysis_result.ml_prediction.fit_score }}%</div>
                    <div class="score-label">AI Fit Score</div>
                </div>

                <div class="score-details">
                    {% if analysis_result.matched_keywords is iterable and analysis_result.matched_keywords is not string %}
                        <p><strong>{{ analysis_result.matched_keywords|length }}</strong> matched keywords out of <strong>{{ analysis_result.job_keywords|length }}</strong> job requirements</p>
                    {% else %}
                        <p><strong>0</strong> matched keywords out of <strong>0</strong> job requirements</p>
                    {% endif %}

                    {% if analysis_result.missing_keywords is iterable and analysis_result.missing_keywords is not string %}
                        <p><strong>{{ analysis_result.missing_keywords|length }}</strong> missing keywords</p>
                    {% else %}
                        <p><strong>0</strong> missing keywords</p>
                    {% endif %}

                    <p class="ml-prediction">AI Prediction: <strong>{{ analysis_result.ml_prediction.fit_label }}</strong> ({{ analysis_result.ml_prediction.confidence }}% confidence)</p>
                </div>
            </div>

            <div class="keywords-section">
                <div class="keywords-column">
                    <h3><i class="fas fa-check-circle"></i> Matched Keywords</h3>
                    <div class="keywords-list matched">
                        {% if analysis_result.matched_keywords is iterable and analysis_result.matched_keywords is not string %}
                            {% for keyword in analysis_result.matched_keywords %}
                                <span class="keyword-tag matched">{{ keyword }}</span>
                            {% else %}
                                <p class="no-keywords">No matched keywords found.</p>
                            {% endfor %}
                        {% else %}
                            <p class="no-keywords">No matched keywords found.</p>
                        {% endif %}
                    </div>
                </div>
                <div class="keywords-column">
                    <h3><i class="fas fa-times-circle"></i> Missing Keywords</h3>
                    <div class="keywords-list missing">
                        {% if analysis_result.missing_keywords is iterable and analysis_result.missing_keywords is not string %}
                            {% for keyword in analysis_result.missing_keywords %}
                                <span class="keyword-tag missing">{{ keyword }}</span>
                            {% else %}
                                <p class="no-keywords">No missing keywords found.</p>
                            {% endfor %}
                        {% else %}
                            <p class="no-keywords">No missing keywords found.</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="suggestions-section">
                <h3><i class="fas fa-lightbulb"></i> Optimization Suggestions</h3>
                {% if analysis_result.suggestions is iterable and analysis_result.suggestions is not string %}
                    {% for suggestion in analysis_result.suggestions %}
                        <div class="suggestion-card">
                            <h4>{{ suggestion.title }}</h4>
                            <p>{{ suggestion.description }}</p>
                            <ul class="suggestion-list">
                                {% if suggestion.items is defined and suggestion.items is iterable and suggestion.items is not string %}
                                    {% for item in suggestion.items %}
                                        <li>{{ item }}</li>
                                    {% endfor %}
                                {% endif %}
                            </ul>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>

            <div class="action-buttons">
                <button class="action-button" onclick="window.print()">
                    <i class="fas fa-print"></i> Print Results
                </button>
                <a href="{{ url_for('resume_scanner') }}" class="action-button">
                    <i class="fas fa-redo"></i> New Analysis
                </a>
            </div>
        </div>
        {% endif %}
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update file name display when file is selected
        const fileInput = document.getElementById('resume');
        const fileName = document.querySelector('.file-name');

        if (fileInput && fileName) {
            fileInput.addEventListener('change', function() {
                if (fileInput.files.length > 0) {
                    fileName.textContent = fileInput.files[0].name;
                    fileName.classList.add('file-selected');
                } else {
                    fileName.textContent = 'No file chosen';
                    fileName.classList.remove('file-selected');
                }
            });
        }

        // Function to get color based on score
        function getScoreColor(score) {
            if (score >= 80) return '#4caf50'; // Green
            if (score >= 60) return '#8bc34a'; // Light Green
            if (score >= 40) return '#ffc107'; // Yellow
            if (score >= 20) return '#ff9800'; // Orange
            return '#f44336'; // Red
        }

        // Function to animate a score circle
        function animateScoreCircle(circleElement, labelText) {
            const score = parseFloat(circleElement.getAttribute('data-score'));
            const circumference = 2 * Math.PI * 45; // 45 is the radius

            // Add SVG for circle animation
            circleElement.innerHTML = `
                <svg width="120" height="120" viewBox="0 0 120 120">
                    <circle cx="60" cy="60" r="45" fill="none" stroke="#e0e0e0" stroke-width="10" />
                    <circle cx="60" cy="60" r="45" fill="none" stroke="${getScoreColor(score)}" stroke-width="10"
                            stroke-dasharray="${circumference}" stroke-dashoffset="${circumference - (score / 100) * circumference}"
                            transform="rotate(-90 60 60)" />
                </svg>
                <div class="score-value">${score}%</div>
                <div class="score-label">${labelText}</div>
            `;

            // Animate the score circle
            setTimeout(() => {
                const circle = circleElement.querySelector('circle:nth-child(2)');
                circle.style.transition = 'stroke-dashoffset 1.5s ease-in-out';
                circle.style.strokeDashoffset = circumference - (score / 100) * circumference;
            }, 100);
        }

        // Animate keyword match score circle
        const keywordScoreCircle = document.querySelector('.score-circle:not(.ml-score)');
        if (keywordScoreCircle) {
            animateScoreCircle(keywordScoreCircle, 'Keyword Match');
        }

        // Animate ML fit score circle
        const mlScoreCircle = document.querySelector('.score-circle.ml-score');
        if (mlScoreCircle) {
            animateScoreCircle(mlScoreCircle, 'AI Fit Score');
        }
    });
</script>
{% endblock %}

{% block extra_head %}
<style>
    .scanner-section {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .scanner-intro {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .scanner-intro h2 {
        color: var(--secondary-color);
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .scanner-intro h2 i {
        color: var(--primary-color);
    }

    .scanner-steps {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
        flex-wrap: wrap;
        gap: 20px;
    }

    .step {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        flex: 1;
        min-width: 250px;
    }

    .step-number {
        width: 40px;
        height: 40px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        font-size: 1.2rem;
    }

    .step-content h3 {
        margin: 0 0 10px 0;
        color: var(--secondary-color);
    }

    .scanner-form-container {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .scanner-form {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .form-group label {
        font-weight: 600;
        color: var(--secondary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-group label i {
        color: var(--primary-color);
    }

    .form-hint {
        font-size: 0.8rem;
        color: #666;
        margin-top: 5px;
    }

    textarea {
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        resize: vertical;
        font-family: inherit;
        font-size: 1rem;
    }

    .scanner-button {
        display: block;
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
    }

    .scanner-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .scanner-status {
        margin-top: 20px;
    }

    .analysis-container {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .analysis-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--primary-color);
    }

    .analysis-header h2 {
        color: var(--secondary-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .analysis-header h2 i {
        color: var(--primary-color);
    }

    .analysis-date {
        color: #666;
        font-size: 0.9rem;
    }

    .score-container {
        display: flex;
        align-items: center;
        gap: 30px;
        margin-bottom: 30px;
        flex-wrap: wrap;
    }

    .score-circle {
        position: relative;
        width: 120px;
        height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .score-circle.ml-score {
        background-color: rgba(0, 123, 255, 0.05);
        border-radius: 50%;
        padding: 5px;
    }

    .score-value {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--secondary-color);
        position: absolute;
    }

    .score-label {
        font-size: 0.9rem;
        color: #666;
        margin-top: 30px;
        position: absolute;
    }

    .score-details {
        flex: 1;
        min-width: 250px;
    }

    .score-details p {
        margin: 10px 0;
    }

    .ml-prediction {
        margin-top: 15px !important;
        padding: 10px;
        background-color: rgba(0, 123, 255, 0.05);
        border-radius: 5px;
        border-left: 3px solid #007bff;
    }

    .keywords-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }

    .keywords-column h3 {
        color: var(--secondary-color);
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .keywords-column h3 i {
        color: var(--primary-color);
    }

    .keywords-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        min-height: 100px;
    }

    .keyword-tag {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.9rem;
    }

    .keyword-tag.matched {
        background-color: rgba(76, 175, 80, 0.1);
        color: #4caf50;
    }

    .keyword-tag.missing {
        background-color: rgba(244, 67, 54, 0.1);
        color: #f44336;
    }

    .no-keywords {
        color: #999;
        font-style: italic;
    }

    .suggestions-section {
        margin-bottom: 30px;
    }

    .suggestions-section h3 {
        color: var(--secondary-color);
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .suggestions-section h3 i {
        color: var(--primary-color);
    }

    .suggestion-card {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
        margin-bottom: 20px;
    }

    .suggestion-card h4 {
        color: var(--secondary-color);
        margin-top: 0;
        margin-bottom: 10px;
    }

    .suggestion-list {
        padding-left: 20px;
    }

    .suggestion-list li {
        margin-bottom: 5px;
    }

    .action-buttons {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .action-button {
        padding: 12px 20px;
        background-color: var(--light-color);
        color: var(--secondary-color);
        border: none;
        border-radius: var(--border-radius);
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 10px;
        text-decoration: none;
    }

    .action-button:hover {
        background-color: var(--primary-color);
        color: white;
    }

    @media (max-width: 768px) {
        .keywords-section {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}
