{% extends "base.html" %}

{% block title %}Job Listings | Applicant Tracking System{% endblock %}

{% block header_title %}Job Listings{% endblock %}
{% block header_subtitle %}Find Your Perfect Career Opportunity{% endblock %}

{% block description %}Browse available job openings and apply online through our Applicant Tracking System.{% endblock %}

{% block content %}
    <div class="jobs-section fade-in">
        <div class="search-section slide-in-left">
            <h2><i class="fas fa-search"></i> Search Jobs</h2>
            <form id="job-search-form" class="search-form">
                <div class="search-container">
                    <input type="text" id="job-search-input" placeholder="Search by title, skills, or location..." required>
                    <button type="submit" class="search-button pulse">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>
                <div class="filter-options">
                    <div class="filter-group">
                        <label for="location-filter">Location</label>
                        <select id="location-filter" class="filter-select">
                            <option value="">All Locations</option>
                            <option value="Abuja, Abj">Abuja, Abj</option>
                            <option value="Port Harcourt, PH">Port Harcourt, PH</option>
                            <option value="Remote">Remote</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="job-type-filter">Job Type</label>
                        <select id="job-type-filter" class="filter-select">
                            <option value="">All Job Types</option>
                            <option value="Full-time">Full-time</option>
                            <option value="Part-time">Part-time</option>
                            <option value="Internship">Internship</option>
                            <option value="Contract">Contract</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="experience-filter">Experience</label>
                        <select id="experience-filter" class="filter-select">
                            <option value="">All Experience Levels</option>
                            <option value="Entry">Entry Level</option>
                            <option value="Mid">Mid Level</option>
                            <option value="Senior">Senior Level</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>

        <div id="job-search-results" class="search-results">
            <!-- Search results will be displayed here -->
        </div>

        <div class="featured-jobs slide-in-right">
            <h2><i class="fas fa-star"></i> Featured Job Openings</h2>
            <div class="job-listings">
                {% if jobs %}
                    {% for job in jobs %}
                        <div class="card job-card scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
                            <div class="job-badge">Featured</div>
                            <div class="card-header">{{ job.title }}</div>
                            <div class="job-details">
                                <div class="job-company">
                                    <i class="fas fa-building"></i> {{ job.company }}
                                </div>
                                <div class="job-location">
                                    <i class="fas fa-map-marker-alt"></i> {{ job.location }}
                                </div>
                                <div class="job-description">
                                    <p>{{ job.description }}</p>
                                </div>
                                <div class="job-meta">
                                    <div class="job-date">
                                        <i class="fas fa-calendar-alt"></i> Posted: {{ job.posted_date }}
                                    </div>
                                    <div class="job-type">
                                        <i class="fas fa-briefcase"></i> Full-time
                                    </div>
                                </div>
                            </div>
                            <div class="job-actions">
                                <button class="button job-apply-btn" data-job-id="{{ job.id }}">
                                    <i class="fas fa-paper-plane"></i> Apply Now
                                </button>
                                <button class="button-secondary job-save-btn" data-job-id="{{ job.id }}">
                                    <i class="far fa-bookmark"></i> Save Job
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="alert alert-warning">No job listings available at this time.</div>
                {% endif %}
            </div>

            <div class="pagination bounce-in" style="animation-delay: 0.5s;">
                <a href="#" class="pagination-link disabled">&laquo; Previous</a>
                <a href="#" class="pagination-link active">1</a>
                <a href="#" class="pagination-link">2</a>
                <a href="#" class="pagination-link">3</a>
                <a href="#" class="pagination-link">Next &raquo;</a>
            </div>
        </div>

        <div class="job-categories slide-in-up" style="animation-delay: 0.3s;">
            <h2><i class="fas fa-th-large"></i> Browse by Category</h2>
            <div class="categories-grid">
                <a href="#" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-laptop-code"></i>
                    </div>
                    <h3>Software Development</h3>
                    <p>40 open positions</p>
                </a>

                <a href="#" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Data Science</h3>
                    <p>18 open positions</p>
                </a>

                <a href="#" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>Design</h3>
                    <p>15 open positions</p>
                </a>

                <a href="#" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <h3>Marketing</h3>
                    <p>23 open positions</p>
                </a>

                <a href="#" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3>Customer Support</h3>
                    <p>12 open positions</p>
                </a>

                <a href="#" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3>Project Management</h3>
                    <p>9 open positions</p>
                </a>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Job search functionality
        const searchForm = document.getElementById('job-search-form');
        const searchResults = document.getElementById('job-search-results');

        if (searchForm) {
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const searchInput = document.getElementById('job-search-input');
                const locationFilter = document.getElementById('location-filter');
                const jobTypeFilter = document.getElementById('job-type-filter');
                const experienceFilter = document.getElementById('experience-filter');

                // Show loading indicator
                searchResults.innerHTML = '<div class="loading">Searching for jobs...</div>';
                searchResults.style.display = 'block';

                // Scroll to results
                searchResults.scrollIntoView({ behavior: 'smooth' });

                // Simulate search delay
                setTimeout(() => {
                    // Create sample results based on search
                    let resultsHTML = `
                        <div class="search-results-header">
                            <h3><i class="fas fa-search"></i> Search Results</h3>
                            <p>Found 3 jobs matching "${searchInput.value}"</p>
                        </div>
                        <div class="job-listings search-results-grid">
                    `;

                    // Sample results
                    for (let i = 1; i <= 3; i++) {
                        resultsHTML += `
                            <div class="card job-card scale-in" style="animation-delay: ${i * 0.1}s;">
                                <div class="card-header">Senior ${searchInput.value} Developer</div>
                                <div class="job-details">
                                    <div class="job-company">
                                        <i class="fas fa-building"></i> Tech Company ${i}
                                    </div>
                                    <div class="job-location">
                                        <i class="fas fa-map-marker-alt"></i> ${locationFilter.value || 'Any Location'}
                                    </div>
                                    <div class="job-description">
                                        <p>We are looking for an experienced ${searchInput.value} developer to join our team.</p>
                                    </div>
                                    <div class="job-meta">
                                        <div class="job-date">
                                            <i class="fas fa-calendar-alt"></i> Posted: Today
                                        </div>
                                        <div class="job-type">
                                            <i class="fas fa-briefcase"></i> ${jobTypeFilter.value || 'Full-time'}
                                        </div>
                                    </div>
                                </div>
                                <div class="job-actions">
                                    <button class="button job-apply-btn" data-job-id="${i}">
                                        <i class="fas fa-paper-plane"></i> Apply Now
                                    </button>
                                    <button class="button-secondary job-save-btn" data-job-id="${i}">
                                        <i class="far fa-bookmark"></i> Save Job
                                    </button>
                                </div>
                            </div>
                        `;
                    }

                    resultsHTML += `</div>`;
                    searchResults.innerHTML = resultsHTML;

                    // Add event listeners to new buttons
                    const applyButtons = searchResults.querySelectorAll('.job-apply-btn');
                    const saveButtons = searchResults.querySelectorAll('.job-save-btn');

                    applyButtons.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const jobId = this.getAttribute('data-job-id');
                            window.location.href = `/applicant_portal?job_id=${jobId}`;
                        });
                    });

                    saveButtons.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const jobId = this.getAttribute('data-job-id');
                            this.innerHTML = '<i class="fas fa-bookmark"></i> Saved';
                            this.classList.add('saved');
                        });
                    });
                }, 1500);
            });
        }

        // Category card hover effect
        const categoryCards = document.querySelectorAll('.category-card');
        categoryCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.querySelector('.category-icon').classList.add('pulse');
            });

            card.addEventListener('mouseleave', function() {
                this.querySelector('.category-icon').classList.remove('pulse');
            });
        });
    });
</script>
{% endblock %}

{% block extra_head %}
<style>
    .jobs-section {
        display: flex;
        flex-direction: column;
        gap: 40px;
    }

    .search-section {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .search-section h2 {
        color: var(--secondary-color);
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .search-section h2 i {
        color: var(--primary-color);
    }

    .search-container {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .search-container input {
        flex-grow: 1;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-size: 1rem;
    }

    .search-button {
        background-color: var(--primary-color);
        color: white;
        border: none;
        padding: 0 20px;
        border-radius: var(--border-radius);
        cursor: pointer;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: var(--transition);
    }

    .search-button:hover {
        background-color: var(--secondary-color);
        transform: translateY(-2px);
    }

    .filter-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .filter-group label {
        font-weight: 600;
        color: var(--secondary-color);
        font-size: 0.9rem;
    }

    .filter-select {
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        background-color: white;
    }

    .search-results {
        display: none;
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .search-results-header {
        margin-bottom: 20px;
    }

    .search-results-header h3 {
        color: var(--secondary-color);
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .search-results-header h3 i {
        color: var(--primary-color);
    }

    .search-results-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .featured-jobs {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .featured-jobs h2 {
        color: var(--secondary-color);
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .featured-jobs h2 i {
        color: var(--primary-color);
    }

    .job-listings {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .job-card {
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: hidden;
    }

    .job-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: var(--primary-color);
        color: white;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        z-index: 1;
    }

    .job-details {
        flex-grow: 1;
        padding: 15px 0;
    }

    .job-company, .job-location, .job-meta {
        margin-bottom: 10px;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .job-company i, .job-location i, .job-meta i {
        color: var(--primary-color);
        width: 16px;
    }

    .job-description {
        margin: 15px 0;
        color: #555;
    }

    .job-meta {
        display: flex;
        justify-content: space-between;
        border-top: 1px solid #eee;
        padding-top: 10px;
    }

    .job-actions {
        display: flex;
        gap: 10px;
        margin-top: auto;
    }

    .job-apply-btn, .job-save-btn {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
    }

    .saved {
        background-color: var(--light-color) !important;
        color: var(--success-color) !important;
        border-color: var(--success-color) !important;
    }

    .job-categories {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .job-categories h2 {
        color: var(--secondary-color);
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .job-categories h2 i {
        color: var(--primary-color);
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 20px;
    }

    .category-card {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
        text-align: center;
        text-decoration: none;
        color: var(--secondary-color);
        transition: var(--transition);
    }

    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--box-shadow);
        background-color: white;
    }

    .category-icon {
        width: 60px;
        height: 60px;
        background-color: rgba(52, 152, 219, 0.1);
        color: var(--primary-color);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 1.5rem;
        margin: 0 auto 15px;
        transition: var(--transition);
    }

    .category-card:hover .category-icon {
        background-color: var(--primary-color);
        color: white;
    }

    .category-card h3 {
        margin-bottom: 5px;
        font-size: 1rem;
    }

    .category-card p {
        color: #666;
        font-size: 0.9rem;
    }

    @media (max-width: 768px) {
        .search-container {
            flex-direction: column;
        }

        .job-listings, .categories-grid, .search-results-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}
