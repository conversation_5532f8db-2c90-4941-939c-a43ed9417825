import os
import requests
import logging
import json
import re
from typing import Dict, Any, Optional, List
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# Configure Gemini API
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
if not GEMINI_API_KEY:
    raise ValueError("GEMINI_API_KEY environment variable not set")

genai.configure(api_key=GEMINI_API_KEY)
model = genai.GenerativeModel('gemini-pro')

def build_gemini_prompt(cv_text: str, job_description: str) -> str:
    """
    Build a prompt for Gemini to analyze CV against job description.
    
    Args:
        cv_text: The extracted text from the CV
        job_description: The job description to match against
        
    Returns:
        str: The formatted prompt for Gemini API
    """
    return f"""Analyze this CV against the job requirements:

CV Text:
{cv_text}

Job Description:
{job_description}

Please provide a detailed analysis including:
1. Overall match percentage
2. Key skills matched
3. Missing skills or qualifications
4. Experience relevance
5. Education fit
6. Recommendations for improvement

Format the response in a clear, structured way."""

async def analyze_cv_with_gemini(cv_text: str, job_description: str) -> Dict[str, Any]:
    """
    Analyze a CV against job requirements using Gemini API.
    
    Args:
        cv_text: The extracted text from the CV
        job_description: The job description to match against
        
    Returns:
        Dict[str, Any]: Analysis results
    """
    try:
        prompt = build_gemini_prompt(cv_text, job_description)
        response = await model.generate_content_async(prompt)
        
        if response and response.text:
            # Process and structure the response
            analysis = {
                'success': True,
                'analysis': response.text,
                'error': None
            }
        else:
            analysis = {
                'success': False,
                'analysis': None,
                'error': 'No response from Gemini API'
            }
            
        return analysis
        
    except Exception as e:
        return {
            'success': False,
            'analysis': None,
            'error': str(e)
        }

def extract_skills_from_text(text: str) -> list[str]:
    """
    Extract technical skills from text using Gemini API.
    
    Args:
        text: The text to analyze for skills
        
    Returns:
        list[str]: List of identified technical skills
    """
    try:
        prompt = f"""Extract all technical skills from this text. Return only a comma-separated list of skills:

{text}

Skills:"""
        
        response = model.generate_content(prompt)
        if response and response.text:
            # Clean and process the response
            skills = [
                skill.strip()
                for skill in response.text.split(',')
                if skill.strip() and len(skill.strip()) > 1
            ]
            return skills
        return []
        
    except Exception as e:
        print(f"Error extracting skills: {e}")
        return []


def extract_cv_information(cv_text: str) -> Dict[str, Any]:
    """
    Extract comprehensive information from CV text using Gemini AI.

    Args:
        cv_text: The extracted text from the CV

    Returns:
        Dict[str, Any]: Structured CV information including education, experience, skills, etc.
    """
    try:
        prompt = f"""
        Analyze this CV text and extract the following information in JSON format:

        CV Text:
        {cv_text}

        Please extract and return the information in this exact JSON structure:
        {{
            "personal_info": {{
                "name": "Full name of the person",
                "email": "Email address",
                "phone": "Phone number",
                "address": "Address if available",
                "linkedin": "LinkedIn profile URL if available",
                "website": "Personal website if available"
            }},
            "educational_background": [
                {{
                    "degree": "Degree name (e.g., Bachelor of Science)",
                    "field": "Field of study (e.g., Computer Science)",
                    "institution": "University/College name",
                    "graduation_year": "Year of graduation",
                    "gpa": "GPA if mentioned",
                    "honors": "Any honors or distinctions"
                }}
            ],
            "work_experience": [
                {{
                    "job_title": "Position title",
                    "company": "Company name",
                    "duration": "Employment period",
                    "location": "Work location",
                    "responsibilities": ["List of key responsibilities"],
                    "achievements": ["Notable achievements"]
                }}
            ],
            "technical_skills": [
                "List of technical skills like programming languages, tools, frameworks"
            ],
            "soft_skills": [
                "List of soft skills like leadership, communication, teamwork"
            ],
            "certifications": [
                {{
                    "name": "Certification name",
                    "issuer": "Issuing organization",
                    "date": "Date obtained",
                    "expiry": "Expiry date if applicable"
                }}
            ],
            "projects": [
                {{
                    "name": "Project name",
                    "description": "Brief description",
                    "technologies": ["Technologies used"],
                    "duration": "Project duration"
                }}
            ],
            "languages": [
                {{
                    "language": "Language name",
                    "proficiency": "Proficiency level"
                }}
            ],
            "summary": "Professional summary or objective",
            "total_experience": "Total years of experience"
        }}

        Important:
        - Return ONLY the JSON object, no additional text
        - If information is not available, use null or empty arrays
        - Be thorough in extracting educational background details
        - Include all degrees, certifications, and educational achievements
        """

        response = model.generate_content(prompt)

        if response and response.text:
            try:
                # Clean the response text to extract JSON
                response_text = response.text.strip()

                # Remove any markdown formatting
                if response_text.startswith('```json'):
                    response_text = response_text[7:]
                if response_text.startswith('```'):
                    response_text = response_text[3:]
                if response_text.endswith('```'):
                    response_text = response_text[:-3]

                # Parse JSON
                cv_info = json.loads(response_text)

                # Validate and clean the data
                cv_info = validate_cv_information(cv_info)

                logger.info("Successfully extracted CV information using Gemini AI")
                return cv_info

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response from Gemini: {e}")
                logger.error(f"Response text: {response.text}")
                # Fallback to basic extraction
                return extract_cv_information_fallback(cv_text)
        else:
            logger.error("No response from Gemini API")
            return extract_cv_information_fallback(cv_text)

    except Exception as e:
        logger.error(f"Error extracting CV information with Gemini: {e}")
        return extract_cv_information_fallback(cv_text)


def validate_cv_information(cv_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and clean the extracted CV information.

    Args:
        cv_info: Raw CV information from Gemini

    Returns:
        Dict[str, Any]: Validated and cleaned CV information
    """
    # Ensure all required keys exist
    default_structure = {
        "personal_info": {
            "name": None,
            "email": None,
            "phone": None,
            "address": None,
            "linkedin": None,
            "website": None
        },
        "educational_background": [],
        "work_experience": [],
        "technical_skills": [],
        "soft_skills": [],
        "certifications": [],
        "projects": [],
        "languages": [],
        "summary": None,
        "total_experience": None
    }

    # Merge with default structure
    for key, default_value in default_structure.items():
        if key not in cv_info:
            cv_info[key] = default_value
        elif isinstance(default_value, dict) and isinstance(cv_info[key], dict):
            for sub_key, sub_default in default_value.items():
                if sub_key not in cv_info[key]:
                    cv_info[key][sub_key] = sub_default

    return cv_info


def extract_cv_information_fallback(cv_text: str) -> Dict[str, Any]:
    """
    Fallback method to extract basic CV information using regex patterns.

    Args:
        cv_text: The CV text to analyze

    Returns:
        Dict[str, Any]: Basic CV information
    """
    logger.info("Using fallback method for CV information extraction")

    # Import local CV parser functions
    try:
        from cv_parser import extract_contact_info, extract_name, extract_skills, extract_education, extract_experience

        contact_info = extract_contact_info(cv_text)
        name = extract_name(cv_text)
        skills = extract_skills(cv_text)
        education = extract_education(cv_text)
        experience = extract_experience(cv_text)

        # Structure the information
        cv_info = {
            "personal_info": {
                "name": name,
                "email": contact_info.get('email'),
                "phone": contact_info.get('phone'),
                "address": None,
                "linkedin": contact_info.get('linkedin'),
                "website": None
            },
            "educational_background": education,
            "work_experience": experience,
            "technical_skills": [skill['name'] for skill in skills if skill.get('category') == 'technical'],
            "soft_skills": [skill['name'] for skill in skills if skill.get('category') == 'soft'],
            "certifications": [],
            "projects": [],
            "languages": [],
            "summary": None,
            "total_experience": None
        }

        return cv_info

    except Exception as e:
        logger.error(f"Fallback extraction failed: {e}")
        return {
            "personal_info": {"name": None, "email": None, "phone": None, "address": None, "linkedin": None, "website": None},
            "educational_background": [],
            "work_experience": [],
            "technical_skills": [],
            "soft_skills": [],
            "certifications": [],
            "projects": [],
            "languages": [],
            "summary": None,
            "total_experience": None
        }
