import os
import requests
import logging
from typing import Dict, Any, Optional
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# Configure Gemini API
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
if not GEMINI_API_KEY:
    raise ValueError("GEMINI_API_KEY environment variable not set")

genai.configure(api_key=GEMINI_API_KEY)
model = genai.GenerativeModel('gemini-pro')

def build_gemini_prompt(cv_text: str, job_description: str) -> str:
    """
    Build a prompt for Gemini to analyze CV against job description.
    
    Args:
        cv_text: The extracted text from the CV
        job_description: The job description to match against
        
    Returns:
        str: The formatted prompt for Gemini API
    """
    return f"""Analyze this CV against the job requirements:

CV Text:
{cv_text}

Job Description:
{job_description}

Please provide a detailed analysis including:
1. Overall match percentage
2. Key skills matched
3. Missing skills or qualifications
4. Experience relevance
5. Education fit
6. Recommendations for improvement

Format the response in a clear, structured way."""

async def analyze_cv_with_gemini(cv_text: str, job_description: str) -> Dict[str, Any]:
    """
    Analyze a CV against job requirements using Gemini API.
    
    Args:
        cv_text: The extracted text from the CV
        job_description: The job description to match against
        
    Returns:
        Dict[str, Any]: Analysis results
    """
    try:
        prompt = build_gemini_prompt(cv_text, job_description)
        response = await model.generate_content_async(prompt)
        
        if response and response.text:
            # Process and structure the response
            analysis = {
                'success': True,
                'analysis': response.text,
                'error': None
            }
        else:
            analysis = {
                'success': False,
                'analysis': None,
                'error': 'No response from Gemini API'
            }
            
        return analysis
        
    except Exception as e:
        return {
            'success': False,
            'analysis': None,
            'error': str(e)
        }

def extract_skills_from_text(text: str) -> list[str]:
    """
    Extract technical skills from text using Gemini API.
    
    Args:
        text: The text to analyze for skills
        
    Returns:
        list[str]: List of identified technical skills
    """
    try:
        prompt = f"""Extract all technical skills from this text. Return only a comma-separated list of skills:

{text}

Skills:"""
        
        response = model.generate_content(prompt)
        if response and response.text:
            # Clean and process the response
            skills = [
                skill.strip()
                for skill in response.text.split(',')
                if skill.strip() and len(skill.strip()) > 1
            ]
            return skills
        return []
        
    except Exception as e:
        print(f"Error extracting skills: {e}")
        return []
