import os
import requests
import logging
import json
import re
from typing import Dict, Any, Optional, List
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# Configure Gemini API
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
if not GEMINI_API_KEY:
    raise ValueError("GEMINI_API_KEY environment variable not set")

genai.configure(api_key=GEMINI_API_KEY)
model = genai.GenerativeModel('gemini-pro')

def build_gemini_prompt(cv_text: str, job_description: str) -> str:
    """
    Build a prompt for Gemini to analyze CV against job description.
    
    Args:
        cv_text: The extracted text from the CV
        job_description: The job description to match against
        
    Returns:
        str: The formatted prompt for Gemini API
    """
    return f"""Analyze this CV against the job requirements:

CV Text:
{cv_text}

Job Description:
{job_description}

Please provide a detailed analysis including:
1. Overall match percentage
2. Key skills matched
3. Missing skills or qualifications
4. Experience relevance
5. Education fit
6. Recommendations for improvement

Format the response in a clear, structured way."""

async def analyze_cv_with_gemini(cv_text: str, job_description: str) -> Dict[str, Any]:
    """
    Analyze a CV against job requirements using Gemini API.
    
    Args:
        cv_text: The extracted text from the CV
        job_description: The job description to match against
        
    Returns:
        Dict[str, Any]: Analysis results
    """
    try:
        prompt = build_gemini_prompt(cv_text, job_description)
        response = await model.generate_content_async(prompt)
        
        if response and response.text:
            # Process and structure the response
            analysis = {
                'success': True,
                'analysis': response.text,
                'error': None
            }
        else:
            analysis = {
                'success': False,
                'analysis': None,
                'error': 'No response from Gemini API'
            }
            
        return analysis
        
    except Exception as e:
        return {
            'success': False,
            'analysis': None,
            'error': str(e)
        }

def extract_skills_from_text(text: str) -> list[str]:
    """
    Extract technical skills from text using Gemini API.
    
    Args:
        text: The text to analyze for skills
        
    Returns:
        list[str]: List of identified technical skills
    """
    try:
        prompt = f"""Extract all technical skills from this text. Return only a comma-separated list of skills:

{text}

Skills:"""
        
        response = model.generate_content(prompt)
        if response and response.text:
            # Clean and process the response
            skills = [
                skill.strip()
                for skill in response.text.split(',')
                if skill.strip() and len(skill.strip()) > 1
            ]
            return skills
        return []
        
    except Exception as e:
        print(f"Error extracting skills: {e}")
        return []


def extract_cv_information(cv_text: str) -> Dict[str, Any]:
    """
    Extract comprehensive information from CV text using Expert System with Gemini AI fallback.

    Args:
        cv_text: The extracted text from the CV

    Returns:
        Dict[str, Any]: Structured CV information including education, experience, skills, etc.
    """
    logger.info("Starting CV information extraction with Expert System")

    # First, try the expert system
    try:
        from expert_cv_analyzer import extract_cv_information_expert

        expert_result = extract_cv_information_expert(cv_text)

        # Check if expert system provided good results
        if expert_result and (
            expert_result.get('personal_info', {}).get('name') or
            expert_result.get('educational_background') or
            expert_result.get('work_experience') or
            expert_result.get('technical_skills', {}).get('programming_languages')
        ):
            logger.info("Expert system extraction successful")

            # Enhance with Gemini AI if available
            try:
                gemini_enhancement = extract_cv_information_gemini(cv_text)
                if gemini_enhancement:
                    expert_result = merge_extraction_results(expert_result, gemini_enhancement)
                    logger.info("Enhanced expert results with Gemini AI")
            except Exception as e:
                logger.warning(f"Gemini enhancement failed: {e}")

            return expert_result
        else:
            logger.warning("Expert system results insufficient, trying Gemini AI")

    except Exception as e:
        logger.error(f"Expert system extraction failed: {e}")

    # Fallback to Gemini AI
    try:
        return extract_cv_information_gemini(cv_text)
    except Exception as e:
        logger.error(f"Gemini AI extraction failed: {e}")
        return extract_cv_information_fallback(cv_text)


def extract_cv_information_gemini(cv_text: str) -> Dict[str, Any]:
    """
    Extract comprehensive information from CV text using Gemini AI.

    Args:
        cv_text: The extracted text from the CV

    Returns:
        Dict[str, Any]: Structured CV information including education, experience, skills, etc.
    """
    try:
        prompt = f"""
        You are an expert CV/Resume parser. Analyze this CV text and extract ALL information with maximum accuracy and detail.

        CV Text:
        {cv_text}

        Extract and return information in this EXACT JSON structure. Be extremely thorough and accurate:

        {{
            "personal_info": {{
                "name": "Extract the full name exactly as written",
                "email": "Extract email address",
                "phone": "Extract phone number with country code if available",
                "address": "Extract full address including city, state, country",
                "linkedin": "Extract LinkedIn profile URL",
                "website": "Extract personal website or portfolio URL",
                "nationality": "Extract nationality if mentioned",
                "date_of_birth": "Extract date of birth if mentioned"
            }},
            "educational_background": [
                {{
                    "degree": "Extract EXACT degree name (e.g., 'Bachelor of Science', 'BSc', 'B.S.', 'Master of Arts', 'PhD', 'Diploma')",
                    "degree_abbreviation": "Extract degree abbreviation (e.g., 'BSc', 'MSc', 'PhD', 'BA', 'MA')",
                    "field": "Extract EXACT field of study (e.g., 'Computer Science', 'Electrical Engineering', 'Business Administration')",
                    "specialization": "Extract specialization or major if mentioned",
                    "institution": "Extract EXACT university/college name",
                    "location": "Extract institution location (city, country)",
                    "start_year": "Extract start year of study",
                    "graduation_year": "Extract graduation year or expected graduation",
                    "duration": "Extract duration of study (e.g., '4 years', '2 years')",
                    "gpa": "Extract GPA, CGPA, or percentage if mentioned",
                    "grade": "Extract grade classification (e.g., 'First Class', 'Second Class Upper', 'Distinction', 'Magna Cum Laude', 'Summa Cum Laude')",
                    "honors": "Extract any honors, awards, or distinctions",
                    "thesis_title": "Extract thesis or dissertation title if mentioned",
                    "relevant_coursework": ["Extract relevant courses or subjects"]
                }}
            ],
            "work_experience": [
                {{
                    "job_title": "Extract EXACT job title/position",
                    "company": "Extract EXACT company name",
                    "company_type": "Extract company type/industry if mentioned",
                    "location": "Extract work location (city, country)",
                    "employment_type": "Extract employment type (Full-time, Part-time, Contract, Internship)",
                    "start_date": "Extract start date (month/year)",
                    "end_date": "Extract end date or 'Present' if current",
                    "duration": "Calculate or extract duration (e.g., '2 years 3 months')",
                    "responsibilities": ["Extract ALL key responsibilities and duties"],
                    "achievements": ["Extract ALL achievements, accomplishments, and quantifiable results"],
                    "technologies_used": ["Extract technologies, tools, or software used"],
                    "team_size": "Extract team size if mentioned",
                    "reporting_to": "Extract supervisor title if mentioned"
                }}
            ],
            "technical_skills": {{
                "programming_languages": ["Extract ALL programming languages"],
                "frameworks": ["Extract ALL frameworks and libraries"],
                "databases": ["Extract ALL database technologies"],
                "tools": ["Extract ALL development tools and software"],
                "cloud_platforms": ["Extract cloud platforms (AWS, Azure, GCP)"],
                "operating_systems": ["Extract operating systems"],
                "methodologies": ["Extract methodologies (Agile, Scrum, DevOps)"],
                "other_technical": ["Extract any other technical skills"]
            }},
            "soft_skills": [
                "Extract ALL soft skills (leadership, communication, teamwork, problem-solving, etc.)"
            ],
            "certifications": [
                {{
                    "name": "Extract EXACT certification name",
                    "issuer": "Extract issuing organization",
                    "date_obtained": "Extract date obtained",
                    "expiry_date": "Extract expiry date if applicable",
                    "credential_id": "Extract credential ID if mentioned",
                    "verification_url": "Extract verification URL if available"
                }}
            ],
            "projects": [
                {{
                    "name": "Extract EXACT project name",
                    "description": "Extract detailed project description",
                    "role": "Extract your role in the project",
                    "technologies": ["Extract ALL technologies used"],
                    "start_date": "Extract project start date",
                    "end_date": "Extract project end date",
                    "duration": "Extract or calculate project duration",
                    "team_size": "Extract team size if mentioned",
                    "achievements": ["Extract project achievements and outcomes"],
                    "url": "Extract project URL or demo link if available",
                    "github_url": "Extract GitHub repository URL if mentioned"
                }}
            ],
            "languages": [
                {{
                    "language": "Extract language name",
                    "proficiency": "Extract proficiency level (Native, Fluent, Advanced, Intermediate, Basic)",
                    "certification": "Extract language certification if mentioned"
                }}
            ],
            "awards_and_honors": [
                {{
                    "name": "Extract award or honor name",
                    "issuer": "Extract issuing organization",
                    "date": "Extract date received",
                    "description": "Extract description if available"
                }}
            ],
            "publications": [
                {{
                    "title": "Extract publication title",
                    "authors": ["Extract all authors"],
                    "journal": "Extract journal or conference name",
                    "date": "Extract publication date",
                    "url": "Extract URL if available"
                }}
            ],
            "volunteer_experience": [
                {{
                    "organization": "Extract organization name",
                    "role": "Extract volunteer role",
                    "duration": "Extract duration",
                    "description": "Extract description of activities"
                }}
            ],
            "summary": "Extract professional summary, objective, or profile statement",
            "total_experience": "Calculate total years of professional experience"
        }}

        CRITICAL INSTRUCTIONS:
        1. Return ONLY the JSON object, no additional text or formatting
        2. Extract information EXACTLY as written in the CV
        3. For degrees, preserve the exact format (BSc, B.S., Bachelor of Science, etc.)
        4. For grades, look for terms like "First Class", "Second Class", "Distinction", "Honors", etc.
        5. Extract ALL work experiences, including internships and part-time jobs
        6. Extract ALL projects, both professional and personal
        7. Extract ALL certifications, licenses, and professional qualifications
        8. If information is not available, use null (not empty string)
        9. Be extremely thorough - don't miss any details
        10. Pay special attention to dates, durations, and quantifiable achievements
        """

        response = model.generate_content(prompt)

        if response and response.text:
            try:
                # Clean the response text to extract JSON
                response_text = response.text.strip()

                # Remove any markdown formatting
                if response_text.startswith('```json'):
                    response_text = response_text[7:]
                if response_text.startswith('```'):
                    response_text = response_text[3:]
                if response_text.endswith('```'):
                    response_text = response_text[:-3]

                # Parse JSON
                cv_info = json.loads(response_text)

                # Validate and clean the data
                cv_info = validate_cv_information(cv_info)

                logger.info("Successfully extracted CV information using Gemini AI")
                return cv_info

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response from Gemini: {e}")
                logger.error(f"Response text: {response.text}")
                # Fallback to basic extraction
                return extract_cv_information_fallback(cv_text)
        else:
            logger.error("No response from Gemini API")
            return extract_cv_information_fallback(cv_text)

    except Exception as e:
        logger.error(f"Error extracting CV information with Gemini: {e}")
        return extract_cv_information_fallback(cv_text)


def validate_cv_information(cv_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and clean the extracted CV information.

    Args:
        cv_info: Raw CV information from Gemini

    Returns:
        Dict[str, Any]: Validated and cleaned CV information
    """
    # Ensure all required keys exist with enhanced structure
    default_structure = {
        "personal_info": {
            "name": None,
            "email": None,
            "phone": None,
            "address": None,
            "linkedin": None,
            "website": None,
            "nationality": None,
            "date_of_birth": None
        },
        "educational_background": [],
        "work_experience": [],
        "technical_skills": {
            "programming_languages": [],
            "frameworks": [],
            "databases": [],
            "tools": [],
            "cloud_platforms": [],
            "operating_systems": [],
            "methodologies": [],
            "other_technical": []
        },
        "soft_skills": [],
        "certifications": [],
        "projects": [],
        "languages": [],
        "awards_and_honors": [],
        "publications": [],
        "volunteer_experience": [],
        "summary": None,
        "total_experience": None
    }

    # Merge with default structure
    for key, default_value in default_structure.items():
        if key not in cv_info:
            cv_info[key] = default_value
        elif isinstance(default_value, dict) and isinstance(cv_info[key], dict):
            for sub_key, sub_default in default_value.items():
                if sub_key not in cv_info[key]:
                    cv_info[key][sub_key] = sub_default

    # Clean and validate educational background
    if cv_info.get("educational_background"):
        for edu in cv_info["educational_background"]:
            # Ensure all education fields exist
            edu_defaults = {
                "degree": None,
                "degree_abbreviation": None,
                "field": None,
                "specialization": None,
                "institution": None,
                "location": None,
                "start_year": None,
                "graduation_year": None,
                "duration": None,
                "gpa": None,
                "grade": None,
                "honors": None,
                "thesis_title": None,
                "relevant_coursework": []
            }
            for field, default in edu_defaults.items():
                if field not in edu:
                    edu[field] = default

    # Clean and validate work experience
    if cv_info.get("work_experience"):
        for exp in cv_info["work_experience"]:
            # Ensure all work experience fields exist
            exp_defaults = {
                "job_title": None,
                "company": None,
                "company_type": None,
                "location": None,
                "employment_type": None,
                "start_date": None,
                "end_date": None,
                "duration": None,
                "responsibilities": [],
                "achievements": [],
                "technologies_used": [],
                "team_size": None,
                "reporting_to": None
            }
            for field, default in exp_defaults.items():
                if field not in exp:
                    exp[field] = default

    # Clean and validate projects
    if cv_info.get("projects"):
        for project in cv_info["projects"]:
            # Ensure all project fields exist
            project_defaults = {
                "name": None,
                "description": None,
                "role": None,
                "technologies": [],
                "start_date": None,
                "end_date": None,
                "duration": None,
                "team_size": None,
                "achievements": [],
                "url": None,
                "github_url": None
            }
            for field, default in project_defaults.items():
                if field not in project:
                    project[field] = default

    # Clean and validate certifications
    if cv_info.get("certifications"):
        for cert in cv_info["certifications"]:
            # Ensure all certification fields exist
            cert_defaults = {
                "name": None,
                "issuer": None,
                "date_obtained": None,
                "expiry_date": None,
                "credential_id": None,
                "verification_url": None
            }
            for field, default in cert_defaults.items():
                if field not in cert:
                    cert[field] = default

    return cv_info


def extract_cv_information_fallback(cv_text: str) -> Dict[str, Any]:
    """
    Fallback method to extract basic CV information using regex patterns.

    Args:
        cv_text: The CV text to analyze

    Returns:
        Dict[str, Any]: Basic CV information
    """
    logger.info("Using fallback method for CV information extraction")

    # Import local CV parser functions
    try:
        from cv_parser import extract_contact_info, extract_name, extract_skills, extract_education, extract_experience

        contact_info = extract_contact_info(cv_text)
        name = extract_name(cv_text)
        skills = extract_skills(cv_text)
        education = extract_education(cv_text)
        experience = extract_experience(cv_text)

        # Structure the information with enhanced format
        technical_skills_list = [skill['name'] for skill in skills if skill.get('category') == 'technical']
        soft_skills_list = [skill['name'] for skill in skills if skill.get('category') == 'soft']

        cv_info = {
            "personal_info": {
                "name": name,
                "email": contact_info.get('email'),
                "phone": contact_info.get('phone'),
                "address": None,
                "linkedin": contact_info.get('linkedin'),
                "website": None,
                "nationality": None,
                "date_of_birth": None
            },
            "educational_background": education,
            "work_experience": experience,
            "technical_skills": {
                "programming_languages": [s for s in technical_skills_list if any(lang in s.lower() for lang in ['python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift'])],
                "frameworks": [s for s in technical_skills_list if any(fw in s.lower() for fw in ['react', 'angular', 'vue', 'django', 'flask', 'spring', 'express'])],
                "databases": [s for s in technical_skills_list if any(db in s.lower() for db in ['mysql', 'postgresql', 'mongodb', 'redis', 'oracle', 'sqlite'])],
                "tools": [s for s in technical_skills_list if any(tool in s.lower() for tool in ['git', 'docker', 'kubernetes', 'jenkins', 'jira'])],
                "cloud_platforms": [s for s in technical_skills_list if any(cloud in s.lower() for cloud in ['aws', 'azure', 'gcp', 'google cloud'])],
                "operating_systems": [s for s in technical_skills_list if any(os in s.lower() for os in ['linux', 'windows', 'macos', 'ubuntu'])],
                "methodologies": [s for s in technical_skills_list if any(method in s.lower() for method in ['agile', 'scrum', 'devops', 'ci/cd'])],
                "other_technical": [s for s in technical_skills_list if not any(cat in s.lower() for cat in ['python', 'java', 'react', 'mysql', 'aws', 'linux', 'agile', 'git'])]
            },
            "soft_skills": soft_skills_list,
            "certifications": [],
            "projects": [],
            "languages": [],
            "awards_and_honors": [],
            "publications": [],
            "volunteer_experience": [],
            "summary": None,
            "total_experience": None
        }

        return cv_info

    except Exception as e:
        logger.error(f"Fallback extraction failed: {e}")
        return {
            "personal_info": {
                "name": None, "email": None, "phone": None, "address": None,
                "linkedin": None, "website": None, "nationality": None, "date_of_birth": None
            },
            "educational_background": [],
            "work_experience": [],
            "technical_skills": {
                "programming_languages": [], "frameworks": [], "databases": [],
                "tools": [], "cloud_platforms": [], "operating_systems": [],
                "methodologies": [], "other_technical": []
            },
            "soft_skills": [],
            "certifications": [],
            "projects": [],
            "languages": [],
            "awards_and_honors": [],
            "publications": [],
            "volunteer_experience": [],
            "summary": None,
            "total_experience": None
        }


def merge_extraction_results(expert_result: Dict[str, Any], gemini_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge results from expert system and Gemini AI, prioritizing expert system.

    Args:
        expert_result: Results from expert system
        gemini_result: Results from Gemini AI

    Returns:
        Dict[str, Any]: Merged results
    """
    merged = expert_result.copy()

    # Merge personal info - fill in missing fields from Gemini
    if merged.get('personal_info') and gemini_result.get('personal_info'):
        for key, value in gemini_result['personal_info'].items():
            if not merged['personal_info'].get(key) and value:
                merged['personal_info'][key] = value

    # Enhance educational background with Gemini details
    if gemini_result.get('educational_background'):
        for i, expert_edu in enumerate(merged.get('educational_background', [])):
            if i < len(gemini_result['educational_background']):
                gemini_edu = gemini_result['educational_background'][i]

                # Fill in missing fields
                for key, value in gemini_edu.items():
                    if not expert_edu.get(key) and value:
                        expert_edu[key] = value

    # Enhance work experience
    if gemini_result.get('work_experience'):
        for i, expert_exp in enumerate(merged.get('work_experience', [])):
            if i < len(gemini_result['work_experience']):
                gemini_exp = gemini_result['work_experience'][i]

                # Merge responsibilities and achievements
                if gemini_exp.get('responsibilities'):
                    expert_exp['responsibilities'].extend(gemini_exp['responsibilities'])
                    expert_exp['responsibilities'] = list(set(expert_exp['responsibilities']))

                if gemini_exp.get('achievements'):
                    expert_exp['achievements'].extend(gemini_exp['achievements'])
                    expert_exp['achievements'] = list(set(expert_exp['achievements']))

                # Fill in missing fields
                for key, value in gemini_exp.items():
                    if not expert_exp.get(key) and value:
                        expert_exp[key] = value

    # Merge technical skills
    if gemini_result.get('technical_skills'):
        expert_tech = merged.get('technical_skills', {})
        gemini_tech = gemini_result['technical_skills']

        if isinstance(gemini_tech, dict):
            for category, skills in gemini_tech.items():
                if category in expert_tech and isinstance(skills, list):
                    expert_tech[category].extend(skills)
                    expert_tech[category] = list(set(expert_tech[category]))
                elif category not in expert_tech:
                    expert_tech[category] = skills

        merged['technical_skills'] = expert_tech

    # Merge other sections
    for section in ['certifications', 'projects', 'languages', 'awards_and_honors', 'publications', 'volunteer_experience']:
        if gemini_result.get(section) and not merged.get(section):
            merged[section] = gemini_result[section]
        elif gemini_result.get(section) and merged.get(section):
            # Combine lists, avoiding duplicates
            merged[section].extend(gemini_result[section])
            # Remove duplicates based on name/title field
            seen = set()
            unique_items = []
            for item in merged[section]:
                identifier = item.get('name') or item.get('title') or str(item)
                if identifier not in seen:
                    seen.add(identifier)
                    unique_items.append(item)
            merged[section] = unique_items

    # Use Gemini summary if expert doesn't have one
    if not merged.get('summary') and gemini_result.get('summary'):
        merged['summary'] = gemini_result['summary']

    # Use Gemini total experience if expert doesn't have one
    if not merged.get('total_experience') and gemini_result.get('total_experience'):
        merged['total_experience'] = gemini_result['total_experience']

    return merged
