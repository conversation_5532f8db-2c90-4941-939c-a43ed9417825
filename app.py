#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Applicant Tracking System

A Flask web application for automated CV analysis and job application management.
This application allows users to upload their CVs, which are then analyzed to extract
relevant information such as skills, education, and contact details.

Author: John 
Version: 1.0.0
"""

# Standard library imports
import os
import re
import logging
import datetime
from typing import Tuple, List, Dict, Any, Optional
from pathlib import Path
from functools import wraps

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Third-party imports
from flask import (
    Flask,
    render_template,
    request,
    redirect,
    url_for,
    flash,
    jsonify,
    session,
    abort
)
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge
from flask_sqlalchemy import SQLAlchemy

# NLP related imports
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.corpus import stopwords
from nltk.stem import PorterStemmer, WordNetLemmatizer
from textblob import TextBlob

# ML related imports
import ml_resume_analyzer
import gemini_api

# Import database models
from models import db, User, Job, Skill, job_skills, Applicant, CV, JobApplication, Settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize Flask application
app = Flask(__name__)

# Application Configuration
class Config:
    """Application configuration class"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-key-for-development-only'
    UPLOAD_FOLDER = Path('static/files')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 MB max upload size
    ALLOWED_EXTENSIONS = {'pdf', 'docx'}
    DEBUG = True

    # Database configuration
    SQLALCHEMY_DATABASE_URI = 'sqlite:///app.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

# Apply configuration
app.config.from_object(Config)

# Initialize database
db.init_app(app)

# Create database tables if they don't exist
with app.app_context():
    db.create_all()

    # Create temporary users if they don't exist
    admin_user = User.query.filter_by(username='admin').first()
    if not admin_user:
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            role='admin',
            first_name='Admin',
            last_name='User'
        )
        db.session.add(admin_user)
        logger.info("Created temporary admin user: admin / admin123")

    regular_user = User.query.filter_by(username='user').first()
    if not regular_user:
        regular_user = User(
            username='user',
            email='<EMAIL>',
            password='user123',
            role='user',
            first_name='John',
            last_name='Doe'
        )
        db.session.add(regular_user)
        logger.info("Created temporary regular user: user / user123")

    # Create sample jobs if they don't exist
    if Job.query.count() == 0:
        sample_jobs = [
            {
                'title': 'Senior Python Developer',
                'company': 'Tech Innovations Inc.',
                'location': 'New York, NY',
                'description': 'We are looking for an experienced Python developer to join our team. You will be responsible for developing and maintaining web applications using Flask and Django frameworks.',
                'requirements': 'Bachelor\'s degree in Computer Science, 5+ years of Python experience, Flask/Django knowledge, SQL database experience.',
                'job_type': 'Full-time',
                'experience_level': 'Senior',
                'salary_min': 120000,
                'salary_max': 150000,
                'is_active': True
            },
            {
                'title': 'Data Scientist',
                'company': 'Data Analytics Co.',
                'location': 'San Francisco, CA',
                'description': 'Seeking a data scientist with experience in machine learning and NLP to analyze large datasets and build predictive models.',
                'requirements': 'Master\'s degree in Data Science or related field, Python, R, machine learning, statistics, data visualization.',
                'job_type': 'Full-time',
                'experience_level': 'Mid',
                'salary_min': 100000,
                'salary_max': 130000,
                'is_active': True
            },
            {
                'title': 'Frontend Developer',
                'company': 'Web Solutions',
                'location': 'Remote',
                'description': 'Looking for a frontend developer with React and JavaScript expertise to build modern web applications.',
                'requirements': 'Bachelor\'s degree, 3+ years JavaScript experience, React, HTML5, CSS3, responsive design.',
                'job_type': 'Contract',
                'experience_level': 'Mid',
                'salary_min': 80000,
                'salary_max': 100000,
                'is_active': True
            },
            {
                'title': 'DevOps Engineer',
                'company': 'Cloud Systems Ltd.',
                'location': 'Austin, TX',
                'description': 'DevOps engineer to manage cloud infrastructure and implement CI/CD pipelines.',
                'requirements': 'AWS/Azure experience, Docker, Kubernetes, Jenkins, Linux administration, scripting.',
                'job_type': 'Full-time',
                'experience_level': 'Senior',
                'salary_min': 110000,
                'salary_max': 140000,
                'is_active': True
            },
            {
                'title': 'UI/UX Designer',
                'company': 'Design Studio Pro',
                'location': 'Los Angeles, CA',
                'description': 'Creative UI/UX designer to design user interfaces and improve user experience.',
                'requirements': 'Design degree, Figma, Adobe Creative Suite, user research, prototyping, 3+ years experience.',
                'job_type': 'Full-time',
                'experience_level': 'Mid',
                'salary_min': 75000,
                'salary_max': 95000,
                'is_active': False  # Inactive job for testing
            }
        ]

        for job_data in sample_jobs:
            job = Job(**job_data)
            db.session.add(job)

        logger.info("Created sample jobs")

    # Create sample applicants and applications if they don't exist
    if Applicant.query.count() == 0:
        sample_applicants = [
            {
                'name': 'John Smith',
                'email': '<EMAIL>',
                'phone': '******-0101'
            },
            {
                'name': 'Jane Doe',
                'email': '<EMAIL>',
                'phone': '******-0102'
            },
            {
                'name': 'Michael Johnson',
                'email': '<EMAIL>',
                'phone': '******-0103'
            },
            {
                'name': 'Sarah Wilson',
                'email': '<EMAIL>',
                'phone': '******-0104'
            }
        ]

        for applicant_data in sample_applicants:
            applicant = Applicant(**applicant_data)
            db.session.add(applicant)

        logger.info("Created sample applicants")

    if JobApplication.query.count() == 0:
        # First, commit applicants so we can reference them
        db.session.commit()

        applicants = Applicant.query.all()
        jobs = Job.query.all()

        if applicants and jobs:
            # Create sample CVs
            for i, applicant in enumerate(applicants[:3]):
                cv = CV(
                    applicant_id=applicant.id,
                    filename=f'cv_{applicant.name.lower().replace(" ", "_")}.pdf',
                    file_path=f'static/files/cv_{applicant.name.lower().replace(" ", "_")}.pdf',
                    file_size=1024000,  # 1MB
                    content_type='application/pdf',
                    extracted_name=applicant.name,
                    extracted_email=applicant.email,
                    extracted_phone=applicant.phone,
                    education='Bachelor of Science in Computer Science',
                    experience='3+ years of software development experience',
                    confidence_score=0.85
                )
                db.session.add(cv)
            # Commit CVs so we can reference them
            db.session.commit()
            cvs = CV.query.all()

            # Create sample applications
            sample_applications = [
                {
                    'applicant_id': applicants[0].id,
                    'job_id': jobs[0].id,
                    'cv_id': cvs[0].id,
                    'status': 'Applied',
                    'match_score': 85.5
                },
                {
                    'applicant_id': applicants[1].id,
                    'job_id': jobs[1].id,
                    'cv_id': cvs[1].id,
                    'status': 'Screening',
                    'match_score': 78.2
                },
                {
                    'applicant_id': applicants[2].id,
                    'job_id': jobs[0].id,
                    'cv_id': cvs[2].id,
                    'status': 'Interview',
                    'match_score': 92.1
                },
                {
                    'applicant_id': applicants[0].id,
                    'job_id': jobs[2].id,
                    'cv_id': cvs[0].id,
                    'status': 'Approved',
                    'match_score': 88.7
                }
            ]

            for app_data in sample_applications:
                application = JobApplication(**app_data)
                db.session.add(application)

            logger.info("Created sample applications")

    db.session.commit()

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Context processor to make current user available in all templates
@app.context_processor
def inject_user():
    """
    Inject current user information into all templates.

    Returns:
        dict: Dictionary containing current user information
    """
    current_user = None
    if 'user_id' in session:
        current_user = User.query.get(session['user_id'])

    return dict(current_user=current_user)

# Authentication helper functions
def login_required(f):
    """
    Decorator to require login for a route.

    Args:
        f: The function to decorate

    Returns:
        The decorated function
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page', 'error')
            return redirect(url_for('login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """
    Decorator to require admin privileges for a route.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page', 'error')
            return redirect(url_for('login', next=request.url))

        user = User.query.get(session['user_id'])
        if not user or not user.is_admin():
            flash('You do not have permission to access this page', 'error')
            return redirect(url_for('index'))

        return f(*args, **kwargs)
    return decorated_function

# Utility Functions
def allowed_file(filename: str) -> bool:
    """
    Check if the uploaded file has an allowed extension.

    Args:
        filename (str): The name of the uploaded file

    Returns:
        bool: True if the file extension is allowed, False otherwise
    """
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def extract_info(cv_text: str) -> Tuple[str, str, List[str], str]:
    """
    Extract relevant information from CV text using Gemini AI and fallback methods.

    Args:
        cv_text (str): The text content of the CV

    Returns:
        Tuple[str, str, List[str], str]: A tuple containing name, contact info, skills, and education
    """
    logger.info(f"Processing CV text of length: {len(cv_text)} using Gemini AI")

    try:
        # Use Gemini AI for comprehensive extraction
        from gemini_api import extract_cv_information

        cv_info = extract_cv_information(cv_text)

        # Extract name
        name = cv_info.get('personal_info', {}).get('name') or "Name not found"

        # Format contact info
        personal_info = cv_info.get('personal_info', {})
        contact_parts = []
        if personal_info.get('email'):
            contact_parts.append(personal_info['email'])
        if personal_info.get('phone'):
            contact_parts.append(personal_info['phone'])
        if personal_info.get('linkedin'):
            contact_parts.append(personal_info['linkedin'])

        contact_info = " | ".join(contact_parts) if contact_parts else "Contact info not found"

        # Combine technical and soft skills
        technical_skills = cv_info.get('technical_skills', [])
        soft_skills = cv_info.get('soft_skills', [])
        skills = technical_skills + soft_skills
        if not skills:
            skills = ["General Skills"]

        # Format education background
        education_list = cv_info.get('educational_background', [])
        education_parts = []

        for edu in education_list:
            edu_str = ""
            if edu.get('degree'):
                edu_str += edu['degree']
            if edu.get('field'):
                edu_str += f" in {edu['field']}"
            if edu.get('institution'):
                edu_str += f" from {edu['institution']}"
            if edu.get('graduation_year'):
                edu_str += f" ({edu['graduation_year']})"
            if edu.get('honors'):
                edu_str += f" - {edu['honors']}"

            if edu_str:
                education_parts.append(edu_str)

        education_text = "; ".join(education_parts) if education_parts else "Education not specified"

        logger.info(f"Successfully extracted with Gemini AI: name={name}, skills={len(skills)}, education entries={len(education_list)}")
        return name, contact_info, skills, education_text

    except Exception as e:
        logger.error(f"Error in Gemini AI extraction: {str(e)}")
        logger.info("Falling back to local CV parser")

        # Fallback to local CV parser
        try:
            from cv_parser import extract_contact_info, extract_name, extract_skills, extract_education

            # Extract information using local CV parser
            contact_info_dict = extract_contact_info(cv_text)
            name = extract_name(cv_text)
            skills_list = extract_skills(cv_text)
            education_list = extract_education(cv_text)

            # Format contact info
            contact_parts = []
            if contact_info_dict.get('email'):
                contact_parts.append(contact_info_dict['email'])
            if contact_info_dict.get('phone'):
                contact_parts.append(contact_info_dict['phone'])
            if contact_info_dict.get('linkedin'):
                contact_parts.append(contact_info_dict['linkedin'])

            contact_info = " | ".join(contact_parts) if contact_parts else "Contact info not found"

            # Extract skill names from skill dictionaries
            skills = [skill['name'] for skill in skills_list if skill.get('name')]
            if not skills:
                skills = ["General Skills"]

            # Format education
            education_text = ""
            if education_list:
                education_text = "; ".join([
                    f"{edu.get('degree', 'Degree')} in {edu.get('field', 'Field')} from {edu.get('institution', 'Institution')}"
                    for edu in education_list
                ])

            if not education_text:
                education_text = "Education not specified"

            # Use extracted name or fallback
            if not name or name.strip() == "":
                name = "Name not found"

            logger.info(f"Successfully extracted with fallback: name={name}, skills={len(skills)}, education entries={len(education_list)}")
            return name, contact_info, skills, education_text

        except Exception as fallback_error:
            logger.error(f"Error in fallback extraction: {str(fallback_error)}")
            # Final fallback
            return "Name not found", "Contact info not found", ["General Skills"], "Education not specified"


def update_applicant_profile_from_cv(user_id: int, cv_info: Dict[str, Any], cv_file_path: str) -> None:
    """
    Update applicant profile with extracted CV information.

    Args:
        user_id: The user ID
        cv_info: Extracted CV information from expert system
        cv_file_path: Path to the uploaded CV file
    """
    try:
        # Get or create applicant record
        applicant = Applicant.query.filter_by(user_id=user_id).first()

        if not applicant:
            # Create new applicant record
            user = User.query.get(user_id)
            applicant = Applicant(
                user_id=user_id,
                name=cv_info.get('personal_info', {}).get('name') or user.username,
                email=cv_info.get('personal_info', {}).get('email') or user.email,
                phone=cv_info.get('personal_info', {}).get('phone') or '',
                skills='',
                education='',
                experience='',
                cv_file_path=cv_file_path
            )
            db.session.add(applicant)
        else:
            # Update existing applicant record
            applicant.cv_file_path = cv_file_path

        # Update personal information
        personal_info = cv_info.get('personal_info', {})
        if personal_info.get('name'):
            applicant.name = personal_info['name']
        if personal_info.get('email'):
            applicant.email = personal_info['email']
        if personal_info.get('phone'):
            applicant.phone = personal_info['phone']

        # Update skills
        all_skills = []
        technical_skills = cv_info.get('technical_skills', {})
        if isinstance(technical_skills, dict):
            for category, skills in technical_skills.items():
                if isinstance(skills, list):
                    all_skills.extend(skills)

        soft_skills = cv_info.get('soft_skills', [])
        if isinstance(soft_skills, list):
            all_skills.extend(soft_skills)

        applicant.skills = ', '.join(list(set(all_skills)))

        # Update education
        education_list = cv_info.get('educational_background', [])
        education_text = []
        for edu in education_list:
            edu_str = ""
            if edu.get('degree'):
                edu_str += edu['degree']
            if edu.get('field'):
                edu_str += f" in {edu['field']}"
            if edu.get('institution'):
                edu_str += f" from {edu['institution']}"
            if edu.get('graduation_year'):
                edu_str += f" ({edu['graduation_year']})"
            if edu_str:
                education_text.append(edu_str)

        applicant.education = '; '.join(education_text)

        # Update experience
        experience_list = cv_info.get('work_experience', [])
        experience_text = []
        for exp in experience_list:
            exp_str = ""
            if exp.get('job_title'):
                exp_str += exp['job_title']
            if exp.get('company'):
                exp_str += f" at {exp['company']}"
            if exp.get('duration'):
                exp_str += f" ({exp['duration']})"
            if exp_str:
                experience_text.append(exp_str)

        applicant.experience = '; '.join(experience_text)

        # Create or update CV record
        cv_record = CV.query.filter_by(applicant_id=applicant.id).first()
        if not cv_record:
            cv_record = CV(
                applicant_id=applicant.id,
                file_path=cv_file_path,
                extracted_text=json.dumps(cv_info),
                analysis_date=datetime.datetime.utcnow()
            )
            db.session.add(cv_record)
        else:
            cv_record.file_path = cv_file_path
            cv_record.extracted_text = json.dumps(cv_info)
            cv_record.analysis_date = datetime.datetime.utcnow()

        db.session.commit()
        logger.info(f"Updated applicant profile for user {user_id}")

    except Exception as e:
        logger.error(f"Error updating applicant profile: {str(e)}")
        db.session.rollback()
        raise


def store_analysis_result(analysis_result: Dict[str, Any], filename: str) -> None:
    """
    Store analysis result in session and database for state management.

    Args:
        analysis_result: The complete analysis result dictionary
        filename: The uploaded file name
    """
    try:
        user_id = session.get('user_id')
        if not user_id:
            logger.warning("No user_id in session for storing analysis result")
            return

        # Store in session for immediate access
        session['current_analysis'] = {
            'timestamp': datetime.datetime.now().isoformat(),
            'filename': filename,
            'analysis_result': analysis_result,
            'user_id': user_id
        }

        # Store summary in session for quick access
        session['last_analysis'] = {
            'timestamp': datetime.datetime.now().isoformat(),
            'resume_filename': filename,
            'match_score': analysis_result.get('match_score', 0),
            'matched_keywords': len(analysis_result.get('matched_keywords', [])),
            'missing_keywords': len(analysis_result.get('missing_keywords', [])),
            'extracted_skills_count': len(analysis_result.get('extracted_skills', [])),
            'has_cv_info': bool(analysis_result.get('cv_info'))
        }

        # Store in database for persistence across sessions
        try:
            # Get or create applicant record
            applicant = Applicant.query.filter_by(user_id=user_id).first()
            if applicant:
                # Create or update analysis record
                analysis_record = AnalysisResult.query.filter_by(applicant_id=applicant.id).first()

                if not analysis_record:
                    analysis_record = AnalysisResult(
                        applicant_id=applicant.id,
                        filename=filename,
                        analysis_data=json.dumps(analysis_result),
                        created_at=datetime.datetime.utcnow(),
                        updated_at=datetime.datetime.utcnow()
                    )
                    db.session.add(analysis_record)
                else:
                    analysis_record.filename = filename
                    analysis_record.analysis_data = json.dumps(analysis_result)
                    analysis_record.updated_at = datetime.datetime.utcnow()

                db.session.commit()
                logger.info(f"Analysis result stored in database for user {user_id}")

        except Exception as e:
            logger.error(f"Error storing analysis result in database: {str(e)}")
            db.session.rollback()

        logger.info(f"Analysis result stored in session for user {user_id}")

    except Exception as e:
        logger.error(f"Error storing analysis result: {str(e)}")


def get_stored_analysis_result() -> Optional[Dict[str, Any]]:
    """
    Retrieve stored analysis result from session or database.

    Returns:
        Dict containing analysis result or None if not found
    """
    try:
        user_id = session.get('user_id')
        if not user_id:
            return None

        # First, try to get from session (fastest)
        current_analysis = session.get('current_analysis')
        if current_analysis and current_analysis.get('user_id') == user_id:
            # Check if the analysis is not too old (e.g., within 24 hours)
            timestamp_str = current_analysis.get('timestamp')
            if timestamp_str:
                try:
                    timestamp = datetime.datetime.fromisoformat(timestamp_str)
                    age = datetime.datetime.now() - timestamp
                    if age.total_seconds() < 86400:  # 24 hours
                        logger.info("Retrieved analysis result from session")
                        return current_analysis.get('analysis_result')
                except ValueError:
                    logger.warning("Invalid timestamp in session analysis")

        # If not in session or too old, try database
        try:
            applicant = Applicant.query.filter_by(user_id=user_id).first()
            if applicant:
                analysis_record = AnalysisResult.query.filter_by(applicant_id=applicant.id).first()
                if analysis_record and analysis_record.analysis_data:
                    try:
                        analysis_result = json.loads(analysis_record.analysis_data)

                        # Restore to session for faster future access
                        session['current_analysis'] = {
                            'timestamp': analysis_record.updated_at.isoformat(),
                            'filename': analysis_record.filename,
                            'analysis_result': analysis_result,
                            'user_id': user_id
                        }

                        # Update session summary
                        session['last_analysis'] = {
                            'timestamp': analysis_record.updated_at.isoformat(),
                            'resume_filename': analysis_record.filename,
                            'match_score': analysis_result.get('match_score', 0),
                            'matched_keywords': len(analysis_result.get('matched_keywords', [])),
                            'missing_keywords': len(analysis_result.get('missing_keywords', [])),
                            'extracted_skills_count': len(analysis_result.get('extracted_skills', [])),
                            'has_cv_info': bool(analysis_result.get('cv_info'))
                        }

                        logger.info("Retrieved analysis result from database and restored to session")
                        return analysis_result

                    except json.JSONDecodeError:
                        logger.error("Failed to parse analysis data from database")

        except Exception as e:
            logger.error(f"Error retrieving analysis result from database: {str(e)}")

        return None

    except Exception as e:
        logger.error(f"Error retrieving stored analysis result: {str(e)}")
        return None


def clear_analysis_state() -> None:
    """
    Clear stored analysis state from session and optionally database.
    """
    try:
        # Clear from session
        session.pop('current_analysis', None)
        session.pop('last_analysis', None)
        logger.info("Cleared analysis state from session")

    except Exception as e:
        logger.error(f"Error clearing analysis state: {str(e)}")


def perform_comprehensive_cv_analysis(cv_text: str, filename: str, filepath: str) -> Dict[str, Any]:
    """
    Perform comprehensive CV analysis using Expert System and Gemini AI.

    Args:
        cv_text: Extracted CV text
        filename: Original filename
        filepath: File path

    Returns:
        Dict containing comprehensive CV analysis
    """
    try:
        logger.info("Starting comprehensive CV analysis for applicant portal")

        # Use Expert System for primary analysis
        from expert_cv_analyzer import extract_cv_information_expert
        expert_analysis = extract_cv_information_expert(cv_text)

        # Enhance with Gemini AI if available
        gemini_enhancement = None
        try:
            from gemini_api import extract_cv_information_gemini
            gemini_enhancement = extract_cv_information_gemini(cv_text)
            logger.info("Gemini AI enhancement completed")
        except Exception as e:
            logger.warning(f"Gemini AI enhancement failed: {str(e)}")

        # Merge results if Gemini enhancement is available
        if gemini_enhancement:
            from gemini_api import merge_extraction_results
            final_analysis = merge_extraction_results(expert_analysis, gemini_enhancement)
            logger.info("Expert system and Gemini AI results merged")
        else:
            final_analysis = expert_analysis
            logger.info("Using expert system results only")

        # Add metadata
        final_analysis['metadata'] = {
            'filename': filename,
            'filepath': filepath,
            'analysis_date': datetime.datetime.now().isoformat(),
            'text_length': len(cv_text),
            'analysis_method': 'Expert System + Gemini AI' if gemini_enhancement else 'Expert System',
            'sections_detected': len([k for k, v in final_analysis.items() if v and k != 'metadata'])
        }

        # Calculate completeness score
        final_analysis['completeness_score'] = calculate_cv_completeness(final_analysis)

        # Generate recommendations
        final_analysis['recommendations'] = generate_cv_recommendations(final_analysis)

        logger.info(f"Comprehensive CV analysis completed with {final_analysis['completeness_score']}% completeness")
        return final_analysis

    except Exception as e:
        logger.error(f"Error in comprehensive CV analysis: {str(e)}")
        # Return basic structure on error
        return {
            'personal_info': {'name': 'Analysis Error', 'email': None, 'phone': None},
            'educational_background': [],
            'work_experience': [],
            'technical_skills': {'programming_languages': [], 'frameworks': [], 'databases': [], 'tools': []},
            'soft_skills': [],
            'certifications': [],
            'projects': [],
            'languages': [],
            'metadata': {
                'filename': filename,
                'analysis_date': datetime.datetime.now().isoformat(),
                'error': str(e)
            },
            'completeness_score': 0,
            'recommendations': ['Please try uploading the CV again or contact support.']
        }


def calculate_cv_completeness(cv_analysis: Dict[str, Any]) -> int:
    """
    Calculate CV completeness score based on available information.

    Args:
        cv_analysis: CV analysis dictionary

    Returns:
        Completeness score (0-100)
    """
    score = 0
    max_score = 100

    # Personal information (20 points)
    personal_info = cv_analysis.get('personal_info', {})
    if personal_info.get('name'): score += 5
    if personal_info.get('email'): score += 5
    if personal_info.get('phone'): score += 5
    if personal_info.get('linkedin'): score += 5

    # Education (20 points)
    education = cv_analysis.get('educational_background', [])
    if education:
        score += 10
        if any(edu.get('degree') for edu in education): score += 5
        if any(edu.get('institution') for edu in education): score += 5

    # Work experience (25 points)
    experience = cv_analysis.get('work_experience', [])
    if experience:
        score += 10
        if any(exp.get('job_title') for exp in experience): score += 5
        if any(exp.get('company') for exp in experience): score += 5
        if any(exp.get('responsibilities') for exp in experience): score += 5

    # Technical skills (20 points)
    tech_skills = cv_analysis.get('technical_skills', {})
    skill_categories = ['programming_languages', 'frameworks', 'databases', 'tools']
    for category in skill_categories:
        if tech_skills.get(category): score += 5

    # Additional sections (15 points)
    if cv_analysis.get('projects'): score += 5
    if cv_analysis.get('certifications'): score += 5
    if cv_analysis.get('languages'): score += 5

    return min(score, max_score)


def generate_cv_recommendations(cv_analysis: Dict[str, Any]) -> List[str]:
    """
    Generate recommendations for CV improvement.

    Args:
        cv_analysis: CV analysis dictionary

    Returns:
        List of recommendations
    """
    recommendations = []

    # Check personal information
    personal_info = cv_analysis.get('personal_info', {})
    if not personal_info.get('email'):
        recommendations.append("Add a professional email address to your contact information.")
    if not personal_info.get('phone'):
        recommendations.append("Include a phone number for easy contact.")
    if not personal_info.get('linkedin'):
        recommendations.append("Add your LinkedIn profile URL to enhance professional networking.")

    # Check education
    education = cv_analysis.get('educational_background', [])
    if not education:
        recommendations.append("Add your educational background including degrees and institutions.")
    else:
        for edu in education:
            if not edu.get('graduation_year'):
                recommendations.append("Include graduation years for your educational qualifications.")
                break

    # Check work experience
    experience = cv_analysis.get('work_experience', [])
    if not experience:
        recommendations.append("Add your work experience including job titles, companies, and key responsibilities.")
    else:
        for exp in experience:
            if not exp.get('responsibilities'):
                recommendations.append("Include detailed responsibilities and achievements for each work experience.")
                break

    # Check technical skills
    tech_skills = cv_analysis.get('technical_skills', {})
    total_skills = sum(len(skills) for skills in tech_skills.values() if isinstance(skills, list))
    if total_skills < 5:
        recommendations.append("Add more technical skills relevant to your field to improve job matching.")

    # Check projects
    if not cv_analysis.get('projects'):
        recommendations.append("Include personal or professional projects to showcase your practical experience.")

    # Check certifications
    if not cv_analysis.get('certifications'):
        recommendations.append("Add any professional certifications or licenses you have obtained.")

    # General recommendations
    completeness = cv_analysis.get('completeness_score', 0)
    if completeness < 70:
        recommendations.append("Your CV completeness is below 70%. Consider adding more detailed information.")

    if not recommendations:
        recommendations.append("Your CV looks comprehensive! Consider keeping it updated with new skills and experiences.")

    return recommendations


def store_portal_analysis(cv_analysis: Dict[str, Any]) -> None:
    """
    Store CV analysis for applicant portal state management.

    Args:
        cv_analysis: Complete CV analysis dictionary
    """
    try:
        user_id = session.get('user_id')
        if not user_id:
            logger.warning("No user_id in session for storing portal analysis")
            return

        # Store in session
        session['portal_cv_analysis'] = {
            'timestamp': datetime.datetime.now().isoformat(),
            'user_id': user_id,
            'analysis': cv_analysis
        }

        logger.info(f"Portal CV analysis stored in session for user {user_id}")

    except Exception as e:
        logger.error(f"Error storing portal analysis: {str(e)}")


def get_stored_portal_analysis() -> Optional[Dict[str, Any]]:
    """
    Retrieve stored CV analysis for applicant portal.

    Returns:
        CV analysis dictionary or None
    """
    try:
        user_id = session.get('user_id')
        if not user_id:
            return None

        portal_analysis = session.get('portal_cv_analysis')
        if portal_analysis and portal_analysis.get('user_id') == user_id:
            # Check if analysis is not too old (24 hours)
            timestamp_str = portal_analysis.get('timestamp')
            if timestamp_str:
                try:
                    timestamp = datetime.datetime.fromisoformat(timestamp_str)
                    age = datetime.datetime.now() - timestamp
                    if age.total_seconds() < 86400:  # 24 hours
                        logger.info("Retrieved portal CV analysis from session")
                        return portal_analysis.get('analysis')
                except ValueError:
                    logger.warning("Invalid timestamp in portal analysis")

        return None

    except Exception as e:
        logger.error(f"Error retrieving portal analysis: {str(e)}")
        return None


@app.route('/api/clear-analysis', methods=['POST'])
@login_required
def api_clear_analysis() -> Any:
    """
    API endpoint to clear stored analysis state.

    Returns:
        JSON response indicating success or failure
    """
    try:
        clear_analysis_state()
        return jsonify({
            'status': 'success',
            'message': 'Analysis state cleared successfully'
        })
    except Exception as e:
        logger.error(f"Error clearing analysis state via API: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/get-analysis-status', methods=['GET'])
@login_required
def api_get_analysis_status() -> Any:
    """
    API endpoint to check if there's a stored analysis result.

    Returns:
        JSON response with analysis status
    """
    try:
        analysis_result = get_stored_analysis_result()
        last_analysis = session.get('last_analysis')

        return jsonify({
            'status': 'success',
            'has_analysis': analysis_result is not None,
            'last_analysis': last_analysis,
            'timestamp': last_analysis.get('timestamp') if last_analysis else None
        })
    except Exception as e:
        logger.error(f"Error getting analysis status: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


def calculate_job_match_score(job_dict: Dict[str, Any], user_skills: List[str], user_projects: List[Dict], user_experience: List[Dict]) -> float:
    """
    Calculate match score between a job and user's CV information.

    Args:
        job_dict: Job information dictionary
        user_skills: List of user's skills
        user_projects: List of user's projects
        user_experience: List of user's work experience

    Returns:
        float: Match score between 0 and 100
    """
    if not user_skills:
        return 0.0

    try:
        # Extract job requirements from description
        job_description = job_dict.get('description', '').lower()
        job_title = job_dict.get('title', '').lower()
        job_requirements = job_dict.get('requirements', '').lower()

        # Combine all job text
        job_text = f"{job_title} {job_description} {job_requirements}"

        # Calculate skill matches
        skill_matches = 0
        total_skills = len(user_skills)

        for skill in user_skills:
            if skill.lower() in job_text:
                skill_matches += 1

        skill_score = (skill_matches / total_skills) * 60 if total_skills > 0 else 0

        # Calculate experience relevance
        experience_score = 0
        if user_experience:
            for exp in user_experience:
                exp_title = exp.get('job_title', '').lower()
                exp_company = exp.get('company', '').lower()

                # Check if experience is relevant
                if any(word in job_text for word in exp_title.split()):
                    experience_score += 20
                    break

        # Calculate project relevance
        project_score = 0
        if user_projects:
            for project in user_projects:
                project_tech = project.get('technologies', [])
                project_desc = project.get('description', '').lower()

                # Check if project technologies match job requirements
                tech_matches = sum(1 for tech in project_tech if tech.lower() in job_text)
                if tech_matches > 0:
                    project_score += min(tech_matches * 5, 20)
                    break

        # Calculate total score
        total_score = min(skill_score + experience_score + project_score, 100)

        return round(total_score, 1)

    except Exception as e:
        logger.error(f"Error calculating job match score: {str(e)}")
        return 0.0


def get_sample_jobs_with_matching(user_skills: List[str], user_projects: List[Dict]) -> List[Dict]:
    """
    Get sample jobs with match scores for fallback.

    Args:
        user_skills: List of user's skills
        user_projects: List of user's projects

    Returns:
        List of job dictionaries with match scores
    """
    sample_jobs = [
        {
            'id': 1,
            'title': 'Senior Python Developer',
            'company': 'Tech Innovations Inc.',
            'location': 'New York, NY',
            'description': 'Looking for an experienced Python developer with Flask, Django, and React experience. Must have experience with databases like PostgreSQL and MongoDB.',
            'requirements': 'Python, Flask, Django, React, PostgreSQL, MongoDB, Git, Docker',
            'posted_date': '2023-11-15',
            'salary_min': 90000,
            'salary_max': 130000
        },
        {
            'id': 2,
            'title': 'Data Scientist',
            'company': 'Data Analytics Co.',
            'location': 'San Francisco, CA',
            'description': 'Seeking a data scientist with experience in machine learning, NLP, and Python. Experience with TensorFlow, scikit-learn, and pandas required.',
            'requirements': 'Python, Machine Learning, NLP, TensorFlow, scikit-learn, pandas, SQL, R',
            'posted_date': '2023-11-10',
            'salary_min': 100000,
            'salary_max': 150000
        },
        {
            'id': 3,
            'title': 'Frontend Developer',
            'company': 'Web Solutions',
            'location': 'Remote',
            'description': 'Looking for a frontend developer with React, JavaScript, and TypeScript expertise. Experience with modern CSS frameworks and responsive design.',
            'requirements': 'JavaScript, React, TypeScript, CSS, HTML, Bootstrap, Sass',
            'posted_date': '2023-11-05',
            'salary_min': 70000,
            'salary_max': 100000
        },
        {
            'id': 4,
            'title': 'Full Stack Engineer',
            'company': 'StartupTech',
            'location': 'Austin, TX',
            'description': 'Full stack engineer needed with experience in Node.js, React, and cloud platforms. AWS experience preferred.',
            'requirements': 'Node.js, React, JavaScript, AWS, Docker, MongoDB, Express',
            'posted_date': '2023-11-01',
            'salary_min': 80000,
            'salary_max': 120000
        },
        {
            'id': 5,
            'title': 'DevOps Engineer',
            'company': 'Cloud Systems Inc.',
            'location': 'Seattle, WA',
            'description': 'DevOps engineer with expertise in Kubernetes, Docker, and CI/CD pipelines. Experience with AWS and infrastructure as code.',
            'requirements': 'Kubernetes, Docker, AWS, Jenkins, Terraform, CI/CD, Linux',
            'posted_date': '2023-10-28',
            'salary_min': 95000,
            'salary_max': 140000
        }
    ]

    # Calculate match scores for sample jobs
    for job in sample_jobs:
        if user_skills:
            match_score = calculate_job_match_score(job, user_skills, user_projects, [])
            job['match_score'] = match_score
        else:
            job['match_score'] = 0

    # Sort by match score
    sample_jobs.sort(key=lambda x: x.get('match_score', 0), reverse=True)

    return sample_jobs

def analyze_resume_job_match(resume_text: str, job_description: str) -> Dict[str, Any]:
    """
    Analyze the match between a resume and job description using real NLP analysis.

    Args:
        resume_text (str): The text content of the resume
        job_description (str): The text content of the job description

    Returns:
        Dict[str, Any]: Analysis results including match score and suggestions
    """
    logger.info("Analyzing resume and job description match using real analysis")

    try:
        # Import CV parser for real analysis
        from cv_parser import extract_skills, calculate_match_score

        # Extract skills from resume using real analysis
        resume_skills_list = extract_skills(resume_text)
        resume_keywords = [skill['name'] for skill in resume_skills_list if skill.get('name')]

        # Extract keywords from job description using real analysis
        job_skills_list = extract_skills(job_description)
        job_keywords = [skill['name'] for skill in job_skills_list if skill.get('name')]

        # Also extract keywords using the existing function as fallback
        if not job_keywords:
            job_keywords = extract_keywords_from_text(job_description)
        if not resume_keywords:
            resume_keywords = extract_keywords_from_text(resume_text)

    except Exception as e:
        logger.error(f"Error in real CV analysis: {str(e)}")
        # Fallback to existing keyword extraction
        resume_keywords = extract_keywords_from_text(resume_text)
        job_keywords = extract_keywords_from_text(job_description)

    # Calculate match score
    matched_keywords = []
    missing_keywords = []

    for keyword in job_keywords:
        if keyword in resume_keywords:
            matched_keywords.append(keyword)
        else:
            missing_keywords.append(keyword)

    # Calculate match percentage
    if len(job_keywords) > 0:
        match_score = (len(matched_keywords) / len(job_keywords)) * 100
    else:
        match_score = 0

    # Generate suggestions
    suggestions = []

    if missing_keywords:
        suggestions.append({
            'title': 'Add Missing Keywords',
            'description': 'Consider adding these keywords to your resume:',
            'items': missing_keywords
        })

    suggestions.append({
        'title': 'Optimize Resume Format',
        'description': 'Ensure your resume is properly formatted:',
        'items': [
            'Use a clean, professional layout',
            'Include clear section headings',
            'Use bullet points for readability',
            'Keep to 1-2 pages in length'
        ]
    })

    # Use ML to predict if the resume is a good fit
    ml_prediction = ml_resume_analyzer.predict_resume_fit(resume_text, job_description)

    # Add ML-based suggestion
    fit_score = ml_prediction['fit_score'] * 100  # Convert to percentage
    fit_label = ml_prediction['fit_label']
    confidence = ml_prediction['confidence'] * 100  # Convert to percentage

    if fit_score < 50:
        suggestions.append({
            'title': 'Consider Job Fit',
            'description': f'Our AI analysis suggests this may not be an ideal match ({fit_label}, {confidence:.1f}% confidence):',
            'items': [
                'Consider if this role aligns with your skills and experience',
                'Focus on highlighting transferable skills',
                'Customize your resume more specifically for this position',
                'Look for roles that better match your experience and skills'
            ]
        })

    # Prepare result
    result = {
        'match_score': round(match_score, 1),
        'resume_keywords': resume_keywords,
        'job_keywords': job_keywords,
        'matched_keywords': matched_keywords,
        'missing_keywords': missing_keywords,
        'suggestions': suggestions,
        'analysis_date': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'ml_prediction': {
            'fit_score': round(fit_score, 1),
            'fit_label': fit_label,
            'confidence': round(confidence, 1)
        }
    }
    
    logger.info(f"Analysis complete. Keyword match: {result['match_score']}%, ML fit score: {fit_score:.1f}%")
    return result

def extract_keywords_from_text(text: str) -> List[str]:
    """
    Extract keywords from text.

    This is a simplified implementation for demonstration purposes.
    In a real application, you would use more sophisticated NLP techniques.

    Args:
        text (str): Text to extract keywords from

    Returns:
        List[str]: Extracted keywords
    """
    # Common keywords in resumes and job descriptions
    common_keywords = [
        "python", "javascript", "java", "c++", "react", "angular", "vue", "node.js",
        "html", "css", "sql", "nosql", "aws", "azure", "gcp", "docker", "kubernetes",
        "ci/cd", "agile", "scrum", "project management", "leadership", "communication",
        "teamwork", "problem solving", "critical thinking", "data analysis", "machine learning",
        "artificial intelligence", "deep learning", "data science", "big data", "analytics",
        "frontend", "backend", "full stack", "devops", "cloud", "security", "testing",
        "qa", "ui/ux", "design", "product management", "marketing", "sales", "customer service",
        "support", "operations", "finance", "accounting", "hr", "recruitment", "legal",
        "compliance", "research", "development", "engineering", "architecture", "infrastructure"
    ]

    # For demonstration, we'll randomly select some keywords based on the text length
    # In a real application, you would actually extract keywords from the text
    import random

    # Use text length to seed the random number generator for consistent results
    random.seed(len(text))

    # Select a random number of keywords based on text length
    num_keywords = max(5, min(20, len(text) // 100))

    # For job descriptions, include more keywords to simulate a more detailed posting
    if len(text) > 1000:  # Assume longer texts are job descriptions
        num_keywords += 10

    # Make sure we don't try to sample more keywords than are available
    num_keywords = min(num_keywords, len(common_keywords))

    # Randomly select keywords
    selected_keywords = random.sample(common_keywords, num_keywords)

    # For demonstration, we'll also extract some actual words from the text
    # to make it seem more realistic
    words = text.lower().split()
    actual_words = [word for word in words if len(word) > 5 and word not in ["the", "and", "that", "with", "for", "this"]]

    if actual_words:
        # Add some actual words from the text
        num_actual = min(10, len(actual_words))
        if num_actual > 0 and len(actual_words) > 0:  # Make sure we have words to sample
            selected_keywords.extend(random.sample(actual_words, num_actual))

    # Remove duplicates
    selected_keywords = list(set(selected_keywords))

    return selected_keywords

def analyze_cv_for_job(cv_text: str, job_requirements: str) -> Dict[str, Any]:
    """
    Analyze a CV against job requirements using Gemini API.
    Returns structured analysis with overall_assessment, strengths, weaknesses_or_gaps, and skills_match.
    """
    return gemini_api.analyze_cv_with_gemini(cv_text, job_requirements)

# Route Handlers
@app.route('/')
def index() -> str:
    """
    Render the landing page.

    Returns:
        str: Rendered HTML template
    """
    logger.info("Landing page accessed")
    return render_template('landing.html', current_year=datetime.datetime.now().year)

@app.route('/login', methods=['GET', 'POST'])
def login() -> str:
    """
    Handle user login.

    Returns:
        str: Rendered HTML template or redirect
    """
    if 'user_id' in session:
        # User is already logged in
        user = User.query.get(session['user_id'])
        if user:
            if user.is_admin():
                return redirect(url_for('admin_dashboard'))
            else:
                return redirect(url_for('user_dashboard'))

    error = None
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        role = request.form.get('role', 'user')  # 'user' or 'admin'

        if not username or not password:
            error = 'Username and password are required'
        else:
            user = User.query.filter_by(username=username).first()

            if user and user.check_password(password):
                # Check if role matches
                if role == 'admin' and not user.is_admin():
                    error = 'Invalid admin credentials'
                else:
                    # Login successful
                    session['user_id'] = user.id
                    user.update_last_login()

                    # Redirect based on role
                    if user.is_admin():
                        return redirect(url_for('admin_dashboard'))
                    else:
                        return redirect(url_for('user_dashboard'))
            else:
                error = 'Invalid username or password'

    return render_template('login.html', error=error, current_year=datetime.datetime.now().year)

@app.route('/register', methods=['GET', 'POST'])
def register() -> str:
    """
    Handle user registration.

    Returns:
        str: Rendered HTML template or redirect
    """
    if 'user_id' in session:
        # User is already logged in
        return redirect(url_for('index'))

    error = None
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        terms = request.form.get('terms')

        # Validate input
        if not username or not email or not password or not confirm_password:
            error = 'All fields are required'
        elif password != confirm_password:
            error = 'Passwords do not match'
        elif not terms:
            error = 'You must agree to the Terms of Service'
        else:
            # Check if username or email already exists
            existing_user = User.query.filter((User.username == username) | (User.email == email)).first()
            if existing_user:
                error = 'Username or email already exists'
            else:
                # Create new user
                new_user = User(
                    username=username,
                    email=email,
                    password=password,
                    role='user',
                    first_name=first_name,
                    last_name=last_name
                )

                db.session.add(new_user)
                db.session.commit()

                # Log in the new user
                session['user_id'] = new_user.id

                # Redirect to user dashboard
                return redirect(url_for('user_dashboard'))

    return render_template('register.html', error=error, current_year=datetime.datetime.now().year)

@app.route('/logout')
def logout() -> str:
    """
    Handle user logout.

    Returns:
        redirect: Redirect to login page
    """
    session.pop('user_id', None)
    flash('You have been logged out', 'info')
    return redirect(url_for('login'))

@app.route('/resume-scanner', methods=['GET', 'POST'])
@login_required
def resume_scanner() -> str:
    """
    Handle the resume scanner page with state management.

    This page allows users to upload their resume and paste a job description
    to get a match score and optimization suggestions.
    Maintains analysis state across page navigation.
    Requires user to be logged in.

    Returns:
        str: Rendered HTML template
    """
    analysis_result = None
    error = None
    success = None

    # Check for existing analysis in session or database on GET request
    if request.method == 'GET':
        analysis_result = get_stored_analysis_result()
        if analysis_result:
            logger.info("Loaded existing analysis from state management")
            success = "Previous analysis loaded. Upload a new CV to analyze again."

    if request.method == 'POST':
        logger.info("Resume scan request received")

        try:
            # Check if the post request has the file part
            if 'resume' not in request.files:
                logger.warning("No resume file in the request")
                error = "Please upload your resume"
            else:
                resume_file = request.files['resume']
                job_description = request.form.get('job_description', '')

                # Check if job description is provided
                if not job_description:
                    logger.warning("No job description provided")
                    error = "Please paste the job description"

                # Check if file is selected
                elif resume_file.filename == '':
                    logger.warning("No file selected")
                    error = "Please select a resume file"

                # Process the resume and job description
                elif resume_file and allowed_file(resume_file.filename) and job_description:
                    filename = secure_filename(resume_file.filename)
                    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

                    # Save the uploaded file
                    resume_file.save(filepath)
                    logger.info(f"Resume saved: {filepath}")

                    # Extract text from the uploaded resume file
                    try:
                        from cv_parser import extract_text_from_file
                        resume_text = extract_text_from_file(filepath)

                        if not resume_text or len(resume_text.strip()) < 50:
                            logger.error(f"Failed to extract meaningful text from {filepath}")
                            error = "Could not extract text from the uploaded file. Please ensure it's a valid PDF or DOCX file."
                            resume_text = None
                    except Exception as e:
                        logger.error(f"Error extracting text from {filepath}: {str(e)}")
                        error = f"Error processing file: {str(e)}"
                        resume_text = None

                    # Extract comprehensive CV information and analyze
                    if resume_text:
                        # Use expert CV extraction system
                        from expert_cv_analyzer import extract_cv_information_expert
                        cv_info = extract_cv_information_expert(resume_text)

                        # Perform job matching analysis
                        analysis_result = analyze_resume_job_match(resume_text, job_description)

                        # Add extracted CV information to analysis result
                        analysis_result['cv_info'] = cv_info
                        analysis_result['extracted_skills'] = []

                        # Combine all technical skills
                        if cv_info.get('technical_skills'):
                            tech_skills = cv_info['technical_skills']
                            for category, skills in tech_skills.items():
                                if isinstance(skills, list):
                                    analysis_result['extracted_skills'].extend(skills)

                        # Add soft skills
                        if cv_info.get('soft_skills'):
                            analysis_result['extracted_skills'].extend(cv_info['soft_skills'])

                        # Update applicant portal with extracted information
                        try:
                            update_applicant_profile_from_cv(session['user_id'], cv_info, filepath)
                            logger.info("Applicant profile updated with CV information")
                        except Exception as e:
                            logger.warning(f"Failed to update applicant profile: {str(e)}")

                        success = "Analysis completed successfully! Your profile has been updated with the extracted information."
                    else:
                        analysis_result = None

                    # Store the complete analysis results in session and database
                    if analysis_result:
                        store_analysis_result(analysis_result, filename)
                        logger.info("Analysis result stored in state management system")
                else:
                    logger.warning(f"Invalid file type: {resume_file.filename}")
                    error = "Please upload a PDF or DOCX file"

        except Exception as e:
            logger.error(f"Error processing resume scan: {str(e)}")
            error = f"An error occurred: {str(e)}"

    logger.info("Resume scanner page accessed")
    return render_template(
        'resume_scanner.html',
        analysis_result=analysis_result,
        error=error,
        success=success,
        current_year=datetime.datetime.now().year
    )

@app.route('/about')
def about() -> str:
    """
    Render the about page.

    Returns:
        str: Rendered HTML template
    """
    logger.info("About page accessed")
    return render_template('about.html', current_year=datetime.datetime.now().year)

@app.route('/applicant_portal', methods=['GET', 'POST'])
@login_required
def applicant_portal() -> str:
    """
    Enhanced applicant portal with comprehensive CV analysis using Expert System and Gemini AI.
    Requires user to be logged in.

    Returns:
        str: Rendered HTML template with complete CV analysis
    """
    cv_analysis = None
    error = None
    success = None

    # Check for existing analysis on GET request
    if request.method == 'GET':
        cv_analysis = get_stored_portal_analysis()
        if cv_analysis:
            logger.info("Loaded existing CV analysis for applicant portal")

    if request.method == 'POST':
        logger.info("CV upload attempt in applicant portal")

        try:
            # Check if the post request has the file part
            if 'file' not in request.files:
                logger.warning("No file part in the request")
                error = "No file part in the request"
                return render_template('applicant_portal.html', error=error, cv_analysis=cv_analysis)

            file = request.files['file']

            # If user does not select file, browser also submits an empty part without filename
            if file.filename == '':
                logger.warning("No file selected")
                error = "No file selected"
                return render_template('applicant_portal.html', error=error, cv_analysis=cv_analysis)

            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

                # Save the uploaded file
                file.save(filepath)
                logger.info(f"File saved: {filepath}")

                # Extract text from the uploaded CV file
                try:
                    from cv_parser import extract_text_from_file
                    cv_text = extract_text_from_file(filepath)

                    if cv_text and len(cv_text.strip()) >= 50:
                        # Use comprehensive CV analysis with Expert System and Gemini AI
                        cv_analysis = perform_comprehensive_cv_analysis(cv_text, filename, filepath)

                        # Store analysis in state management system
                        store_portal_analysis(cv_analysis)

                        # Update applicant profile
                        try:
                            update_applicant_profile_from_cv(session['user_id'], cv_analysis, filepath)
                            logger.info("Applicant profile updated from portal upload")
                        except Exception as e:
                            logger.warning(f"Failed to update applicant profile: {str(e)}")

                        success = f"CV '{filename}' analyzed successfully! Complete analysis with {len(cv_analysis.get('technical_skills', {}).get('programming_languages', []))} programming languages detected."

                    else:
                        logger.error(f"Failed to extract meaningful text from {filepath}")
                        error = "Could not extract meaningful text from the uploaded file. Please ensure it's a valid PDF or DOCX file."

                except Exception as e:
                    logger.error(f"Error processing CV file {filepath}: {str(e)}")
                    error = f"Error processing file: {str(e)}"

            else:
                logger.warning(f"Invalid file type: {file.filename}")
                error = "File type not allowed. Please upload PDF or DOCX files only."

        except RequestEntityTooLarge:
            logger.error("File too large")
            error = "File too large. Maximum size is 16MB."
        except Exception as e:
            logger.error(f"Error processing upload: {str(e)}")
            error = f"An error occurred: {str(e)}"

    logger.info("Applicant portal page accessed")
    return render_template(
        'applicant_portal.html',
        cv_analysis=cv_analysis,
        error=error,
        success=success,
        current_year=datetime.datetime.now().year
    )

@app.route('/upload-cv', methods=['POST'])
@login_required
def upload_cv() -> str:
    """
    Handle CV upload from the user dashboard.

    Returns:
        str: Redirect to user dashboard with success/error message
    """
    if request.method == 'POST':
        logger.info("CV upload attempt from user dashboard")

        try:
            # Check if the post request has the file part
            if 'file' not in request.files:
                logger.warning("No file part in the request")
                flash("No file part", "error")
                return redirect(url_for('user_dashboard'))

            file = request.files['file']

            # If user does not select file, browser also submits an empty part without filename
            if file.filename == '':
                logger.warning("No file selected")
                flash("No file selected", "error")
                return redirect(url_for('user_dashboard'))

            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

                # Save the uploaded file
                file.save(filepath)
                logger.info(f"File saved: {filepath}")

                # Get current user
                user = User.query.get(session['user_id'])

                # Extract text from the uploaded CV file and analyze it
                try:
                    from cv_parser import extract_text_from_file
                    cv_text = extract_text_from_file(filepath)

                    if cv_text and len(cv_text.strip()) >= 50:
                        name, contact_info, skills, education = extract_info(cv_text)
                    else:
                        logger.error(f"Failed to extract meaningful text from {filepath}")
                        flash("Could not extract text from the uploaded file. Please ensure it's a valid PDF or DOCX file.", "error")
                        name, contact_info, skills, education = "Name not found", "Contact not found", ["General Skills"], "Education not found"
                except Exception as e:
                    logger.error(f"Error processing CV file {filepath}: {str(e)}")
                    flash(f"Error processing file: {str(e)}", "error")
                    name, contact_info, skills, education = "Name not found", "Contact not found", ["General Skills"], "Education not found"

                flash(f"CV '{filename}' uploaded and analyzed successfully!", "success")
                return redirect(url_for('user_dashboard'))
            else:
                logger.warning(f"Invalid file type: {file.filename}")
                flash("File type not allowed", "error")
                return redirect(url_for('user_dashboard'))

        except Exception as e:
            logger.error(f"Error processing CV upload: {str(e)}")
            flash(f"An error occurred: {str(e)}", "error")
            return redirect(url_for('user_dashboard'))

    # If not POST, redirect to dashboard
    return redirect(url_for('user_dashboard'))

@app.route('/user-dashboard')
@login_required
def user_dashboard() -> str:
    """
    Render the user dashboard page.

    Returns:
        str: Rendered HTML template
    """
    logger.info("User dashboard accessed")

    # Get current user
    user = User.query.get(session['user_id'])
    if not user:
        session.pop('user_id', None)
        return redirect(url_for('login'))

    # Get user's applications and stats
    stats = {
        'applications': 0,
        'views': 0,
        'interviews': 0,
        'match_score': 75  # Default value
    }

    # Get recent applications
    recent_applications = []

    # Get recommended jobs
    recommended_jobs = []

    # Get CV history
    cv_history = []

    return render_template(
        'user_dashboard.html',
        current_user=user,
        stats=stats,
        recent_applications=recent_applications,
        recommended_jobs=recommended_jobs,
        cv_history=cv_history,
        current_year=datetime.datetime.now().year
    )

@app.route('/admin-dashboard')
@admin_required
def admin_dashboard() -> str:
    """
    Render the admin dashboard page.

    Returns:
        str: Rendered HTML template
    """
    logger.info("Admin dashboard accessed")

    # Get current admin user
    admin = User.query.get(session['user_id'])
    if not admin or not admin.is_admin():
        return redirect(url_for('login'))

    # Get system stats
    total_applications = JobApplication.query.count()
    total_users = User.query.filter_by(role='user').count()
    total_jobs = Job.query.filter_by(is_active=True).count()
    approved_applications = JobApplication.query.filter_by(status='Approved').count()
    conversion_rate = (approved_applications / total_applications * 100) if total_applications > 0 else 0

    stats = {
        'applications': total_applications,
        'users': total_users,
        'jobs': total_jobs,
        'conversion_rate': round(conversion_rate, 1)
    }

    # Get recent activities (last 10 activities)
    recent_activities = []

    # Recent users
    recent_users = User.query.filter_by(role='user').order_by(User.created_at.desc()).limit(3).all()
    for user in recent_users:
        recent_activities.append({
            'icon': 'fas fa-user-plus',
            'text': f'New user registered: {user.first_name} {user.last_name}',
            'time': user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else 'Unknown'
        })

    # Recent applications
    recent_apps = JobApplication.query.join(Applicant).join(Job).order_by(JobApplication.application_date.desc()).limit(3).all()
    for app in recent_apps:
        recent_activities.append({
            'icon': 'fas fa-file-alt',
            'text': f'New application: {app.applicant.name} applied for {app.job.title}',
            'time': app.application_date.strftime('%Y-%m-%d %H:%M') if app.application_date else 'Unknown'
        })

    # Recent jobs
    recent_jobs = Job.query.order_by(Job.posted_date.desc()).limit(2).all()
    for job in recent_jobs:
        recent_activities.append({
            'icon': 'fas fa-briefcase',
            'text': f'New job posted: {job.title} at {job.company}',
            'time': job.posted_date.strftime('%Y-%m-%d %H:%M') if job.posted_date else 'Unknown'
        })

    # Sort activities by time (most recent first)
    recent_activities = sorted(recent_activities, key=lambda x: x['time'], reverse=True)[:5]

    # Get applications for admin view (last 10)
    applications_query = JobApplication.query.join(Applicant).join(Job).order_by(JobApplication.application_date.desc()).limit(10).all()
    applications = []
    for app in applications_query:
        applications.append({
            'id': app.id,
            'applicant_name': app.applicant.name if app.applicant else 'Unknown',
            'job_title': app.job.title if app.job else 'Unknown',
            'date_applied': app.application_date.strftime('%Y-%m-%d') if app.application_date else 'Unknown',
            'status': app.status
        })

    # Get users for admin view
    users = User.query.all()

    return render_template(
        'admin_dashboard.html',
        current_user=admin,
        stats=stats,
        recent_activities=recent_activities,
        applications=applications,
        users=users,
        current_year=datetime.datetime.now().year
    )

@app.route('/admin_panel')
@admin_required
def admin_panel() -> str:
    """
    Render the admin panel page.

    Returns:
        str: Rendered HTML template
    """
    logger.info("Admin panel accessed")

    # Get a list of all uploaded files
    uploaded_files = []
    try:
        for file in os.listdir(app.config['UPLOAD_FOLDER']):
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], file)
            if os.path.isfile(file_path):
                file_stats = os.stat(file_path)
                uploaded_files.append({
                    'name': file,
                    'size': file_stats.st_size,
                    'modified': datetime.datetime.fromtimestamp(file_stats.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
    except Exception as e:
        logger.error(f"Error listing uploaded files: {str(e)}")

    return render_template('admin_panel.html', uploaded_files=uploaded_files, current_year=datetime.datetime.now().year)

@app.route('/job_listings')
def job_listings() -> str:
    """
    Render the job listings page with intelligent job matching based on user's CV.

    Returns:
        str: Rendered HTML template
    """
    logger.info("Job listings page accessed")

    # Get user's skills and projects from their CV if logged in
    user_skills = []
    user_projects = []
    user_experience = []
    match_scores = {}

    if 'user_id' in session:
        try:
            # Get user's applicant record
            applicant = Applicant.query.filter_by(user_id=session['user_id']).first()

            if applicant and applicant.cv_file_path:
                # Get CV information from the latest analysis
                cv_record = CV.query.filter_by(applicant_id=applicant.id).order_by(CV.analysis_date.desc()).first()

                if cv_record and cv_record.extracted_text:
                    try:
                        cv_info = json.loads(cv_record.extracted_text)

                        # Extract user's skills
                        technical_skills = cv_info.get('technical_skills', {})
                        if isinstance(technical_skills, dict):
                            for category, skills in technical_skills.items():
                                if isinstance(skills, list):
                                    user_skills.extend(skills)

                        soft_skills = cv_info.get('soft_skills', [])
                        if isinstance(soft_skills, list):
                            user_skills.extend(soft_skills)

                        # Extract user's projects
                        user_projects = cv_info.get('projects', [])

                        # Extract user's experience
                        user_experience = cv_info.get('work_experience', [])

                        logger.info(f"User skills extracted: {len(user_skills)} skills, {len(user_projects)} projects")

                    except json.JSONDecodeError:
                        logger.warning("Failed to parse CV information from database")

        except Exception as e:
            logger.warning(f"Error extracting user CV information: {str(e)}")

    # Get all jobs from database
    try:
        all_jobs = Job.query.filter_by(is_active=True).order_by(Job.posted_date.desc()).all()
        jobs = []

        for job in all_jobs:
            job_dict = job.to_dict()

            # Calculate match score if user has skills
            if user_skills:
                match_score = calculate_job_match_score(job_dict, user_skills, user_projects, user_experience)
                job_dict['match_score'] = match_score
                match_scores[job.id] = match_score
            else:
                job_dict['match_score'] = 0

            jobs.append(job_dict)

        # Sort jobs by match score if user is logged in and has skills
        if user_skills:
            jobs.sort(key=lambda x: x.get('match_score', 0), reverse=True)
            logger.info(f"Jobs sorted by match score for user with {len(user_skills)} skills")

    except Exception as e:
        logger.error(f"Error fetching jobs from database: {str(e)}")
        # Fallback to sample jobs
        jobs = get_sample_jobs_with_matching(user_skills, user_projects)

    return render_template(
        'job_listings.html',
        jobs=jobs,
        user_skills=user_skills,
        has_cv=len(user_skills) > 0,
        current_year=datetime.datetime.now().year
    )

@app.route('/contact', methods=['GET', 'POST'])
def contact() -> str:
    """
    Handle the contact page and form submissions.

    Returns:
        str: Rendered HTML template
    """
    if request.method == 'POST':
        try:
            name = request.form['name']
            email = request.form['email']
            message = request.form['message']

            logger.info(f"Contact form submission from {name} ({email})")

            # Here you would typically save the message to a database
            # or send an email, but for now we'll just return a success message

            # Create a timestamp for the message
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # In a real application, you would store this in a database
            contact_message = {
                'name': name,
                'email': email,
                'message': message,
                'timestamp': timestamp,
                'ip_address': request.remote_addr
            }

            # For demonstration, we'll just log it
            logger.info(f"Contact message: {contact_message}")

            flash("Thank you for contacting us! We'll get back to you soon.", "success")
            return render_template(
                'contact.html',
                message_sent="Thank you for contacting us! We'll get back to you soon.",
                submission_time=timestamp,
                current_year=datetime.datetime.now().year
            )

        except Exception as e:
            logger.error(f"Error processing contact form: {str(e)}")
            flash(f"An error occurred: {str(e)}", "error")
            return render_template('contact.html', error=f"An error occurred: {str(e)}")

    logger.info("Contact page accessed")
    return render_template('contact.html', current_year=datetime.datetime.now().year)

@app.route('/api/job_search', methods=['GET'])
def api_job_search() -> Any:
    """
    API endpoint for job search functionality.

    Returns:
        Response: JSON response with job listings
    """
    query = request.args.get('query', '')
    logger.info(f"API job search with query: {query}")

    # Simulate job search results
    jobs = [
        {
            'id': 1,
            'title': 'Senior Python Developer',
            'company': 'Tech Innovations Inc.',
            'location': 'New York, NY',
            'description': 'Looking for an experienced Python developer with Flask experience.',
            'posted_date': '2023-11-15'
        },
        {
            'id': 2,
            'title': 'Data Scientist',
            'company': 'Data Analytics Co.',
            'location': 'San Francisco, CA',
            'description': 'Seeking a data scientist with experience in machine learning and NLP.',
            'posted_date': '2023-11-10'
        }
    ]

    # Filter jobs based on query
    if query:
        filtered_jobs = [job for job in jobs if query.lower() in job['title'].lower() or
                                               query.lower() in job['description'].lower()]
    else:
        filtered_jobs = jobs

    return jsonify({
        'status': 'success',
        'count': len(filtered_jobs),
        'jobs': filtered_jobs
    })

@app.route('/job-details/<int:job_id>')
@login_required
def job_details(job_id: int) -> str:
    """
    Display job details.

    Args:
        job_id: The ID of the job to display

    Returns:
        str: Rendered HTML template with job details
    """
    logger.info(f"Job details request for ID: {job_id}")

    # In a real application, you would fetch the job from the database
    # For demonstration, we'll create a sample job
    job = {
        'id': job_id,
        'title': 'Senior Python Developer',
        'company': 'Tech Innovations Inc.',
        'location': 'San Francisco, CA',
        'description': 'We are looking for an experienced Python developer...',
        'requirements': 'Bachelor\'s degree in Computer Science, 5+ years of Python experience...',
        'salary': '$120,000 - $150,000',
        'posted_date': '2023-05-15'
    }

    return render_template('job_details.html', job=job, current_year=datetime.datetime.now().year)

@app.route('/download-cv/<int:cv_id>')
@login_required
def download_cv(cv_id: int) -> str:
    """
    Download a CV file.

    Args:
        cv_id: The ID of the CV to download

    Returns:
        str: The CV file for download
    """
    logger.info(f"CV download request for ID: {cv_id}")

    # In a real application, you would fetch the CV from the database
    # and return the file for download

    # For demonstration, we'll just redirect to the dashboard
    flash("CV download functionality is not implemented yet", "info")
    return redirect(url_for('user_dashboard'))

@app.route('/view-cv-analysis/<int:cv_id>')
@login_required
def view_cv_analysis(cv_id: int) -> str:
    """
    View the analysis of a CV.

    Args:
        cv_id: The ID of the CV to view analysis for

    Returns:
        str: Rendered HTML template with CV analysis
    """
    logger.info(f"CV analysis view request for ID: {cv_id}")

    # In a real application, you would fetch the CV analysis from the database

    # For demonstration, we'll just redirect to the dashboard
    flash("CV analysis view functionality is not implemented yet", "info")
    return redirect(url_for('user_dashboard'))

@app.route('/terms')
def terms() -> str:
    """
    Render the terms of service page.

    Returns:
        str: Rendered HTML template
    """
    logger.info("Terms of service page accessed")
    return render_template('terms.html', current_year=datetime.datetime.now().year)

@app.route('/privacy')
def privacy() -> str:
    """
    Render the privacy policy page.

    Returns:
        str: Rendered HTML template
    """
    logger.info("Privacy policy page accessed")
    return render_template('privacy.html', current_year=datetime.datetime.now().year)

@app.route('/forgot-password')
def forgot_password() -> str:
    """
    Render the forgot password page.

    Returns:
        str: Rendered HTML template
    """
    logger.info("Forgot password page accessed")
    return render_template('forgot_password.html', current_year=datetime.datetime.now().year)


# Admin Job Management API Routes
@app.route('/api/admin/jobs', methods=['GET'])
@admin_required
def api_get_jobs() -> Any:
    """
    API endpoint to get all jobs for admin.

    Returns:
        Response: JSON response with job listings
    """
    try:
        logger.info("Admin jobs API accessed")

        # Get all jobs from database
        jobs = Job.query.all()

        # Convert to dictionary format
        jobs_data = []
        for job in jobs:
            job_dict = job.to_dict()
            jobs_data.append(job_dict)

        return jsonify({
            'status': 'success',
            'count': len(jobs_data),
            'jobs': jobs_data
        })

    except Exception as e:
        logger.error(f"Error fetching jobs: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/admin/jobs/<int:job_id>', methods=['GET'])
@admin_required
def api_get_job(job_id: int) -> Any:
    """
    API endpoint to get a specific job by ID.

    Args:
        job_id: The ID of the job to retrieve

    Returns:
        Response: JSON response with job details
    """
    try:
        logger.info(f"Admin get job API accessed for job ID: {job_id}")

        job = Job.query.get_or_404(job_id)

        return jsonify({
            'status': 'success',
            'job': job.to_dict()
        })

    except Exception as e:
        logger.error(f"Error fetching job {job_id}: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/admin/jobs', methods=['POST'])
@admin_required
def api_create_job() -> Any:
    """
    API endpoint to create a new job.

    Returns:
        Response: JSON response with creation status
    """
    try:
        logger.info("Admin create job API accessed")

        # Get form data
        title = request.form.get('title')
        company = request.form.get('company')
        location = request.form.get('location')
        description = request.form.get('description')
        requirements = request.form.get('requirements', '')
        job_type = request.form.get('job_type', '')
        experience_level = request.form.get('experience_level', '')
        salary_min = request.form.get('salary_min')
        salary_max = request.form.get('salary_max')
        closing_date = request.form.get('closing_date')
        is_active = request.form.get('is_active', 'true').lower() == 'true'
        skills_str = request.form.get('skills', '')

        # Validate required fields
        if not title or not company or not location or not description:
            return jsonify({
                'status': 'error',
                'message': 'Title, company, location, and description are required'
            }), 400

        # Create new job
        new_job = Job(
            title=title,
            company=company,
            location=location,
            description=description,
            requirements=requirements,
            job_type=job_type if job_type else None,
            experience_level=experience_level if experience_level else None,
            salary_min=float(salary_min) if salary_min else None,
            salary_max=float(salary_max) if salary_max else None,
            closing_date=datetime.datetime.strptime(closing_date, '%Y-%m-%d').date() if closing_date else None,
            is_active=is_active
        )

        # Add skills if provided
        if skills_str:
            skill_names = [skill.strip() for skill in skills_str.split(',') if skill.strip()]
            for skill_name in skill_names:
                # Check if skill exists, create if not
                skill = Skill.query.filter_by(name=skill_name).first()
                if not skill:
                    skill = Skill(name=skill_name, category='Technical')
                    db.session.add(skill)
                new_job.skills.append(skill)

        db.session.add(new_job)
        db.session.commit()

        logger.info(f"New job created: {title} at {company}")

        return jsonify({
            'status': 'success',
            'message': 'Job created successfully',
            'job': new_job.to_dict()
        })

    except Exception as e:
        logger.error(f"Error creating job: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/admin/jobs/<int:job_id>', methods=['PUT'])
@admin_required
def api_update_job(job_id: int) -> Any:
    """
    API endpoint to update an existing job.

    Args:
        job_id: The ID of the job to update

    Returns:
        Response: JSON response with update status
    """
    try:
        logger.info(f"Admin update job API accessed for job ID: {job_id}")

        job = Job.query.get_or_404(job_id)

        # Get form data
        title = request.form.get('title')
        company = request.form.get('company')
        location = request.form.get('location')
        description = request.form.get('description')
        requirements = request.form.get('requirements', '')
        job_type = request.form.get('job_type', '')
        experience_level = request.form.get('experience_level', '')
        salary_min = request.form.get('salary_min')
        salary_max = request.form.get('salary_max')
        closing_date = request.form.get('closing_date')
        is_active = request.form.get('is_active', 'true').lower() == 'true'
        skills_str = request.form.get('skills', '')

        # Validate required fields
        if not title or not company or not location or not description:
            return jsonify({
                'status': 'error',
                'message': 'Required fields: title, company, location, and description cannot be empty'
            }), 400

        # Validate salary values if provided
        if salary_min and salary_max:
            try:
                salary_min = float(salary_min)
                salary_max = float(salary_max)
                if salary_min > salary_max:
                    return jsonify({
                        'status': 'error',
                        'message': 'Minimum salary cannot be greater than maximum salary'
                    }), 400
            except ValueError:
                return jsonify({
                    'status': 'error',
                    'message': 'Invalid salary values. Must be numeric.'
                }), 400

        # Validate closing date if provided
        if closing_date:
            try:
                closing_date = datetime.datetime.strptime(closing_date, '%Y-%m-%d').date()
                if closing_date < datetime.date.today():
                    return jsonify({
                        'status': 'error',
                        'message': 'Closing date cannot be in the past'
                    }), 400
            except ValueError:
                return jsonify({
                    'status': 'error',
                    'message': 'Invalid date format. Use YYYY-MM-DD'
                }), 400

        # Update job fields
        job.title = title
        job.company = company
        job.location = location
        job.description = description
        job.requirements = requirements
        job.job_type = job_type
        job.experience_level = experience_level
        job.is_active = is_active

        if salary_min and salary_max:
            job.salary_min = salary_min
            job.salary_max = salary_max

        if closing_date:
            job.closing_date = closing_date

        # Update skills
        if skills_str:
            # Clear existing skills
            job.skills = []
            # Add new skills
            skills = [s.strip() for s in skills_str.split(',') if s.strip()]
            for skill_name in skills:
                skill = Skill.query.filter_by(name=skill_name).first()
                if not skill:
                    skill = Skill(name=skill_name, category='Technical')
                    db.session.add(skill)
                job.skills.append(skill)

        db.session.commit()

        logger.info(f"Job updated: {title} at {company}")

        return jsonify({
            'status': 'success',
            'message': 'Job updated successfully',
            'job': job.to_dict()
        })

    except Exception as e:
        logger.error(f"Error updating job {job_id}: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/admin/jobs/<int:job_id>', methods=['DELETE'])
@admin_required
def api_delete_job(job_id: int) -> Any:
    """
    API endpoint to delete a job.

    Args:
        job_id: The ID of the job to delete

    Returns:
        Response: JSON response with deletion status
    """
    try:
        logger.info(f"Admin delete job API accessed for job ID: {job_id}")

        job = Job.query.get_or_404(job_id)
        job_title = job.title
        job_company = job.company

        # Remove the job (this will also remove associated relationships)
        db.session.delete(job)
        db.session.commit()

        logger.info(f"Job deleted: {job_title} at {job_company}")

        return jsonify({
            'status': 'success',
            'message': 'Job deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error deleting job {job_id}: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


# Admin Application Management API Routes
@app.route('/api/admin/applications', methods=['GET'])
@admin_required
def api_get_applications() -> Any:
    """
    API endpoint to get all applications for admin.

    Returns:
        Response: JSON response with application listings
    """
    try:
        logger.info("Admin applications API accessed")

        # Get all applications from database
        applications = JobApplication.query.join(Applicant).join(Job).all()

        # Convert to dictionary format
        applications_data = []
        for app in applications:
            app_dict = app.to_dict()
            # Add additional information
            app_dict['applicant_name'] = app.applicant.name if app.applicant else 'Unknown'
            app_dict['job_title'] = app.job.title if app.job else 'Unknown'
            app_dict['job_company'] = app.job.company if app.job else 'Unknown'
            applications_data.append(app_dict)

        return jsonify({
            'status': 'success',
            'count': len(applications_data),
            'applications': applications_data
        })

    except Exception as e:
        logger.error(f"Error fetching applications: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/admin/applications/<int:application_id>', methods=['GET'])
@admin_required
def api_get_application(application_id: int) -> Any:
    """
    API endpoint to get a specific application by ID.

    Args:
        application_id: The ID of the application to retrieve

    Returns:
        Response: JSON response with application details
    """
    try:
        logger.info(f"Admin get application API accessed for application ID: {application_id}")

        application = JobApplication.query.get_or_404(application_id)
        app_dict = application.to_dict()

        # Add additional information
        app_dict['applicant_name'] = application.applicant.name if application.applicant else 'Unknown'
        app_dict['job_title'] = application.job.title if application.job else 'Unknown'
        app_dict['job_company'] = application.job.company if application.job else 'Unknown'

        return jsonify({
            'status': 'success',
            'application': app_dict
        })

    except Exception as e:
        logger.error(f"Error fetching application {application_id}: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/admin/applications/<int:application_id>', methods=['PUT'])
@admin_required
def api_update_application(application_id: int) -> Any:
    """
    API endpoint to update an application status.

    Args:
        application_id: The ID of the application to update

    Returns:
        Response: JSON response with update status
    """
    try:
        logger.info(f"Admin update application API accessed for application ID: {application_id}")

        application = JobApplication.query.get_or_404(application_id)

        # Get form data
        status = request.form.get('status')
        notes = request.form.get('notes', '')

        # Validate required fields
        if not status:
            return jsonify({
                'status': 'error',
                'message': 'Status is required'
            }), 400

        # Update application fields
        application.status = status
        application.notes = notes

        db.session.commit()

        logger.info(f"Application updated: {application_id} - Status: {status}")

        return jsonify({
            'status': 'success',
            'message': 'Application updated successfully',
            'application': application.to_dict()
        })

    except Exception as e:
        logger.error(f"Error updating application {application_id}: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/admin/applications/<int:application_id>', methods=['DELETE'])
@admin_required
def api_delete_application(application_id: int) -> Any:
    """
    API endpoint to delete an application.

    Args:
        application_id: The ID of the application to delete

    Returns:
        Response: JSON response with deletion status
    """
    try:
        logger.info(f"Admin delete application API accessed for application ID: {application_id}")

        application = JobApplication.query.get_or_404(application_id)

        applicant_name = application.applicant.name if application.applicant else 'Unknown'
        job_title = application.job.title if application.job else 'Unknown'

        # Remove the application
        db.session.delete(application)
        db.session.commit()

        logger.info(f"Application deleted: {applicant_name} - {job_title}")

        return jsonify({
            'status': 'success',
            'message': 'Application deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error deleting application {application_id}: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


# Admin User Management API Routes
@app.route('/api/admin/users', methods=['GET'])
@admin_required
def api_get_users() -> Any:
    """
    API endpoint to get all users for admin.

    Returns:
        Response: JSON response with user listings
    """
    try:
        logger.info("Admin users API accessed")

        # Get all users from database
        users = User.query.all()

        # Convert to dictionary format
        users_data = []
        for user in users:
            user_dict = user.to_dict()
            users_data.append(user_dict)

        return jsonify({
            'status': 'success',
            'count': len(users_data),
            'users': users_data
        })

    except Exception as e:
        logger.error(f"Error fetching users: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/admin/users/<int:user_id>', methods=['GET'])
@admin_required
def api_get_user(user_id: int) -> Any:
    """
    API endpoint to get a specific user by ID.

    Args:
        user_id: The ID of the user to retrieve

    Returns:
        Response: JSON response with user details
    """
    try:
        logger.info(f"Admin get user API accessed for user ID: {user_id}")

        user = User.query.get_or_404(user_id)

        return jsonify({
            'status': 'success',
            'user': user.to_dict()
        })

    except Exception as e:
        logger.error(f"Error fetching user {user_id}: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/admin/users', methods=['POST'])
@admin_required
def api_create_user() -> Any:
    """
    API endpoint to create a new user.

    Returns:
        Response: JSON response with creation status
    """
    try:
        logger.info("Admin create user API accessed")

        # Get form data
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        role = request.form.get('role', 'user')
        first_name = request.form.get('first_name', '')
        last_name = request.form.get('last_name', '')

        # Validate required fields
        if not username or not email or not password:
            return jsonify({
                'status': 'error',
                'message': 'Username, email, and password are required'
            }), 400

        # Check if username or email already exists
        existing_user = User.query.filter((User.username == username) | (User.email == email)).first()
        if existing_user:
            return jsonify({
                'status': 'error',
                'message': 'Username or email already exists'
            }), 400

        # Create new user
        new_user = User(
            username=username,
            email=email,
            password=password,
            role=role,
            first_name=first_name,
            last_name=last_name
        )

        db.session.add(new_user)
        db.session.commit()

        logger.info(f"New user created: {username} ({role})")

        return jsonify({
            'status': 'success',
            'message': 'User created successfully',
            'user': new_user.to_dict()
        })

    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/admin/users/<int:user_id>', methods=['PUT'])
@admin_required
def api_update_user(user_id: int) -> Any:
    """
    API endpoint to update an existing user.

    Args:
        user_id: The ID of the user to update

    Returns:
        Response: JSON response with update status
    """
    try:
        logger.info(f"Admin update user API accessed for user ID: {user_id}")

        user = User.query.get_or_404(user_id)

        # Get form data
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        role = request.form.get('role')
        first_name = request.form.get('first_name', '')
        last_name = request.form.get('last_name', '')

        # Validate required fields
        if not username or not email:
            return jsonify({
                'status': 'error',
                'message': 'Username and email are required'
            }), 400

        # Check if username or email already exists (excluding current user)
        existing_user = User.query.filter(
            ((User.username == username) | (User.email == email)) & (User.id != user_id)
        ).first()
        if existing_user:
            return jsonify({
                'status': 'error',
                'message': 'Username or email already exists'
            }), 400

        # Update user fields
        user.username = username
        user.email = email
        if password:  # Only update password if provided
            user.set_password(password)
        user.role = role
        user.first_name = first_name
        user.last_name = last_name

        db.session.commit()

        logger.info(f"User updated: {username}")

        return jsonify({
            'status': 'success',
            'message': 'User updated successfully',
            'user': user.to_dict()
        })

    except Exception as e:
        logger.error(f"Error updating user {user_id}: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/admin/users/<int:user_id>', methods=['DELETE'])
@admin_required
def api_delete_user(user_id: int) -> Any:
    """
    API endpoint to delete a user.

    Args:
        user_id: The ID of the user to delete

    Returns:
        Response: JSON response with deletion status
    """
    try:
        logger.info(f"Admin delete user API accessed for user ID: {user_id}")

        user = User.query.get_or_404(user_id)

        # Prevent deleting the current admin user
        current_user_id = session.get('user_id')
        if user_id == current_user_id:
            return jsonify({
                'status': 'error',
                'message': 'Cannot delete your own account'
            }), 400

        username = user.username
        user_role = user.role

        # Remove the user (this will also remove associated relationships)
        db.session.delete(user)
        db.session.commit()

        logger.info(f"User deleted: {username} ({user_role})")

        return jsonify({
            'status': 'success',
            'message': 'User deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error deleting user {user_id}: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


# Admin Settings Management API Routes
@app.route('/api/admin/settings', methods=['GET'])
@admin_required
def api_get_settings() -> Any:
    """
    API endpoint to get all settings for admin.

    Returns:
        Response: JSON response with settings
    """
    try:
        logger.info("Admin settings API accessed")

        # Get all settings from database
        settings = Settings.query.all()

        # Convert to dictionary format
        settings_data = {}
        for setting in settings:
            settings_data[setting.key] = setting.value

        # Add default settings if they don't exist
        default_settings = {
            'site_name': 'Applicant Tracking System',
            'max_file_size': '16',
            'allowed_file_types': 'pdf, docx',
            'smtp_server': '',
            'smtp_port': '587',
            'email_username': ''
        }

        for key, default_value in default_settings.items():
            if key not in settings_data:
                settings_data[key] = default_value

        return jsonify({
            'status': 'success',
            'settings': settings_data
        })

    except Exception as e:
        logger.error(f"Error fetching settings: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/test_cv_extraction')
def test_cv_extraction() -> str:
    """
    Test page for CV extraction functionality.

    Returns:
        str: Rendered HTML template
    """
    return render_template('test_cv_extraction.html')


@app.route('/api/test_cv_extraction', methods=['POST'])
def api_test_cv_extraction() -> Any:
    """
    Test endpoint for CV extraction functionality.

    Returns:
        Response: JSON response with extracted CV information
    """
    try:
        if 'cv_file' not in request.files:
            return jsonify({
                'status': 'error',
                'message': 'No CV file provided'
            }), 400

        file = request.files['cv_file']
        if file.filename == '':
            return jsonify({
                'status': 'error',
                'message': 'No file selected'
            }), 400

        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # Extract text from file
            from cv_parser import extract_text_from_file
            cv_text = extract_text_from_file(filepath)

            if cv_text and len(cv_text.strip()) >= 50:
                # Use the enhanced extraction
                from gemini_api import extract_cv_information
                cv_info = extract_cv_information(cv_text)

                return jsonify({
                    'status': 'success',
                    'message': 'CV extracted successfully',
                    'cv_info': cv_info,
                    'text_length': len(cv_text)
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'Could not extract meaningful text from the file'
                }), 400
        else:
            return jsonify({
                'status': 'error',
                'message': 'Invalid file type. Please upload PDF or DOCX files only.'
            }), 400

    except Exception as e:
        logger.error(f"Error in CV extraction test: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


@app.route('/api/admin/settings', methods=['POST'])
@admin_required
def api_save_settings() -> Any:
    """
    API endpoint to save settings.

    Returns:
        Response: JSON response with save status
    """
    try:
        logger.info("Admin save settings API accessed")

        # Get form data
        settings_data = {
            'site_name': request.form.get('site_name', ''),
            'max_file_size': request.form.get('max_file_size', '16'),
            'allowed_file_types': request.form.get('allowed_file_types', 'pdf, docx'),
            'smtp_server': request.form.get('smtp_server', ''),
            'smtp_port': request.form.get('smtp_port', '587'),
            'email_username': request.form.get('email_username', '')
        }

        # Save or update each setting
        for key, value in settings_data.items():
            setting = Settings.query.filter_by(key=key).first()
            if setting:
                setting.value = value
            else:
                setting = Settings(key=key, value=value)
                db.session.add(setting)

        db.session.commit()

        logger.info("Settings saved successfully")

        return jsonify({
            'status': 'success',
            'message': 'Settings saved successfully'
        })

    except Exception as e:
        logger.error(f"Error saving settings: {str(e)}")
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


# Error handlers
@app.errorhandler(404)
def page_not_found(e) -> Tuple[str, int]:
    """
    Handle 404 errors.

    Args:
        e: The error

    Returns:
        Tuple[str, int]: Rendered HTML template and status code
    """
    logger.warning(f"404 error: {request.path}")
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_server_error(e) -> Tuple[str, int]:
    """
    Handle 500 errors.

    Args:
        e: The error

    Returns:
        Tuple[str, int]: Rendered HTML template and status code
    """
    logger.error(f"500 error: {str(e)}")
    return render_template('errors/500.html'), 500

# Application entry point
if __name__ == '__main__':
    logger.info("Starting application")
    app.run(debug=Config.DEBUG)
