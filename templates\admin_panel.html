{% extends "base.html" %}

{% block title %}Admin Panel | Applicant Tracking System{% endblock %}

{% block header_title %}Admin Panel{% endblock %}
{% block header_subtitle %}Manage Applications and System Settings{% endblock %}

{% block description %}Administrative dashboard for managing applications, users, and system settings.{% endblock %}

{% block content %}
    <div class="admin-section fade-in">
        <div class="admin-sidebar slide-in-left">
            <div class="admin-nav">
                <a href="#dashboard" class="admin-nav-item active">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="#applications" class="admin-nav-item">
                    <i class="fas fa-file-alt"></i> Applications
                </a>
                <a href="#jobs" class="admin-nav-item">
                    <i class="fas fa-briefcase"></i> Job Listings
                </a>
                <a href="#users" class="admin-nav-item">
                    <i class="fas fa-users"></i> Users
                </a>
                <a href="#settings" class="admin-nav-item">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </div>
        </div>

        <div class="admin-content slide-in-right">
            <div id="dashboard" class="admin-panel active">
                <h2><i class="fas fa-tachometer-alt"></i> Dashboard</h2>

                <div class="stats-grid">
                    <div class="stat-card bounce-in">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3>Total Applications</h3>
                            <p class="stat-value">124</p>
                            <p class="stat-change positive">+12% from last month</p>
                        </div>
                    </div>

                    <div class="stat-card bounce-in" style="animation-delay: 0.1s;">
                        <div class="stat-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="stat-info">
                            <h3>Active Jobs</h3>
                            <p class="stat-value">18</p>
                            <p class="stat-change positive">+3 new this week</p>
                        </div>
                    </div>

                    <div class="stat-card bounce-in" style="animation-delay: 0.2s;">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>Registered Users</h3>
                            <p class="stat-value">256</p>
                            <p class="stat-change positive">+5% from last month</p>
                        </div>
                    </div>

                    <div class="stat-card bounce-in" style="animation-delay: 0.3s;">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3>Hired Candidates</h3>
                            <p class="stat-value">42</p>
                            <p class="stat-change neutral">Same as last month</p>
                        </div>
                    </div>
                </div>

                <div class="recent-activity scale-in" style="animation-delay: 0.4s;">
                    <h3><i class="fas fa-history"></i> Recent Activity</h3>
                    <ul class="activity-list">
                        <li class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-file-upload"></i>
                            </div>
                            <div class="activity-details">
                                <p class="activity-text">New CV uploaded by <strong>John Smith</strong></p>
                                <p class="activity-time">2 hours ago</p>
                            </div>
                        </li>
                        <li class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <div class="activity-details">
                                <p class="activity-text">New job listing created: <strong>Senior Python Developer</strong></p>
                                <p class="activity-time">Yesterday</p>
                            </div>
                        </li>
                        <li class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="activity-details">
                                <p class="activity-text">New user registered: <strong>Sarah Johnson</strong></p>
                                <p class="activity-time">2 days ago</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <div id="applications" class="admin-panel">
                <h2><i class="fas fa-file-alt"></i> Applications</h2>

                <div class="table-container scale-in">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Contact Info</th>
                                <th>Skills</th>
                                <th>Education</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if uploaded_files %}
                                {% for file in uploaded_files %}
                                <tr>
                                    <td>{{ file.name }}</td>
                                    <td>{{ file.size }} bytes</td>
                                    <td>{{ file.modified }}</td>
                                    <td>N/A</td>
                                    <td>
                                        <button class="action-button view-btn"><i class="fas fa-eye"></i></button>
                                        <button class="action-button edit-btn"><i class="fas fa-edit"></i></button>
                                        <button class="action-button delete-btn"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="5">No applications found</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Admin navigation
        const navItems = document.querySelectorAll('.admin-nav-item');
        const panels = document.querySelectorAll('.admin-panel');

        navItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all items
                navItems.forEach(navItem => navItem.classList.remove('active'));

                // Add active class to clicked item
                this.classList.add('active');

                // Show corresponding panel
                const targetId = this.getAttribute('href').substring(1);
                panels.forEach(panel => {
                    if (panel.id === targetId) {
                        panel.classList.add('active');
                    } else {
                        panel.classList.remove('active');
                    }
                });
            });
        });

        // Action buttons
        const viewButtons = document.querySelectorAll('.view-btn');
        const editButtons = document.querySelectorAll('.edit-btn');
        const deleteButtons = document.querySelectorAll('.delete-btn');

        viewButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                alert('Viewing application details...');
            });
        });

        editButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                alert('Editing application...');
            });
        });

        deleteButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                if (confirm('Are you sure you want to delete this application?')) {
                    alert('Application deleted!');
                }
            });
        });
    });
</script>
{% endblock %}

{% block extra_head %}
<style>
    .admin-section {
        display: grid;
        grid-template-columns: 250px 1fr;
        gap: 30px;
    }

    .admin-sidebar {
        background-color: var(--secondary-color);
        border-radius: var(--border-radius);
        padding: 20px;
        height: fit-content;
    }

    .admin-nav {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .admin-nav-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 15px;
        color: white;
        text-decoration: none;
        border-radius: var(--border-radius);
        transition: var(--transition);
    }

    .admin-nav-item:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .admin-nav-item.active {
        background-color: var(--primary-color);
        font-weight: 600;
    }

    .admin-nav-item i {
        width: 20px;
        text-align: center;
    }

    .admin-content {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .admin-panel {
        display: none;
    }

    .admin-panel.active {
        display: block;
    }

    .admin-panel h2 {
        color: var(--secondary-color);
        margin-bottom: 30px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .admin-panel h2 i {
        color: var(--primary-color);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
        display: flex;
        align-items: center;
        gap: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: var(--transition);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--box-shadow);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 1.5rem;
    }

    .stat-info {
        flex-grow: 1;
    }

    .stat-info h3 {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 5px;
    }

    .stat-value {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--secondary-color);
        margin-bottom: 5px;
    }

    .stat-change {
        font-size: 0.8rem;
        font-weight: 500;
    }

    .positive {
        color: var(--success-color);
    }

    .negative {
        color: var(--error-color);
    }

    .neutral {
        color: var(--warning-color);
    }

    .recent-activity {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
    }

    .recent-activity h3 {
        margin-bottom: 20px;
        color: var(--secondary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .recent-activity h3 i {
        color: var(--primary-color);
    }

    .activity-list {
        list-style: none;
        padding: 0;
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px 0;
        border-bottom: 1px solid #eee;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        background-color: rgba(52, 152, 219, 0.1);
        color: var(--primary-color);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .activity-details {
        flex-grow: 1;
    }

    .activity-text {
        margin-bottom: 5px;
    }

    .activity-time {
        font-size: 0.8rem;
        color: #999;
    }

    .action-button {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: none;
        background-color: #f8f9fa;
        color: var(--secondary-color);
        cursor: pointer;
        transition: var(--transition);
        margin-right: 5px;
    }

    .view-btn:hover {
        background-color: var(--primary-color);
        color: white;
    }

    .edit-btn:hover {
        background-color: var(--warning-color);
        color: white;
    }

    .delete-btn:hover {
        background-color: var(--error-color);
        color: white;
    }

    @media (max-width: 768px) {
        .admin-section {
            grid-template-columns: 1fr;
        }

        .admin-sidebar {
            margin-bottom: 20px;
        }

        .admin-nav {
            flex-direction: row;
            flex-wrap: wrap;
        }

        .admin-nav-item {
            flex: 1;
            min-width: 120px;
            text-align: center;
            justify-content: center;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}
