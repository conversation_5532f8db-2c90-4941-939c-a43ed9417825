#!/usr/bin/env python3
"""
Test script for CV parser functionality.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_cv_parser():
    """Test the CV parser with sample text."""
    try:
        from cv_parser import extract_contact_info, extract_name, extract_skills, extract_education
        
        # Sample CV text
        sample_cv = """
        <PERSON>
        <EMAIL>
        (555) 123-4567
        linkedin.com/in/johnsmith
        
        PROFESSIONAL SUMMARY
        Experienced software engineer with 5+ years in Python, JavaScript, and React development.
        
        EDUCATION
        Bachelor of Science in Computer Science
        University of Technology, 2015-2019
        
        EXPERIENCE
        Senior Software Engineer
        Tech Solutions Inc., 2019-Present
        - Developed web applications using React and Node.js
        - Implemented RESTful APIs using Python and Flask
        - Led a team of 3 junior developers
        
        SKILLS
        Python, JavaScript, React, Node.js, Flask, Django, SQL, MongoDB, AWS, Docker
        """
        
        print("Testing CV parser...")
        
        # Test contact info extraction
        contact_info = extract_contact_info(sample_cv)
        print(f"Contact Info: {contact_info}")
        
        # Test name extraction
        name = extract_name(sample_cv)
        print(f"Name: {name}")
        
        # Test skills extraction
        skills = extract_skills(sample_cv)
        print(f"Skills: {[skill['name'] for skill in skills]}")
        
        # Test education extraction
        education = extract_education(sample_cv)
        print(f"Education: {education}")
        
        print("CV parser test completed successfully!")
        return True
        
    except Exception as e:
        print(f"Error testing CV parser: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_document_parsing():
    """Test document parsing functionality."""
    try:
        from cv_parser import extract_text_from_file
        
        print("Testing document parsing...")
        
        # Test with a non-existent file (should handle gracefully)
        text = extract_text_from_file("non_existent_file.pdf")
        print(f"Non-existent file result: '{text}'")
        
        print("Document parsing test completed!")
        return True
        
    except Exception as e:
        print(f"Error testing document parsing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Starting CV parser tests...")
    
    success1 = test_cv_parser()
    success2 = test_document_parsing()
    
    if success1 and success2:
        print("\nAll tests passed!")
        sys.exit(0)
    else:
        print("\nSome tests failed!")
        sys.exit(1)
