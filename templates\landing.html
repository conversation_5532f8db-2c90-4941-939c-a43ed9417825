{% extends "base.html" %}

{% block title %}Home | Applicant Tracking System{% endblock %}

{% block description %}Automated CV Analysis for Efficient Hiring - Streamline your recruitment process with our advanced AI-powered system.{% endblock %}

{% block content %}
    <div class="hero-section bounce-in">
        <h2 class="pulse">Optimize Your Resume for Job Success</h2>
        <p class="slide-in-up">Our Resume Scanner helps you tailor your resume to specific job descriptions, increasing your chances of getting past Applicant Tracking Systems (ATS) and landing interviews.</p>

        <div class="features-grid">
            <div class="feature-card scale-in">
                <i class="fas fa-search feature-icon"></i>
                <h3>Resume Scanner</h3>
                <p>Get your resume analyzed against job descriptions to see how well you match the requirements.</p>
            </div>

            <div class="feature-card scale-in" style="animation-delay: 0.2s;">
                <i class="fas fa-percentage feature-icon"></i>
                <h3>Match Score</h3>
                <p>Receive a match percentage score to understand how well your resume fits the job.</p>
            </div>

            <div class="feature-card scale-in" style="animation-delay: 0.4s;">
                <i class="fas fa-lightbulb feature-icon"></i>
                <h3>Optimization Tips</h3>
                <p>Get personalized suggestions to improve your resume and increase your match score.</p>
            </div>
        </div>

        <div class="cta-buttons">
            <button type="button" class="cta-button primary pulse" onclick="window.location.href='{{ url_for('resume_scanner') }}'">
                <i class="fas fa-search"></i> Scan Your Resume
            </button>
            <button type="button" class="cta-button secondary" onclick="window.location.href='{{ url_for('job_listings') }}'">
                <i class="fas fa-briefcase"></i> Browse Jobs
            </button>
        </div>
    </div>

    <div class="how-it-works-section slide-in-up" style="animation-delay: 0.3s;">
        <h2><i class="fas fa-cogs"></i> How It Works</h2>
        <div class="steps-container">
            <div class="step-card">
                <div class="step-number">1</div>
                <div class="step-icon"><i class="fas fa-upload"></i></div>
                <h3>Upload Resume</h3>
                <p>Upload your resume in PDF or DOCX format.</p>
            </div>
            <div class="step-card">
                <div class="step-number">2</div>
                <div class="step-icon"><i class="fas fa-paste"></i></div>
                <h3>Paste Job Description</h3>
                <p>Copy and paste the job description you're interested in.</p>
            </div>
            <div class="step-card">
                <div class="step-number">3</div>
                <div class="step-icon"><i class="fas fa-chart-pie"></i></div>
                <h3>Get Analysis</h3>
                <p>Receive a detailed analysis with match score and suggestions.</p>
            </div>
            <div class="step-card">
                <div class="step-number">4</div>
                <div class="step-icon"><i class="fas fa-edit"></i></div>
                <h3>Optimize Resume</h3>
                <p>Update your resume based on the suggestions provided.</p>
            </div>
        </div>
    </div>

    <div class="testimonials-section slide-in-up" style="animation-delay: 0.5s;">
        <h2><i class="fas fa-quote-left"></i> What Our Users Say</h2>
        <div class="testimonials-grid">
            <div class="testimonial-card">
                <div class="testimonial-content">
                    <p>"I was applying to jobs for months with no response. After using this tool to optimize my resume, I got three interview calls in just one week!"</p>
                </div>
                <div class="testimonial-author">
                    <p><strong>Sarah Johnson</strong><br>Software Developer</p>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="testimonial-content">
                    <p>"The resume scanner showed me exactly what keywords I was missing. I updated my resume and finally got past the ATS systems that were filtering me out."</p>
                </div>
                <div class="testimonial-author">
                    <p><strong>Michael Chen</strong><br>Marketing Specialist</p>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="testimonial-content">
                    <p>"This tool is a game-changer! As an HR Personnel, I can now focus on the most qualified candidates and spend less time on unqualified applicants."</p>
                </div>
                <div class="testimonial-author">
                    <p><strong>Emily Rodriguez</strong><br>Project Manager</p>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Add some extra animations for the landing page
    document.addEventListener('DOMContentLoaded', function() {
        // Animate feature cards on scroll
        const featureCards = document.querySelectorAll('.feature-card');
        const stepCards = document.querySelectorAll('.step-card');
        const testimonialCards = document.querySelectorAll('.testimonial-card');

        function checkScroll() {
            // Animate feature cards
            featureCards.forEach(card => {
                const cardPosition = card.getBoundingClientRect().top;
                const screenPosition = window.innerHeight / 1.3;

                if (cardPosition < screenPosition) {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }
            });

            // Animate step cards
            stepCards.forEach(card => {
                const cardPosition = card.getBoundingClientRect().top;
                const screenPosition = window.innerHeight / 1.3;

                if (cardPosition < screenPosition) {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }
            });

            // Animate testimonial cards
            testimonialCards.forEach(card => {
                const cardPosition = card.getBoundingClientRect().top;
                const screenPosition = window.innerHeight / 1.3;

                if (cardPosition < screenPosition) {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }
            });
        }

        // Initial check
        checkScroll();

        // Check on scroll
        window.addEventListener('scroll', checkScroll);
    });
</script>
{% endblock %}

{% block extra_head %}
<style>
    .hero-section {
        text-align: center;
        padding: 30px 0;
    }

    .hero-section h2 {
        font-size: 2.5rem;
        margin-bottom: 20px;
        color: var(--secondary-color);
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
        margin: 40px 0;
    }

    .feature-card {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
        text-align: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }

    .feature-icon {
        font-size: 3rem;
        color: var(--primary-color);
        margin-bottom: 20px;
    }

    .cta-button {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        padding: 15px 30px;
        font-size: 1.2rem;
        border-radius: var(--border-radius);
        cursor: pointer;
        margin-top: 30px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .cta-button:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }

    .cta-buttons {
        display: flex;
        gap: 20px;
        margin-top: 30px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .cta-button.primary {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    }

    .cta-button.secondary {
        background: var(--light-color);
        color: var(--secondary-color);
    }

    .cta-button.secondary:hover {
        background: var(--secondary-color);
        color: white;
    }

    .how-it-works-section {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        margin-top: 40px;
        box-shadow: var(--box-shadow);
    }

    .how-it-works-section h2 {
        color: var(--secondary-color);
        margin-bottom: 30px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .how-it-works-section h2 i {
        color: var(--primary-color);
    }

    .steps-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 20px;
    }

    .step-card {
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 20px;
        text-align: center;
        position: relative;
        transition: transform 0.3s ease, opacity 0.3s ease;
        opacity: 0;
        transform: translateY(20px);
    }

    .step-number {
        position: absolute;
        top: -15px;
        left: -15px;
        width: 40px;
        height: 40px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        font-size: 1.2rem;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .step-icon {
        width: 70px;
        height: 70px;
        background-color: rgba(52, 152, 219, 0.1);
        color: var(--primary-color);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 2rem;
        margin: 0 auto 15px auto;
        transition: transform 0.3s ease;
    }

    .step-card:hover .step-icon {
        transform: scale(1.1);
        background-color: rgba(52, 152, 219, 0.2);
    }

    .step-card h3 {
        color: var(--secondary-color);
        margin-bottom: 10px;
    }

    .testimonials-section {
        margin-top: 60px;
        text-align: center;
    }

    .testimonials-section h2 {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .testimonials-section h2 i {
        color: var(--primary-color);
    }

    .testimonials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-top: 30px;
    }

    .testimonial-card {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
        position: relative;
        opacity: 0;
        transform: translateY(20px);
        transition: transform 0.3s ease, opacity 0.3s ease;
    }

    .testimonial-card::before {
        content: '"';
        position: absolute;
        top: 10px;
        left: 10px;
        font-size: 5rem;
        color: rgba(52, 152, 219, 0.1);
        font-family: Georgia, serif;
    }

    .testimonial-content {
        position: relative;
        z-index: 1;
    }

    .testimonial-author {
        margin-top: 20px;
        text-align: right;
    }

    @media (max-width: 768px) {
        .features-grid, .testimonials-grid {
            grid-template-columns: 1fr;
        }

        .cta-buttons {
            flex-direction: column;
        }

        .steps-container {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}
