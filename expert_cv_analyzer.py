#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Expert CV Analyzer System

An advanced rule-based expert system for comprehensive CV information extraction.
Uses pattern matching, NLP, and domain knowledge to extract detailed information.

Author: AI Assistant
Version: 2.0.0
"""

import re
import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import spacy
from collections import defaultdict

# Configure logging
logger = logging.getLogger(__name__)

class ExpertCVAnalyzer:
    """Expert system for comprehensive CV analysis and information extraction."""
    
    def __init__(self):
        """Initialize the expert CV analyzer."""
        self.setup_patterns()
        self.setup_knowledge_base()
        
        # Try to load spaCy model
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            logger.warning("spaCy model not found. Using basic pattern matching only.")
            self.nlp = None
    
    def setup_patterns(self):
        """Setup regex patterns for information extraction."""
        
        # Personal information patterns
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_pattern = re.compile(r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})|(?:\+?[1-9]\d{0,3}[-.\s]?)?\(?([0-9]{1,4})\)?[-.\s]?([0-9]{1,4})[-.\s]?([0-9]{1,9})')
        self.linkedin_pattern = re.compile(r'(?:https?://)?(?:www\.)?linkedin\.com/in/[\w-]+/?')
        self.github_pattern = re.compile(r'(?:https?://)?(?:www\.)?github\.com/[\w-]+/?')
        self.website_pattern = re.compile(r'(?:https?://)?(?:www\.)?[\w-]+\.[\w.]+/?[\w-]*')
        
        # Education patterns
        self.degree_patterns = [
            re.compile(r'\b(?:Bachelor|B\.?A\.?|B\.?S\.?|B\.?Sc\.?|B\.?E\.?|B\.?Tech\.?|B\.?Com\.?|B\.?B\.?A\.?)\b', re.IGNORECASE),
            re.compile(r'\b(?:Master|M\.?A\.?|M\.?S\.?|M\.?Sc\.?|M\.?E\.?|M\.?Tech\.?|M\.?Com\.?|M\.?B\.?A\.?|MBA)\b', re.IGNORECASE),
            re.compile(r'\b(?:Doctor|Ph\.?D\.?|D\.?Phil\.?|Ed\.?D\.?|M\.?D\.?|J\.?D\.?)\b', re.IGNORECASE),
            re.compile(r'\b(?:Diploma|Certificate|Associate|A\.?A\.?|A\.?S\.?)\b', re.IGNORECASE)
        ]
        
        self.field_patterns = [
            re.compile(r'\bin\s+([A-Z][a-z\s&]+(?:Engineering|Science|Studies|Arts|Administration|Management|Technology|Design|Medicine|Law))', re.IGNORECASE),
            re.compile(r'\bof\s+([A-Z][a-z\s&]+(?:Engineering|Science|Studies|Arts|Administration|Management|Technology|Design|Medicine|Law))', re.IGNORECASE),
            re.compile(r'\b(Computer Science|Information Technology|Software Engineering|Electrical Engineering|Mechanical Engineering|Civil Engineering|Business Administration|Data Science|Artificial Intelligence|Machine Learning|Cybersecurity|Digital Marketing|Graphic Design|Web Development)\b', re.IGNORECASE)
        ]
        
        self.year_pattern = re.compile(r'\b(19|20)\d{2}\b')
        self.gpa_pattern = re.compile(r'\b(?:GPA|CGPA|Grade)[\s:]*([0-9]+\.?[0-9]*)\s*(?:/\s*([0-9]+\.?[0-9]*))?\b', re.IGNORECASE)
        self.grade_pattern = re.compile(r'\b(First Class|Second Class|Third Class|Distinction|Merit|Pass|Honors?|Magna Cum Laude|Summa Cum Laude|Cum Laude|Dean\'s List)\b', re.IGNORECASE)
        
        # Work experience patterns
        self.job_title_indicators = [
            'Engineer', 'Developer', 'Manager', 'Analyst', 'Consultant', 'Specialist', 'Coordinator',
            'Director', 'Lead', 'Senior', 'Junior', 'Associate', 'Principal', 'Chief', 'Head',
            'Architect', 'Designer', 'Programmer', 'Administrator', 'Officer', 'Executive'
        ]
        
        self.company_indicators = [
            'Inc', 'LLC', 'Corp', 'Corporation', 'Company', 'Ltd', 'Limited', 'Technologies',
            'Solutions', 'Systems', 'Services', 'Consulting', 'Group', 'International'
        ]
        
        # Skills patterns
        self.technical_skills = {
            'programming_languages': [
                'Python', 'Java', 'JavaScript', 'C++', 'C#', 'C', 'PHP', 'Ruby', 'Go', 'Rust',
                'Swift', 'Kotlin', 'Scala', 'R', 'MATLAB', 'Perl', 'Shell', 'Bash', 'PowerShell'
            ],
            'frameworks': [
                'React', 'Angular', 'Vue', 'Django', 'Flask', 'Spring', 'Express', 'Node.js',
                'Laravel', 'Rails', 'ASP.NET', 'Bootstrap', 'jQuery', 'Ember', 'Backbone'
            ],
            'databases': [
                'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Oracle', 'SQLite', 'SQL Server',
                'Cassandra', 'DynamoDB', 'Neo4j', 'InfluxDB', 'Elasticsearch'
            ],
            'tools': [
                'Git', 'Docker', 'Kubernetes', 'Jenkins', 'JIRA', 'Confluence', 'Slack',
                'Visual Studio', 'IntelliJ', 'Eclipse', 'Postman', 'Swagger', 'Terraform'
            ],
            'cloud_platforms': [
                'AWS', 'Azure', 'Google Cloud', 'GCP', 'Heroku', 'DigitalOcean', 'Linode'
            ],
            'methodologies': [
                'Agile', 'Scrum', 'Kanban', 'DevOps', 'CI/CD', 'TDD', 'BDD', 'Waterfall'
            ]
        }
        
        self.soft_skills = [
            'Leadership', 'Communication', 'Teamwork', 'Problem Solving', 'Critical Thinking',
            'Time Management', 'Project Management', 'Analytical Skills', 'Creativity',
            'Adaptability', 'Collaboration', 'Presentation', 'Negotiation', 'Mentoring'
        ]
    
    def setup_knowledge_base(self):
        """Setup domain knowledge for better extraction."""
        
        self.education_keywords = [
            'education', 'academic', 'qualification', 'degree', 'university', 'college',
            'school', 'institute', 'graduated', 'studied', 'major', 'minor', 'thesis'
        ]
        
        self.experience_keywords = [
            'experience', 'employment', 'work', 'career', 'position', 'role', 'job',
            'worked', 'employed', 'served', 'responsible', 'duties', 'achievements'
        ]
        
        self.project_keywords = [
            'project', 'developed', 'built', 'created', 'designed', 'implemented',
            'portfolio', 'github', 'demo', 'application', 'system', 'website'
        ]
        
        self.certification_keywords = [
            'certification', 'certified', 'license', 'credential', 'certificate',
            'qualified', 'accredited', 'professional', 'training', 'course'
        ]
    
    def extract_comprehensive_info(self, cv_text: str) -> Dict[str, Any]:
        """
        Extract comprehensive information from CV text using expert system.
        
        Args:
            cv_text: The CV text to analyze
            
        Returns:
            Dict containing all extracted information
        """
        logger.info("Starting expert CV analysis")
        
        # Preprocess text
        processed_text = self.preprocess_text(cv_text)
        
        # Extract information using expert rules
        cv_info = {
            'personal_info': self.extract_personal_info(processed_text),
            'educational_background': self.extract_education(processed_text),
            'work_experience': self.extract_work_experience(processed_text),
            'technical_skills': self.extract_technical_skills(processed_text),
            'soft_skills': self.extract_soft_skills(processed_text),
            'certifications': self.extract_certifications(processed_text),
            'projects': self.extract_projects(processed_text),
            'languages': self.extract_languages(processed_text),
            'awards_and_honors': self.extract_awards(processed_text),
            'publications': self.extract_publications(processed_text),
            'volunteer_experience': self.extract_volunteer_experience(processed_text),
            'summary': self.extract_summary(processed_text),
            'total_experience': self.calculate_total_experience(processed_text)
        }
        
        logger.info("Expert CV analysis completed")
        return cv_info
    
    def preprocess_text(self, text: str) -> str:
        """Preprocess the CV text for better extraction."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Normalize line breaks
        text = re.sub(r'\n+', '\n', text)
        
        # Remove special characters that might interfere
        text = re.sub(r'[^\w\s@.+\-/(),:;]', ' ', text)
        
        return text.strip()
    
    def extract_personal_info(self, text: str) -> Dict[str, Optional[str]]:
        """Extract personal information using expert patterns."""
        personal_info = {
            'name': None,
            'email': None,
            'phone': None,
            'address': None,
            'linkedin': None,
            'website': None,
            'nationality': None,
            'date_of_birth': None
        }
        
        # Extract email
        email_match = self.email_pattern.search(text)
        if email_match:
            personal_info['email'] = email_match.group(0)
        
        # Extract phone
        phone_match = self.phone_pattern.search(text)
        if phone_match:
            personal_info['phone'] = phone_match.group(0)
        
        # Extract LinkedIn
        linkedin_match = self.linkedin_pattern.search(text)
        if linkedin_match:
            personal_info['linkedin'] = linkedin_match.group(0)
        
        # Extract name (first few words before email or at the beginning)
        lines = text.split('\n')
        for line in lines[:5]:  # Check first 5 lines
            line = line.strip()
            if line and len(line.split()) <= 4 and not any(char.isdigit() for char in line):
                if not any(keyword in line.lower() for keyword in ['email', 'phone', 'address', 'linkedin']):
                    personal_info['name'] = line
                    break
        
        return personal_info

    def extract_education(self, text: str) -> List[Dict[str, Any]]:
        """Extract educational background using expert patterns."""
        education_list = []

        # Split text into sections
        sections = self.split_into_sections(text)
        education_section = self.find_education_section(sections)

        if not education_section:
            education_section = text  # Fallback to full text

        # Extract degree information
        degree_blocks = self.extract_degree_blocks(education_section)

        for block in degree_blocks:
            education_entry = {
                'degree': None,
                'degree_abbreviation': None,
                'field': None,
                'specialization': None,
                'institution': None,
                'location': None,
                'start_year': None,
                'graduation_year': None,
                'duration': None,
                'gpa': None,
                'grade': None,
                'honors': None,
                'thesis_title': None,
                'relevant_coursework': []
            }

            # Extract degree
            for pattern in self.degree_patterns:
                match = pattern.search(block)
                if match:
                    degree = match.group(0)
                    education_entry['degree'] = degree

                    # Extract abbreviation
                    if '.' in degree or len(degree) <= 6:
                        education_entry['degree_abbreviation'] = degree
                    break

            # Extract field of study
            for pattern in self.field_patterns:
                match = pattern.search(block)
                if match:
                    education_entry['field'] = match.group(1).strip()
                    break

            # Extract institution
            institution = self.extract_institution(block)
            if institution:
                education_entry['institution'] = institution

            # Extract years
            years = self.year_pattern.findall(block)
            if years:
                years = sorted([int(y) for y in years])
                if len(years) >= 2:
                    education_entry['start_year'] = str(years[0])
                    education_entry['graduation_year'] = str(years[-1])
                elif len(years) == 1:
                    education_entry['graduation_year'] = str(years[0])

            # Extract GPA
            gpa_match = self.gpa_pattern.search(block)
            if gpa_match:
                gpa = gpa_match.group(1)
                if gpa_match.group(2):
                    gpa += f"/{gpa_match.group(2)}"
                education_entry['gpa'] = gpa

            # Extract grade/honors
            grade_match = self.grade_pattern.search(block)
            if grade_match:
                education_entry['grade'] = grade_match.group(1)

            # Only add if we found meaningful information
            if education_entry['degree'] or education_entry['institution']:
                education_list.append(education_entry)

        return education_list

    def extract_work_experience(self, text: str) -> List[Dict[str, Any]]:
        """Extract work experience using expert patterns."""
        experience_list = []

        # Find experience section
        sections = self.split_into_sections(text)
        experience_section = self.find_experience_section(sections)

        if not experience_section:
            experience_section = text

        # Extract job blocks
        job_blocks = self.extract_job_blocks(experience_section)

        for block in job_blocks:
            experience_entry = {
                'job_title': None,
                'company': None,
                'company_type': None,
                'location': None,
                'employment_type': None,
                'start_date': None,
                'end_date': None,
                'duration': None,
                'responsibilities': [],
                'achievements': [],
                'technologies_used': [],
                'team_size': None,
                'reporting_to': None
            }

            # Extract job title and company
            title_company = self.extract_job_title_company(block)
            if title_company:
                experience_entry.update(title_company)

            # Extract dates
            dates = self.extract_employment_dates(block)
            if dates:
                experience_entry.update(dates)

            # Extract responsibilities and achievements
            responsibilities = self.extract_responsibilities(block)
            experience_entry['responsibilities'] = responsibilities

            # Extract technologies
            technologies = self.extract_technologies_from_text(block)
            experience_entry['technologies_used'] = technologies

            # Only add if we found meaningful information
            if experience_entry['job_title'] or experience_entry['company']:
                experience_list.append(experience_entry)

        return experience_list

    def extract_technical_skills(self, text: str) -> Dict[str, List[str]]:
        """Extract technical skills categorized by type."""
        skills = {
            'programming_languages': [],
            'frameworks': [],
            'databases': [],
            'tools': [],
            'cloud_platforms': [],
            'operating_systems': [],
            'methodologies': [],
            'other_technical': []
        }

        text_lower = text.lower()

        # Extract each category
        for category, skill_list in self.technical_skills.items():
            for skill in skill_list:
                if skill.lower() in text_lower:
                    skills[category].append(skill)

        # Remove duplicates
        for category in skills:
            skills[category] = list(set(skills[category]))

        return skills

    def extract_soft_skills(self, text: str) -> List[str]:
        """Extract soft skills from text."""
        found_skills = []
        text_lower = text.lower()

        for skill in self.soft_skills:
            if skill.lower() in text_lower:
                found_skills.append(skill)

        return list(set(found_skills))

    def extract_certifications(self, text: str) -> List[Dict[str, Any]]:
        """Extract certifications from text."""
        certifications = []

        # Find certification section
        sections = self.split_into_sections(text)
        cert_section = self.find_certification_section(sections)

        if not cert_section:
            cert_section = text

        # Look for certification patterns
        cert_patterns = [
            re.compile(r'(AWS|Microsoft|Google|Oracle|Cisco|CompTIA|PMP|Scrum Master|CISSP|CISA|CISM)\s+([A-Za-z\s]+)', re.IGNORECASE),
            re.compile(r'Certified\s+([A-Za-z\s]+)', re.IGNORECASE)
        ]

        for pattern in cert_patterns:
            matches = pattern.finditer(cert_section)
            for match in matches:
                cert = {
                    'name': match.group(0).strip(),
                    'issuer': match.group(1) if len(match.groups()) > 1 else None,
                    'date_obtained': None,
                    'expiry_date': None,
                    'credential_id': None,
                    'verification_url': None
                }
                certifications.append(cert)

        return certifications

    def extract_projects(self, text: str) -> List[Dict[str, Any]]:
        """Extract projects from text."""
        projects = []

        # Find project section
        sections = self.split_into_sections(text)
        project_section = self.find_project_section(sections)

        if not project_section:
            project_section = text

        # Extract project blocks
        project_blocks = self.extract_project_blocks(project_section)

        for block in project_blocks:
            project = {
                'name': None,
                'description': None,
                'role': None,
                'technologies': [],
                'start_date': None,
                'end_date': None,
                'duration': None,
                'team_size': None,
                'achievements': [],
                'url': None,
                'github_url': None
            }

            # Extract project name (usually first line or after "Project:")
            lines = block.split('\n')
            for line in lines:
                if line.strip() and not any(tech in line.lower() for tech in ['python', 'java', 'react']):
                    project['name'] = line.strip()
                    break

            # Extract technologies
            technologies = self.extract_technologies_from_text(block)
            project['technologies'] = technologies

            # Extract GitHub URL
            github_match = self.github_pattern.search(block)
            if github_match:
                project['github_url'] = github_match.group(0)

            if project['name'] or project['technologies']:
                projects.append(project)

        return projects

    def extract_languages(self, text: str) -> List[Dict[str, str]]:
        """Extract language skills from text."""
        languages = []

        # Common languages and proficiency levels
        language_names = ['English', 'Spanish', 'French', 'German', 'Chinese', 'Japanese', 'Korean', 'Arabic', 'Hindi', 'Portuguese', 'Russian', 'Italian']
        proficiency_levels = ['Native', 'Fluent', 'Advanced', 'Intermediate', 'Basic', 'Conversational']

        for lang in language_names:
            if lang.lower() in text.lower():
                language_entry = {'language': lang, 'proficiency': 'Not specified', 'certification': None}

                # Look for proficiency level near the language
                lang_context = self.get_context_around_word(text, lang, 20)
                for level in proficiency_levels:
                    if level.lower() in lang_context.lower():
                        language_entry['proficiency'] = level
                        break

                languages.append(language_entry)

        return languages

    def extract_awards(self, text: str) -> List[Dict[str, str]]:
        """Extract awards and honors from text."""
        awards = []

        award_patterns = [
            re.compile(r'(Award|Prize|Recognition|Honor|Achievement|Medal|Scholarship)\s*:?\s*([A-Za-z\s]+)', re.IGNORECASE),
            re.compile(r'(Dean\'s List|Honor Roll|Magna Cum Laude|Summa Cum Laude|Cum Laude)', re.IGNORECASE)
        ]

        for pattern in award_patterns:
            matches = pattern.finditer(text)
            for match in matches:
                award = {
                    'name': match.group(0).strip(),
                    'issuer': None,
                    'date': None,
                    'description': None
                }
                awards.append(award)

        return awards

    def extract_publications(self, text: str) -> List[Dict[str, Any]]:
        """Extract publications from text."""
        publications = []

        # Look for publication patterns
        pub_patterns = [
            re.compile(r'Published\s+([A-Za-z\s]+)', re.IGNORECASE),
            re.compile(r'Paper\s*:?\s*([A-Za-z\s]+)', re.IGNORECASE)
        ]

        for pattern in pub_patterns:
            matches = pattern.finditer(text)
            for match in matches:
                publication = {
                    'title': match.group(1).strip(),
                    'authors': [],
                    'journal': None,
                    'date': None,
                    'url': None
                }
                publications.append(publication)

        return publications

    def extract_volunteer_experience(self, text: str) -> List[Dict[str, str]]:
        """Extract volunteer experience from text."""
        volunteer_exp = []

        volunteer_patterns = [
            re.compile(r'Volunteer\s+([A-Za-z\s]+)', re.IGNORECASE),
            re.compile(r'Community\s+([A-Za-z\s]+)', re.IGNORECASE)
        ]

        for pattern in volunteer_patterns:
            matches = pattern.finditer(text)
            for match in matches:
                volunteer = {
                    'organization': match.group(1).strip(),
                    'role': 'Volunteer',
                    'duration': None,
                    'description': None
                }
                volunteer_exp.append(volunteer)

        return volunteer_exp

    def extract_summary(self, text: str) -> Optional[str]:
        """Extract professional summary or objective."""
        summary_keywords = ['summary', 'objective', 'profile', 'about', 'overview']

        lines = text.split('\n')
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in summary_keywords):
                # Get next few lines as summary
                summary_lines = []
                for j in range(i+1, min(i+5, len(lines))):
                    if lines[j].strip():
                        summary_lines.append(lines[j].strip())
                    else:
                        break

                if summary_lines:
                    return ' '.join(summary_lines)

        return None

    def calculate_total_experience(self, text: str) -> Optional[str]:
        """Calculate total years of experience."""
        # Look for experience mentions
        exp_patterns = [
            re.compile(r'(\d+)\s*\+?\s*years?\s+(?:of\s+)?experience', re.IGNORECASE),
            re.compile(r'(\d+)\s*\+?\s*yrs?\s+(?:of\s+)?experience', re.IGNORECASE)
        ]

        for pattern in exp_patterns:
            match = pattern.search(text)
            if match:
                return f"{match.group(1)} years"

        return None

    # Helper methods for text processing and section detection

    def split_into_sections(self, text: str) -> List[str]:
        """Split text into logical sections."""
        # Split by common section headers
        section_headers = [
            'education', 'experience', 'work', 'employment', 'skills', 'projects',
            'certifications', 'awards', 'publications', 'languages', 'volunteer'
        ]

        sections = []
        current_section = []

        lines = text.split('\n')
        for line in lines:
            line_lower = line.lower().strip()

            # Check if this line is a section header
            is_header = any(header in line_lower for header in section_headers)

            if is_header and current_section:
                sections.append('\n'.join(current_section))
                current_section = [line]
            else:
                current_section.append(line)

        if current_section:
            sections.append('\n'.join(current_section))

        return sections

    def find_education_section(self, sections: List[str]) -> Optional[str]:
        """Find the education section from text sections."""
        for section in sections:
            if any(keyword in section.lower() for keyword in self.education_keywords):
                return section
        return None

    def find_experience_section(self, sections: List[str]) -> Optional[str]:
        """Find the experience section from text sections."""
        for section in sections:
            if any(keyword in section.lower() for keyword in self.experience_keywords):
                return section
        return None

    def find_project_section(self, sections: List[str]) -> Optional[str]:
        """Find the project section from text sections."""
        for section in sections:
            if any(keyword in section.lower() for keyword in self.project_keywords):
                return section
        return None

    def find_certification_section(self, sections: List[str]) -> Optional[str]:
        """Find the certification section from text sections."""
        for section in sections:
            if any(keyword in section.lower() for keyword in self.certification_keywords):
                return section
        return None

    def extract_degree_blocks(self, text: str) -> List[str]:
        """Extract blocks of text that contain degree information."""
        blocks = []
        lines = text.split('\n')
        current_block = []

        for line in lines:
            line = line.strip()
            if not line:
                if current_block:
                    blocks.append('\n'.join(current_block))
                    current_block = []
            else:
                current_block.append(line)

        if current_block:
            blocks.append('\n'.join(current_block))

        # Filter blocks that likely contain degree information
        degree_blocks = []
        for block in blocks:
            if any(pattern.search(block) for pattern in self.degree_patterns):
                degree_blocks.append(block)

        return degree_blocks

    def extract_job_blocks(self, text: str) -> List[str]:
        """Extract blocks of text that contain job information."""
        blocks = []
        lines = text.split('\n')
        current_block = []

        for line in lines:
            line = line.strip()
            if not line:
                if current_block:
                    blocks.append('\n'.join(current_block))
                    current_block = []
            else:
                current_block.append(line)

        if current_block:
            blocks.append('\n'.join(current_block))

        # Filter blocks that likely contain job information
        job_blocks = []
        for block in blocks:
            if any(indicator in block for indicator in self.job_title_indicators):
                job_blocks.append(block)

        return job_blocks

    def extract_project_blocks(self, text: str) -> List[str]:
        """Extract blocks of text that contain project information."""
        blocks = []
        lines = text.split('\n')
        current_block = []

        for line in lines:
            line = line.strip()
            if not line:
                if current_block:
                    blocks.append('\n'.join(current_block))
                    current_block = []
            else:
                current_block.append(line)

        if current_block:
            blocks.append('\n'.join(current_block))

        return blocks

    def extract_institution(self, text: str) -> Optional[str]:
        """Extract institution name from education block."""
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            # Look for lines that might contain institution names
            if any(word in line.lower() for word in ['university', 'college', 'institute', 'school']):
                return line
        return None

    def extract_job_title_company(self, text: str) -> Dict[str, Optional[str]]:
        """Extract job title and company from work experience block."""
        result = {'job_title': None, 'company': None}

        lines = text.split('\n')
        for line in lines:
            line = line.strip()

            # Look for job title indicators
            if any(indicator in line for indicator in self.job_title_indicators):
                result['job_title'] = line
                break

        # Look for company name
        for line in lines:
            line = line.strip()
            if any(indicator in line for indicator in self.company_indicators):
                result['company'] = line
                break

        return result

    def extract_employment_dates(self, text: str) -> Dict[str, Optional[str]]:
        """Extract employment dates from work experience block."""
        dates = {'start_date': None, 'end_date': None, 'duration': None}

        # Look for date patterns
        date_patterns = [
            re.compile(r'(\w+\s+\d{4})\s*[-–]\s*(\w+\s+\d{4}|Present)', re.IGNORECASE),
            re.compile(r'(\d{4})\s*[-–]\s*(\d{4}|Present)', re.IGNORECASE)
        ]

        for pattern in date_patterns:
            match = pattern.search(text)
            if match:
                dates['start_date'] = match.group(1)
                dates['end_date'] = match.group(2)
                break

        return dates

    def extract_responsibilities(self, text: str) -> List[str]:
        """Extract responsibilities from work experience block."""
        responsibilities = []

        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            # Look for bullet points or responsibility indicators
            if line.startswith('•') or line.startswith('-') or line.startswith('*'):
                responsibilities.append(line[1:].strip())
            elif any(word in line.lower() for word in ['responsible', 'managed', 'developed', 'led', 'implemented']):
                responsibilities.append(line)

        return responsibilities

    def extract_technologies_from_text(self, text: str) -> List[str]:
        """Extract technologies mentioned in text."""
        technologies = []
        text_lower = text.lower()

        # Check all technical skills
        for category, skills in self.technical_skills.items():
            for skill in skills:
                if skill.lower() in text_lower:
                    technologies.append(skill)

        return list(set(technologies))

    def get_context_around_word(self, text: str, word: str, context_size: int) -> str:
        """Get context around a specific word."""
        words = text.split()
        word_lower = word.lower()

        for i, w in enumerate(words):
            if word_lower in w.lower():
                start = max(0, i - context_size)
                end = min(len(words), i + context_size + 1)
                return ' '.join(words[start:end])

        return ""


# Main function to use the expert system
def extract_cv_information_expert(cv_text: str) -> Dict[str, Any]:
    """
    Main function to extract CV information using the expert system.

    Args:
        cv_text: The CV text to analyze

    Returns:
        Dict containing comprehensive CV information
    """
    analyzer = ExpertCVAnalyzer()
    return analyzer.extract_comprehensive_info(cv_text)
