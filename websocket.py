"""
WebSocket implementation for real-time updates in the Applicant Tracking System.

This module provides WebSocket functionality for real-time updates between
CV uploads, job applications, and the admin panel.
"""

from flask import Flask
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
import json
import logging
from typing import Dict, Any, List

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize SocketIO
socketio = SocketIO()

# Connected clients
connected_clients = {}

def init_app(app: Flask) -> None:
    """
    Initialize the WebSocket with the Flask application.
    
    Args:
        app: Flask application instance
    """
    socketio.init_app(app, cors_allowed_origins="*")
    register_handlers()

def register_handlers() -> None:
    """Register WebSocket event handlers."""
    
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection."""
        logger.info(f"Client connected: {socketio.request.sid}")
        connected_clients[socketio.request.sid] = {
            'rooms': []
        }
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection."""
        logger.info(f"Client disconnected: {socketio.request.sid}")
        if socketio.request.sid in connected_clients:
            del connected_clients[socketio.request.sid]
    
    @socketio.on('join')
    def handle_join(data):
        """
        Handle client joining a room.
        
        Args:
            data: Dictionary containing room information
        """
        room = data.get('room')
        if not room:
            return
        
        join_room(room)
        if socketio.request.sid in connected_clients:
            if 'rooms' not in connected_clients[socketio.request.sid]:
                connected_clients[socketio.request.sid]['rooms'] = []
            connected_clients[socketio.request.sid]['rooms'].append(room)
        
        logger.info(f"Client {socketio.request.sid} joined room: {room}")
        emit('joined', {'room': room}, room=socketio.request.sid)
    
    @socketio.on('leave')
    def handle_leave(data):
        """
        Handle client leaving a room.
        
        Args:
            data: Dictionary containing room information
        """
        room = data.get('room')
        if not room:
            return
        
        leave_room(room)
        if socketio.request.sid in connected_clients:
            if 'rooms' in connected_clients[socketio.request.sid]:
                if room in connected_clients[socketio.request.sid]['rooms']:
                    connected_clients[socketio.request.sid]['rooms'].remove(room)
        
        logger.info(f"Client {socketio.request.sid} left room: {room}")
        emit('left', {'room': room}, room=socketio.request.sid)

def notify_cv_upload(cv_data: Dict[str, Any]) -> None:
    """
    Notify clients about a new CV upload.
    
    Args:
        cv_data: Dictionary containing CV data
    """
    logger.info(f"Notifying about new CV upload: {cv_data.get('id')}")
    
    # Notify admin room
    emit('cv_upload', cv_data, room='admin', namespace='/')
    
    # Notify applicant's personal room
    applicant_id = cv_data.get('applicant_id')
    if applicant_id:
        emit('cv_upload', cv_data, room=f'applicant_{applicant_id}', namespace='/')

def notify_job_application(application_data: Dict[str, Any]) -> None:
    """
    Notify clients about a new job application.
    
    Args:
        application_data: Dictionary containing job application data
    """
    logger.info(f"Notifying about new job application: {application_data.get('id')}")
    
    # Notify admin room
    emit('job_application', application_data, room='admin', namespace='/')
    
    # Notify applicant's personal room
    applicant_id = application_data.get('applicant_id')
    if applicant_id:
        emit('job_application', application_data, room=f'applicant_{applicant_id}', namespace='/')
    
    # Notify job's room
    job_id = application_data.get('job_id')
    if job_id:
        emit('job_application', application_data, room=f'job_{job_id}', namespace='/')

def notify_application_update(application_data: Dict[str, Any]) -> None:
    """
    Notify clients about an update to a job application.
    
    Args:
        application_data: Dictionary containing updated job application data
    """
    logger.info(f"Notifying about job application update: {application_data.get('id')}")
    
    # Notify admin room
    emit('application_update', application_data, room='admin', namespace='/')
    
    # Notify applicant's personal room
    applicant_id = application_data.get('applicant_id')
    if applicant_id:
        emit('application_update', application_data, room=f'applicant_{applicant_id}', namespace='/')
    
    # Notify job's room
    job_id = application_data.get('job_id')
    if job_id:
        emit('application_update', application_data, room=f'job_{job_id}', namespace='/')

def notify_cv_analysis_complete(cv_data: Dict[str, Any]) -> None:
    """
    Notify clients that CV analysis is complete.
    
    Args:
        cv_data: Dictionary containing CV data with analysis results
    """
    logger.info(f"Notifying about CV analysis completion: {cv_data.get('id')}")
    
    # Notify admin room
    emit('cv_analysis_complete', cv_data, room='admin', namespace='/')
    
    # Notify applicant's personal room
    applicant_id = cv_data.get('applicant_id')
    if applicant_id:
        emit('cv_analysis_complete', cv_data, room=f'applicant_{applicant_id}', namespace='/')

def notify_job_match(match_data: Dict[str, Any]) -> None:
    """
    Notify clients about a job match.
    
    Args:
        match_data: Dictionary containing job match data
    """
    logger.info(f"Notifying about job match: {match_data.get('job_id')} - {match_data.get('applicant_id')}")
    
    # Notify admin room
    emit('job_match', match_data, room='admin', namespace='/')
    
    # Notify applicant's personal room
    applicant_id = match_data.get('applicant_id')
    if applicant_id:
        emit('job_match', match_data, room=f'applicant_{applicant_id}', namespace='/')
    
    # Notify job's room
    job_id = match_data.get('job_id')
    if job_id:
        emit('job_match', match_data, room=f'job_{job_id}', namespace='/')
