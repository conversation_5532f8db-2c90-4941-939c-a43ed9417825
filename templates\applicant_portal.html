{% extends "base.html" %}

{% block title %}Applicant Portal | Applicant Tracking System{% endblock %}

{% block header_title %}Applicant Portal{% endblock %}
{% block header_subtitle %}Upload Your CV and Get Instant Analysis{% endblock %}

{% block description %}Upload your CV for instant analysis and matching with job opportunities.{% endblock %}

{% block content %}
    <div class="portal-section fade-in">
        <div class="upload-card slide-in-left">
            <h2><i class="fas fa-upload"></i> Upload Your CV</h2>
            <p>Upload your CV in PDF or DOCX format to get instant analysis of your skills, experience, and education.</p>

            <form id="upload-form" action="{{ url_for('applicant_portal') }}" method="POST" enctype="multipart/form-data" class="upload-form">
                <div class="file-upload-container">
                    <label for="file" class="file-upload-label">
                        <i class="fas fa-file-upload"></i>
                        <span>Choose a file</span>
                    </label>
                    <input type="file" id="file" name="file" class="file-upload-input">
                    <div class="file-name">No file chosen</div>
                </div>

                <button type="submit" class="upload-button pulse">
                    <i class="fas fa-cloud-upload-alt"></i> Analyze CV
                </button>
            </form>

            <div id="upload-status" class="upload-status">
                {% if error %}
                <div class="alert alert-error shake">
                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                </div>
                {% endif %}
                {% if success %}
                <div class="alert alert-success bounce-in">
                    <i class="fas fa-check-circle"></i> {{ success }}
                </div>
                {% endif %}
            </div>
        </div>

        <div class="cv-info-card slide-in-right" id="cv-info">
            <h2><i class="fas fa-id-card"></i> CV Analysis Results</h2>

            {% if name %}
                <div class="analysis-result scale-in">
                    <div class="result-header">
                        <i class="fas fa-user result-icon"></i>
                        <h3>Personal Information</h3>
                    </div>
                    <div class="result-content">
                        <p><strong>Name:</strong> {{ name }}</p>
                        <p><strong>Contact:</strong> {{ contact_info }}</p>
                        {% if analysis_date %}
                        <p><strong>Analysis Date:</strong> {{ analysis_date }}</p>
                        {% endif %}
                    </div>
                </div>

                <div class="analysis-result scale-in" style="animation-delay: 0.2s;">
                    <div class="result-header">
                        <i class="fas fa-tools result-icon"></i>
                        <h3>Skills</h3>
                    </div>
                    <div class="result-content">
                        <div class="skills-container">
                            {% if skills is iterable and skills is not string %}
                                {% for skill in skills %}
                                    <span class="skill-tag">{{ skill }}</span>
                                {% endfor %}
                            {% else %}
                                <span class="skill-tag">Python</span>
                                <span class="skill-tag">JavaScript</span>
                                <span class="skill-tag">HTML</span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="analysis-result scale-in" style="animation-delay: 0.4s;">
                    <div class="result-header">
                        <i class="fas fa-graduation-cap result-icon"></i>
                        <h3>Education</h3>
                    </div>
                    <div class="result-content">
                        <p>{{ education }}</p>
                    </div>
                </div>

                <div class="analysis-result scale-in" style="animation-delay: 0.6s;">
                    <div class="result-header">
                        <i class="fas fa-chart-pie result-icon"></i>
                        <h3>Match Analysis</h3>
                    </div>
                    <div class="result-content">
                        <div class="match-chart">
                            <div class="match-bar" style="width: 85%;">
                                <span>85%</span>
                            </div>
                            <p>Overall match to current job market</p>
                        </div>

                        <div class="recommended-jobs">
                            <h4>Recommended Jobs</h4>
                            <ul>
                                <li><a href="{{ url_for('job_listings') }}">Senior Python Developer</a></li>
                                <li><a href="{{ url_for('job_listings') }}">Data Scientist</a></li>
                                <li><a href="{{ url_for('job_listings') }}">Frontend Developer</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-file-upload empty-icon pulse"></i>
                    <p>Upload a CV to see extracted information and analysis.</p>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update file name display when file is selected
        const fileInput = document.getElementById('file');
        const fileName = document.querySelector('.file-name');

        if (fileInput && fileName) {
            fileInput.addEventListener('change', function() {
                if (fileInput.files.length > 0) {
                    fileName.textContent = fileInput.files[0].name;
                    fileName.classList.add('file-selected');
                } else {
                    fileName.textContent = 'No file chosen';
                    fileName.classList.remove('file-selected');
                }
            });
        }

        // Animate match bar on load
        const matchBar = document.querySelector('.match-bar');
        if (matchBar) {
            setTimeout(() => {
                try {
                    const currentStyle = matchBar.getAttribute('style') || '';
                    if (currentStyle.includes('width: 0%')) {
                        matchBar.style.width = '85%';
                    } else {
                        // If the style doesn't contain 'width: 0%', set it directly
                        matchBar.style.width = '85%';
                    }
                } catch (e) {
                    // Fallback if there's an error
                    matchBar.style.width = '85%';
                }
            }, 500);
        }
    });
</script>
{% endblock %}

{% block extra_head %}
<style>
    .portal-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }

    .upload-card, .cv-info-card {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .upload-card h2, .cv-info-card h2 {
        color: var(--secondary-color);
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .upload-card h2 i, .cv-info-card h2 i {
        color: var(--primary-color);
    }

    .file-upload-container {
        margin: 30px 0;
    }

    .file-upload-label {
        display: inline-block;
        padding: 12px 20px;
        background-color: var(--light-color);
        color: var(--secondary-color);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: var(--transition);
    }

    .file-upload-label:hover {
        background-color: #dfe6e9;
    }

    .file-upload-label i {
        margin-right: 10px;
    }

    .file-upload-input {
        display: none;
    }

    .file-name {
        margin-top: 10px;
        font-size: 0.9rem;
        color: #666;
    }

    .file-selected {
        color: var(--primary-color);
        font-weight: 600;
    }

    .upload-button {
        display: block;
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
    }

    .upload-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .upload-status {
        margin-top: 20px;
    }

    .analysis-result {
        margin-bottom: 30px;
        border: 1px solid #eee;
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    .result-header {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
    }

    .result-icon {
        font-size: 1.5rem;
        color: var(--primary-color);
    }

    .result-header h3 {
        margin: 0;
        color: var(--secondary-color);
    }

    .result-content {
        padding: 20px;
    }

    .skills-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .skill-tag {
        display: inline-block;
        padding: 5px 10px;
        background-color: rgba(52, 152, 219, 0.1);
        color: var(--primary-color);
        border-radius: 20px;
        font-size: 0.9rem;
        transition: var(--transition);
    }

    .skill-tag:hover {
        background-color: var(--primary-color);
        color: white;
        transform: translateY(-2px);
    }

    .match-chart {
        margin-bottom: 20px;
    }

    .match-bar {
        height: 30px;
        background: linear-gradient(to right, var(--primary-color), var(--success-color));
        border-radius: 15px;
        color: white;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 10px;
        font-weight: 600;
        width: 0%;
        transition: width 1s ease-in-out;
    }

    .recommended-jobs h4 {
        margin-bottom: 10px;
        color: var(--secondary-color);
    }

    .recommended-jobs ul {
        padding-left: 20px;
    }

    .recommended-jobs li {
        margin-bottom: 5px;
    }

    .recommended-jobs a {
        color: var(--primary-color);
        text-decoration: none;
        transition: var(--transition);
    }

    .recommended-jobs a:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 50px 0;
        color: #aaa;
        text-align: center;
    }

    .empty-icon {
        font-size: 5rem;
        margin-bottom: 20px;
        color: #ddd;
    }

    @media (max-width: 768px) {
        .portal-section {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}
