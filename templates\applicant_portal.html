{% extends "base.html" %}

{% block title %}Applicant Portal | Applicant Tracking System{% endblock %}

{% block header_title %}Applicant Portal{% endblock %}
{% block header_subtitle %}Upload Your CV and Get Instant Analysis{% endblock %}

{% block description %}Upload your CV for instant analysis and matching with job opportunities.{% endblock %}

{% block content %}
    <div class="portal-section fade-in">
        <div class="upload-card slide-in-left">
            <h2><i class="fas fa-upload"></i> Upload Your CV</h2>
            <p>Upload your CV in PDF or DOCX format to get instant analysis of your skills, experience, and education.</p>

            <form id="upload-form" action="{{ url_for('applicant_portal') }}" method="POST" enctype="multipart/form-data" class="upload-form">
                <div class="file-upload-container">
                    <label for="file" class="file-upload-label">
                        <i class="fas fa-file-upload"></i>
                        <span>Choose a file</span>
                    </label>
                    <input type="file" id="file" name="file" class="file-upload-input">
                    <div class="file-name">No file chosen</div>
                </div>

                <button type="submit" class="upload-button pulse">
                    <i class="fas fa-cloud-upload-alt"></i> Analyze CV
                </button>
            </form>

            <div id="upload-status" class="upload-status">
                {% if error %}
                <div class="alert alert-error shake">
                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                </div>
                {% endif %}
                {% if success %}
                <div class="alert alert-success bounce-in">
                    <i class="fas fa-check-circle"></i> {{ success }}
                </div>
                {% endif %}
            </div>
        </div>

        <div class="cv-info-card slide-in-right" id="cv-info">
            <h2><i class="fas fa-id-card"></i> Comprehensive CV Analysis</h2>

            {% if cv_analysis %}
                <!-- Completeness Score -->
                <div class="completeness-section scale-in">
                    <div class="completeness-header">
                        <h3><i class="fas fa-chart-pie"></i> CV Completeness Score</h3>
                        <div class="score-circle" data-score="{{ cv_analysis.completeness_score }}">
                            <div class="score-text">{{ cv_analysis.completeness_score }}%</div>
                        </div>
                    </div>
                    <div class="completeness-bar">
                        <div class="completeness-fill" style="width: {{ cv_analysis.completeness_score }}%"></div>
                    </div>
                    <p class="analysis-method">Analysis Method: {{ cv_analysis.metadata.analysis_method }}</p>
                </div>

                <!-- Personal Information -->
                {% if cv_analysis.personal_info %}
                <div class="analysis-section scale-in">
                    <div class="section-header">
                        <h3><i class="fas fa-user"></i> Personal Information</h3>
                        <span class="section-status complete">Complete</span>
                    </div>
                    <div class="info-grid">
                        {% if cv_analysis.personal_info.name %}
                        <div class="info-item">
                            <label><i class="fas fa-user"></i> Name:</label>
                            <span>{{ cv_analysis.personal_info.name }}</span>
                        </div>
                        {% endif %}
                        {% if cv_analysis.personal_info.email %}
                        <div class="info-item">
                            <label><i class="fas fa-envelope"></i> Email:</label>
                            <span>{{ cv_analysis.personal_info.email }}</span>
                        </div>
                        {% endif %}
                        {% if cv_analysis.personal_info.phone %}
                        <div class="info-item">
                            <label><i class="fas fa-phone"></i> Phone:</label>
                            <span>{{ cv_analysis.personal_info.phone }}</span>
                        </div>
                        {% endif %}
                        {% if cv_analysis.personal_info.linkedin %}
                        <div class="info-item">
                            <label><i class="fab fa-linkedin"></i> LinkedIn:</label>
                            <span><a href="{{ cv_analysis.personal_info.linkedin }}" target="_blank">{{ cv_analysis.personal_info.linkedin }}</a></span>
                        </div>
                        {% endif %}
                        {% if cv_analysis.personal_info.address %}
                        <div class="info-item">
                            <label><i class="fas fa-map-marker-alt"></i> Address:</label>
                            <span>{{ cv_analysis.personal_info.address }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Educational Background -->
                {% if cv_analysis.educational_background %}
                <div class="analysis-section scale-in">
                    <div class="section-header">
                        <h3><i class="fas fa-graduation-cap"></i> Educational Background</h3>
                        <span class="section-status complete">{{ cv_analysis.educational_background|length }} Qualification(s)</span>
                    </div>
                    {% for edu in cv_analysis.educational_background %}
                    <div class="education-item">
                        <div class="edu-main">
                            <h4>{{ edu.degree or 'Degree' }}{% if edu.degree_abbreviation %} ({{ edu.degree_abbreviation }}){% endif %}</h4>
                            {% if edu.field %}<p class="edu-field">{{ edu.field }}</p>{% endif %}
                            {% if edu.specialization %}<p class="edu-specialization">Specialization: {{ edu.specialization }}</p>{% endif %}
                        </div>
                        <div class="edu-details">
                            {% if edu.institution %}<div class="edu-institution"><i class="fas fa-university"></i> {{ edu.institution }}</div>{% endif %}
                            {% if edu.location %}<div class="edu-location"><i class="fas fa-map-marker-alt"></i> {{ edu.location }}</div>{% endif %}
                            <div class="edu-timeline">
                                {% if edu.start_year %}<span class="start-year">{{ edu.start_year }}</span>{% endif %}
                                {% if edu.start_year and edu.graduation_year %}<span class="separator">-</span>{% endif %}
                                {% if edu.graduation_year %}<span class="grad-year">{{ edu.graduation_year }}</span>{% endif %}
                                {% if edu.duration %}<span class="duration">({{ edu.duration }})</span>{% endif %}
                            </div>
                            {% if edu.gpa or edu.grade %}
                            <div class="edu-performance">
                                {% if edu.gpa %}<span class="gpa">GPA: {{ edu.gpa }}</span>{% endif %}
                                {% if edu.grade %}<span class="grade">{{ edu.grade }}</span>{% endif %}
                            </div>
                            {% endif %}
                            {% if edu.honors %}<div class="edu-honors"><i class="fas fa-award"></i> {{ edu.honors }}</div>{% endif %}
                            {% if edu.thesis_title %}<div class="edu-thesis"><strong>Thesis:</strong> {{ edu.thesis_title }}</div>{% endif %}
                            {% if edu.relevant_coursework %}
                            <div class="edu-coursework">
                                <strong>Relevant Coursework:</strong>
                                <div class="coursework-tags">
                                    {% for course in edu.relevant_coursework %}
                                    <span class="course-tag">{{ course }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Work Experience -->
                {% if cv_analysis.work_experience %}
                <div class="analysis-section scale-in">
                    <div class="section-header">
                        <h3><i class="fas fa-briefcase"></i> Work Experience</h3>
                        <span class="section-status complete">{{ cv_analysis.work_experience|length }} Position(s)</span>
                    </div>
                    {% for exp in cv_analysis.work_experience %}
                    <div class="experience-item">
                        <div class="exp-header">
                            <h4>{{ exp.job_title or 'Position' }}</h4>
                            {% if exp.company %}<p class="exp-company">{{ exp.company }}{% if exp.company_type %} ({{ exp.company_type }}){% endif %}</p>{% endif %}
                        </div>
                        <div class="exp-details">
                            <div class="exp-meta">
                                {% if exp.location %}<span class="exp-location"><i class="fas fa-map-marker-alt"></i> {{ exp.location }}</span>{% endif %}
                                {% if exp.employment_type %}<span class="exp-type"><i class="fas fa-briefcase"></i> {{ exp.employment_type }}</span>{% endif %}
                                {% if exp.duration %}<span class="exp-duration"><i class="fas fa-clock"></i> {{ exp.duration }}</span>{% endif %}
                            </div>
                            {% if exp.start_date or exp.end_date %}
                            <div class="exp-timeline">
                                {% if exp.start_date %}<span class="start-date">{{ exp.start_date }}</span>{% endif %}
                                {% if exp.start_date and exp.end_date %}<span class="separator">-</span>{% endif %}
                                {% if exp.end_date %}<span class="end-date">{{ exp.end_date }}</span>{% endif %}
                            </div>
                            {% endif %}
                            {% if exp.responsibilities %}
                            <div class="exp-responsibilities">
                                <strong>Key Responsibilities:</strong>
                                <ul>
                                    {% for resp in exp.responsibilities[:5] %}
                                    <li>{{ resp }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                            {% if exp.achievements %}
                            <div class="exp-achievements">
                                <strong>Achievements:</strong>
                                <ul>
                                    {% for ach in exp.achievements[:3] %}
                                    <li>{{ ach }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                            {% if exp.technologies_used %}
                            <div class="exp-technologies">
                                <strong>Technologies Used:</strong>
                                <div class="tech-tags">
                                    {% for tech in exp.technologies_used %}
                                    <span class="tech-tag">{{ tech }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Technical Skills -->
                {% if cv_analysis.technical_skills %}
                <div class="analysis-section scale-in">
                    <div class="section-header">
                        <h3><i class="fas fa-code"></i> Technical Skills</h3>
                        <span class="section-status complete">Categorized</span>
                    </div>
                    <div class="skills-grid">
                        {% if cv_analysis.technical_skills.programming_languages %}
                        <div class="skill-category">
                            <h4><i class="fas fa-laptop-code"></i> Programming Languages</h4>
                            <div class="skill-tags">
                                {% for skill in cv_analysis.technical_skills.programming_languages %}
                                <span class="skill-tag programming">{{ skill }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        {% if cv_analysis.technical_skills.frameworks %}
                        <div class="skill-category">
                            <h4><i class="fas fa-layer-group"></i> Frameworks</h4>
                            <div class="skill-tags">
                                {% for skill in cv_analysis.technical_skills.frameworks %}
                                <span class="skill-tag framework">{{ skill }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        {% if cv_analysis.technical_skills.databases %}
                        <div class="skill-category">
                            <h4><i class="fas fa-database"></i> Databases</h4>
                            <div class="skill-tags">
                                {% for skill in cv_analysis.technical_skills.databases %}
                                <span class="skill-tag database">{{ skill }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        {% if cv_analysis.technical_skills.tools %}
                        <div class="skill-category">
                            <h4><i class="fas fa-tools"></i> Tools</h4>
                            <div class="skill-tags">
                                {% for skill in cv_analysis.technical_skills.tools %}
                                <span class="skill-tag tool">{{ skill }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        {% if cv_analysis.technical_skills.cloud_platforms %}
                        <div class="skill-category">
                            <h4><i class="fas fa-cloud"></i> Cloud Platforms</h4>
                            <div class="skill-tags">
                                {% for skill in cv_analysis.technical_skills.cloud_platforms %}
                                <span class="skill-tag cloud">{{ skill }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Soft Skills -->
                {% if cv_analysis.soft_skills %}
                <div class="analysis-section scale-in">
                    <div class="section-header">
                        <h3><i class="fas fa-users"></i> Soft Skills</h3>
                        <span class="section-status complete">{{ cv_analysis.soft_skills|length }} Skills</span>
                    </div>
                    <div class="soft-skills-container">
                        {% for skill in cv_analysis.soft_skills %}
                        <span class="soft-skill-tag">{{ skill }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Projects -->
                {% if cv_analysis.projects %}
                <div class="analysis-section scale-in">
                    <div class="section-header">
                        <h3><i class="fas fa-project-diagram"></i> Projects</h3>
                        <span class="section-status complete">{{ cv_analysis.projects|length }} Project(s)</span>
                    </div>
                    {% for project in cv_analysis.projects %}
                    <div class="project-item">
                        <div class="project-header">
                            <h4>{{ project.name or 'Project' }}</h4>
                            {% if project.role %}<p class="project-role">Role: {{ project.role }}</p>{% endif %}
                        </div>
                        {% if project.description %}
                        <div class="project-description">{{ project.description }}</div>
                        {% endif %}
                        <div class="project-details">
                            {% if project.start_date or project.end_date %}
                            <div class="project-timeline">
                                {% if project.start_date %}<span class="start-date">{{ project.start_date }}</span>{% endif %}
                                {% if project.start_date and project.end_date %}<span class="separator">-</span>{% endif %}
                                {% if project.end_date %}<span class="end-date">{{ project.end_date }}</span>{% endif %}
                                {% if project.duration %}<span class="duration">({{ project.duration }})</span>{% endif %}
                            </div>
                            {% endif %}
                            {% if project.technologies %}
                            <div class="project-tech">
                                <strong>Technologies:</strong>
                                <div class="tech-tags">
                                    {% for tech in project.technologies %}
                                    <span class="tech-tag">{{ tech }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                            {% if project.achievements %}
                            <div class="project-achievements">
                                <strong>Achievements:</strong>
                                <ul>
                                    {% for ach in project.achievements %}
                                    <li>{{ ach }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                            {% if project.url or project.github_url %}
                            <div class="project-links">
                                {% if project.url %}<a href="{{ project.url }}" target="_blank" class="project-link"><i class="fas fa-external-link-alt"></i> Live Demo</a>{% endif %}
                                {% if project.github_url %}<a href="{{ project.github_url }}" target="_blank" class="project-link"><i class="fab fa-github"></i> GitHub</a>{% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Certifications -->
                {% if cv_analysis.certifications %}
                <div class="analysis-section scale-in">
                    <div class="section-header">
                        <h3><i class="fas fa-certificate"></i> Certifications</h3>
                        <span class="section-status complete">{{ cv_analysis.certifications|length }} Certification(s)</span>
                    </div>
                    {% for cert in cv_analysis.certifications %}
                    <div class="certification-item">
                        <div class="cert-header">
                            <h4>{{ cert.name }}</h4>
                            {% if cert.issuer %}<p class="cert-issuer">{{ cert.issuer }}</p>{% endif %}
                        </div>
                        <div class="cert-details">
                            {% if cert.date_obtained %}<span class="cert-date"><i class="fas fa-calendar"></i> {{ cert.date_obtained }}</span>{% endif %}
                            {% if cert.expiry_date %}<span class="cert-expiry"><i class="fas fa-clock"></i> Expires: {{ cert.expiry_date }}</span>{% endif %}
                            {% if cert.credential_id %}<span class="cert-id"><i class="fas fa-id-badge"></i> ID: {{ cert.credential_id }}</span>{% endif %}
                            {% if cert.verification_url %}<a href="{{ cert.verification_url }}" target="_blank" class="cert-verify"><i class="fas fa-check-circle"></i> Verify</a>{% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Recommendations -->
                {% if cv_analysis.recommendations %}
                <div class="analysis-section scale-in recommendations-section">
                    <div class="section-header">
                        <h3><i class="fas fa-lightbulb"></i> Improvement Recommendations</h3>
                        <span class="section-status improvement">{{ cv_analysis.recommendations|length }} Suggestions</span>
                    </div>
                    <div class="recommendations-list">
                        {% for recommendation in cv_analysis.recommendations %}
                        <div class="recommendation-item">
                            <i class="fas fa-arrow-right"></i>
                            <span>{{ recommendation }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Analysis Metadata -->
                {% if cv_analysis.metadata %}
                <div class="analysis-section scale-in metadata-section">
                    <div class="section-header">
                        <h3><i class="fas fa-info-circle"></i> Analysis Details</h3>
                        <span class="section-status info">Metadata</span>
                    </div>
                    <div class="metadata-grid">
                        <div class="metadata-item">
                            <label>File:</label>
                            <span>{{ cv_analysis.metadata.filename }}</span>
                        </div>
                        <div class="metadata-item">
                            <label>Analysis Date:</label>
                            <span>{{ cv_analysis.metadata.analysis_date[:19] }}</span>
                        </div>
                        <div class="metadata-item">
                            <label>Text Length:</label>
                            <span>{{ cv_analysis.metadata.text_length }} characters</span>
                        </div>
                        <div class="metadata-item">
                            <label>Sections Detected:</label>
                            <span>{{ cv_analysis.metadata.sections_detected }}</span>
                        </div>
                    </div>
                </div>
                {% endif %}
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-file-upload empty-icon pulse"></i>
                    <h3>No CV Uploaded</h3>
                    <p>Upload your CV to see comprehensive analysis powered by Expert System and Gemini AI.</p>
                    <div class="features-preview">
                        <div class="feature-item">
                            <i class="fas fa-brain"></i>
                            <span>AI-Powered Analysis</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-chart-line"></i>
                            <span>Completeness Scoring</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-lightbulb"></i>
                            <span>Improvement Recommendations</span>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update file name display when file is selected
        const fileInput = document.getElementById('file');
        const fileName = document.querySelector('.file-name');

        if (fileInput && fileName) {
            fileInput.addEventListener('change', function() {
                if (fileInput.files.length > 0) {
                    fileName.textContent = fileInput.files[0].name;
                    fileName.classList.add('file-selected');
                } else {
                    fileName.textContent = 'No file chosen';
                    fileName.classList.remove('file-selected');
                }
            });
        }

        // Animate completeness score circle
        const scoreCircle = document.querySelector('.score-circle');
        if (scoreCircle) {
            const score = scoreCircle.getAttribute('data-score') || 0;
            scoreCircle.style.setProperty('--score', score);

            // Animate the circle
            setTimeout(() => {
                scoreCircle.style.background = `conic-gradient(#2ecc71 0deg, #2ecc71 ${score * 3.6}deg, rgba(255,255,255,0.3) ${score * 3.6}deg)`;
            }, 500);
        }

        // Animate completeness bar
        const completenessBar = document.querySelector('.completeness-fill');
        if (completenessBar) {
            setTimeout(() => {
                const width = completenessBar.style.width;
                completenessBar.style.width = '0%';
                setTimeout(() => {
                    completenessBar.style.width = width;
                }, 100);
            }, 800);
        }

        // Animate sections with stagger effect
        const sections = document.querySelectorAll('.analysis-section');
        sections.forEach((section, index) => {
            section.style.animationDelay = `${index * 0.1}s`;
        });
    });
</script>
{% endblock %}

{% block extra_head %}
<style>
    .portal-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }

    .upload-card, .cv-info-card {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 30px;
        box-shadow: var(--box-shadow);
    }

    .upload-card h2, .cv-info-card h2 {
        color: var(--secondary-color);
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .upload-card h2 i, .cv-info-card h2 i {
        color: var(--primary-color);
    }

    .file-upload-container {
        margin: 30px 0;
    }

    .file-upload-label {
        display: inline-block;
        padding: 12px 20px;
        background-color: var(--light-color);
        color: var(--secondary-color);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: var(--transition);
    }

    .file-upload-label:hover {
        background-color: #dfe6e9;
    }

    .file-upload-label i {
        margin-right: 10px;
    }

    .file-upload-input {
        display: none;
    }

    .file-name {
        margin-top: 10px;
        font-size: 0.9rem;
        color: #666;
    }

    .file-selected {
        color: var(--primary-color);
        font-weight: 600;
    }

    .upload-button {
        display: block;
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
    }

    .upload-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .upload-status {
        margin-top: 20px;
    }

    .analysis-result {
        margin-bottom: 30px;
        border: 1px solid #eee;
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    .result-header {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
    }

    .result-icon {
        font-size: 1.5rem;
        color: var(--primary-color);
    }

    .result-header h3 {
        margin: 0;
        color: var(--secondary-color);
    }

    .result-content {
        padding: 20px;
    }

    .skills-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .skill-tag {
        display: inline-block;
        padding: 5px 10px;
        background-color: rgba(52, 152, 219, 0.1);
        color: var(--primary-color);
        border-radius: 20px;
        font-size: 0.9rem;
        transition: var(--transition);
    }

    .skill-tag:hover {
        background-color: var(--primary-color);
        color: white;
        transform: translateY(-2px);
    }

    .match-chart {
        margin-bottom: 20px;
    }

    .match-bar {
        height: 30px;
        background: linear-gradient(to right, var(--primary-color), var(--success-color));
        border-radius: 15px;
        color: white;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 10px;
        font-weight: 600;
        width: 0%;
        transition: width 1s ease-in-out;
    }

    .recommended-jobs h4 {
        margin-bottom: 10px;
        color: var(--secondary-color);
    }

    .recommended-jobs ul {
        padding-left: 20px;
    }

    .recommended-jobs li {
        margin-bottom: 5px;
    }

    .recommended-jobs a {
        color: var(--primary-color);
        text-decoration: none;
        transition: var(--transition);
    }

    .recommended-jobs a:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 50px 0;
        color: #aaa;
        text-align: center;
    }

    .empty-icon {
        font-size: 5rem;
        margin-bottom: 20px;
        color: #ddd;
    }

    .features-preview {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 20px;
        flex-wrap: wrap;
    }

    .feature-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        min-width: 120px;
    }

    .feature-item i {
        font-size: 1.5em;
        color: var(--primary-color);
    }

    /* Completeness Section */
    .completeness-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 12px;
        margin-bottom: 25px;
        text-align: center;
    }

    .completeness-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
    }

    .completeness-header h3 {
        margin: 0;
        font-size: 1.3em;
    }

    .score-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: conic-gradient(#2ecc71 0deg, #2ecc71 calc(var(--score) * 3.6deg), rgba(255,255,255,0.3) calc(var(--score) * 3.6deg));
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    .score-circle::before {
        content: '';
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: inherit;
        position: absolute;
    }

    .score-text {
        font-size: 1.2em;
        font-weight: bold;
        z-index: 1;
    }

    .completeness-bar {
        width: 100%;
        height: 8px;
        background-color: rgba(255,255,255,0.3);
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 10px;
    }

    .completeness-fill {
        height: 100%;
        background: linear-gradient(90deg, #e74c3c, #f39c12, #2ecc71);
        border-radius: 4px;
        transition: width 1s ease-in-out;
    }

    .analysis-method {
        font-size: 0.9em;
        opacity: 0.9;
        margin: 0;
    }

    /* Analysis Sections */
    .analysis-section {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f8f9fa;
    }

    .section-header h3 {
        margin: 0;
        color: #495057;
        font-size: 1.2em;
    }

    .section-header i {
        margin-right: 8px;
        color: var(--primary-color);
    }

    .section-status {
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8em;
        font-weight: bold;
    }

    .section-status.complete {
        background-color: #d4edda;
        color: #155724;
    }

    .section-status.improvement {
        background-color: #fff3cd;
        color: #856404;
    }

    .section-status.info {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    /* Info Grid */
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }

    .info-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border-left: 3px solid var(--primary-color);
    }

    .info-item label {
        font-weight: bold;
        margin-right: 10px;
        color: #495057;
        min-width: 80px;
    }

    .info-item label i {
        margin-right: 5px;
        width: 16px;
    }

    /* Education Items */
    .education-item, .experience-item, .project-item, .certification-item {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        border-left: 4px solid var(--primary-color);
    }

    .edu-main h4, .exp-header h4, .project-header h4, .cert-header h4 {
        margin: 0 0 5px 0;
        color: #495057;
        font-size: 1.1em;
    }

    .edu-field, .exp-company, .project-role, .cert-issuer {
        color: #6c757d;
        margin: 0 0 10px 0;
        font-style: italic;
    }

    .edu-details, .exp-details, .project-details, .cert-details {
        margin-top: 15px;
    }

    .edu-institution, .edu-location, .exp-location, .exp-type, .exp-duration {
        display: inline-block;
        margin-right: 15px;
        margin-bottom: 5px;
        font-size: 0.9em;
        color: #6c757d;
    }

    .edu-timeline, .exp-timeline, .project-timeline {
        margin: 10px 0;
        font-weight: bold;
        color: #495057;
    }

    .separator {
        margin: 0 5px;
        color: #6c757d;
    }

    .edu-performance {
        margin: 10px 0;
    }

    .gpa, .grade {
        background-color: #e7f3ff;
        color: #0066cc;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.85em;
        margin-right: 10px;
    }

    .edu-honors, .edu-thesis {
        margin: 8px 0;
        font-size: 0.9em;
    }

    .coursework-tags, .tech-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-top: 8px;
    }

    .course-tag, .tech-tag {
        background-color: #e9ecef;
        color: #495057;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.8em;
    }

    /* Skills Grid */
    .skills-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .skill-category {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        border-top: 3px solid var(--primary-color);
    }

    .skill-category h4 {
        margin: 0 0 10px 0;
        color: #495057;
        font-size: 1em;
    }

    .skill-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
    }

    .skill-tag {
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 0.85em;
        font-weight: 500;
    }

    .skill-tag.programming { background-color: #e3f2fd; color: #1976d2; }
    .skill-tag.framework { background-color: #f3e5f5; color: #7b1fa2; }
    .skill-tag.database { background-color: #e8f5e8; color: #388e3c; }
    .skill-tag.tool { background-color: #fff3e0; color: #f57c00; }
    .skill-tag.cloud { background-color: #fce4ec; color: #c2185b; }

    .soft-skills-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .soft-skill-tag {
        background-color: #e8f4fd;
        color: #1565c0;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.9em;
    }

    /* Experience Sections */
    .exp-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 10px;
    }

    .exp-responsibilities ul, .exp-achievements ul, .project-achievements ul {
        margin: 8px 0;
        padding-left: 20px;
    }

    .exp-responsibilities li, .exp-achievements li, .project-achievements li {
        margin-bottom: 5px;
        line-height: 1.4;
    }

    /* Project Links */
    .project-links {
        margin-top: 15px;
        display: flex;
        gap: 10px;
    }

    .project-link {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        padding: 6px 12px;
        background-color: var(--primary-color);
        color: white;
        text-decoration: none;
        border-radius: 5px;
        font-size: 0.85em;
        transition: background-color 0.3s;
    }

    .project-link:hover {
        background-color: #0056b3;
        color: white;
    }

    /* Recommendations */
    .recommendations-section {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        border-left-color: #ffc107;
    }

    .recommendations-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .recommendation-item {
        display: flex;
        align-items: flex-start;
        gap: 10px;
        padding: 12px;
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 6px;
    }

    .recommendation-item i {
        color: #ffc107;
        margin-top: 2px;
    }

    /* Metadata */
    .metadata-section {
        background-color: #f8f9fa;
        border-left-color: #6c757d;
    }

    .metadata-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .metadata-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #dee2e6;
    }

    .metadata-item label {
        font-weight: bold;
        color: #495057;
    }

    @media (max-width: 768px) {
        .portal-section {
            grid-template-columns: 1fr;
        }

        .info-grid {
            grid-template-columns: 1fr;
        }

        .skills-grid {
            grid-template-columns: 1fr;
        }

        .completeness-header {
            flex-direction: column;
            gap: 15px;
        }
    }
</style>
{% endblock %}
