{% extends "base.html" %}

{% block title %}{{ job.title }} | Applicant Tracking System{% endblock %}

{% block header_title %}{{ job.title }}{% endblock %}
{% block header_subtitle %}{{ job.company }} • {{ job.location }}{% endblock %}

{% block description %}Job details for {{ job.title }} at {{ job.company }}.{% endblock %}

{% block content %}
    <div class="job-details-container fade-in">
        <div class="job-details-card slide-in-bottom">
            <div class="job-header">
                <div class="job-info">
                    <h1>{{ job.title }}</h1>
                    <div class="job-meta">
                        <span class="company"><i class="fas fa-building"></i> {{ job.company }}</span>
                        <span class="location"><i class="fas fa-map-marker-alt"></i> {{ job.location }}</span>
                        <span class="salary"><i class="fas fa-dollar-sign"></i> {{ job.salary }}</span>
                        <span class="posted-date"><i class="fas fa-calendar"></i> Posted on {{ job.posted_date }}</span>
                    </div>
                </div>
                <div class="job-actions">
                    <button class="apply-btn pulse">
                        <i class="fas fa-paper-plane"></i> Apply Now
                    </button>
                    <button class="save-btn">
                        <i class="fas fa-bookmark"></i> Save Job
                    </button>
                </div>
            </div>
            
            <div class="job-content">
                <div class="job-section">
                    <h3><i class="fas fa-info-circle"></i> Job Description</h3>
                    <p>{{ job.description }}</p>
                </div>
                
                <div class="job-section">
                    <h3><i class="fas fa-list-ul"></i> Requirements</h3>
                    <p>{{ job.requirements }}</p>
                </div>
                
                <div class="job-section">
                    <h3><i class="fas fa-handshake"></i> What We Offer</h3>
                    <ul>
                        <li>Competitive salary and benefits package</li>
                        <li>Flexible working hours and remote work options</li>
                        <li>Professional development opportunities</li>
                        <li>Health, dental, and vision insurance</li>
                        <li>500(k) retirement plan with company matching (if applicable)</li>
                        <li>Paid time off and holidays</li>
                    </ul>
                </div>
                
                <div class="job-section">
                    <h3><i class="fas fa-building"></i> About the Company</h3>
                    <p>{{ job.company }} is a leading technology company focused on innovation and excellence. We are committed to creating a diverse and inclusive workplace where all employees can thrive and contribute to our success.</p>
                </div>
            </div>
            
            <div class="job-footer">
                <div class="job-actions-footer">
                    <button class="apply-btn-large pulse">
                        <i class="fas fa-paper-plane"></i> Apply for This Position
                    </button>
                    <a href="{{ url_for('job_listings') }}" class="back-btn">
                        <i class="fas fa-arrow-left"></i> Back to Job Listings
                    </a>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_head %}
<style>
    .job-details-container {
        padding: 40px 20px;
    }
    
    .job-details-card {
        max-width: 900px;
        margin: 0 auto;
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        overflow: hidden;
    }
    
    .job-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 40px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }
    
    .job-info h1 {
        margin: 0 0 20px 0;
        font-size: 2.5rem;
        font-weight: 700;
    }
    
    .job-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
    }
    
    .job-meta span {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 1rem;
        opacity: 0.9;
    }
    
    .job-meta i {
        font-size: 0.9rem;
    }
    
    .job-actions {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .apply-btn, .save-btn {
        padding: 12px 20px;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;
    }
    
    .apply-btn {
        background-color: white;
        color: var(--primary-color);
    }
    
    .apply-btn:hover {
        background-color: #f8f9fa;
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .save-btn {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }
    
    .save-btn:hover {
        background-color: rgba(255, 255, 255, 0.3);
        transform: translateY(-3px);
    }
    
    .job-content {
        padding: 40px;
    }
    
    .job-section {
        margin-bottom: 40px;
    }
    
    .job-section:last-child {
        margin-bottom: 0;
    }
    
    .job-section h3 {
        color: var(--secondary-color);
        margin-bottom: 20px;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .job-section h3 i {
        color: var(--primary-color);
        font-size: 1.3rem;
    }
    
    .job-section p {
        line-height: 1.8;
        color: #333;
        margin-bottom: 15px;
    }
    
    .job-section ul {
        margin-left: 20px;
    }
    
    .job-section li {
        margin-bottom: 10px;
        line-height: 1.6;
        color: #333;
    }
    
    .job-footer {
        background-color: #f8f9fa;
        padding: 30px 40px;
        border-top: 1px solid #eee;
    }
    
    .job-actions-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 20px;
    }
    
    .apply-btn-large {
        padding: 15px 30px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .apply-btn-large:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .back-btn {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: var(--transition);
    }
    
    .back-btn:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }
    
    @media (max-width: 768px) {
        .job-header {
            flex-direction: column;
            gap: 30px;
        }
        
        .job-info h1 {
            font-size: 2rem;
        }
        
        .job-meta {
            flex-direction: column;
            gap: 10px;
        }
        
        .job-actions {
            flex-direction: row;
            width: 100%;
        }
        
        .job-actions-footer {
            flex-direction: column;
            align-items: stretch;
        }
        
        .apply-btn-large {
            justify-content: center;
        }
        
        .back-btn {
            justify-content: center;
        }
    }
</style>
{% endblock %}
