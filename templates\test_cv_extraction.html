<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CV Extraction - Resume Analyzer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            background-color: #f8f9fa;
        }
        .upload-area:hover {
            background-color: #e9ecef;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .info-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background-color: white;
        }
        .info-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Enhanced CV Extraction</h1>
        <p>Upload a CV file to test the enhanced Gemini AI extraction capabilities.</p>
        
        <form id="cvForm" enctype="multipart/form-data">
            <div class="upload-area">
                <h3>Upload CV File</h3>
                <input type="file" id="cvFile" name="cv_file" accept=".pdf,.docx,.doc" required>
                <p>Supported formats: PDF, DOCX, DOC</p>
            </div>
            <button type="submit" class="btn">Extract CV Information</button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Analyzing CV with Gemini AI...</p>
        </div>
        
        <div id="message"></div>
        
        <div class="results" id="results">
            <h2>Extraction Results</h2>
            <div id="extractedInfo"></div>
        </div>
    </div>

    <script>
        document.getElementById('cvForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('cvFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showMessage('Please select a file', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('cv_file', file);
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            document.getElementById('message').innerHTML = '';
            
            fetch('/api/test_cv_extraction', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                
                if (data.status === 'success') {
                    showMessage('CV extracted successfully!', 'success');
                    displayResults(data.cv_info);
                } else {
                    showMessage('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                showMessage('Error: ' + error.message, 'error');
            });
        });
        
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        function displayResults(cvInfo) {
            const resultsDiv = document.getElementById('extractedInfo');
            let html = '';
            
            // Personal Information
            if (cvInfo.personal_info) {
                html += '<div class="info-section">';
                html += '<h3>Personal Information</h3>';
                html += `<p><strong>Name:</strong> ${cvInfo.personal_info.name || 'Not found'}</p>`;
                html += `<p><strong>Email:</strong> ${cvInfo.personal_info.email || 'Not found'}</p>`;
                html += `<p><strong>Phone:</strong> ${cvInfo.personal_info.phone || 'Not found'}</p>`;
                html += `<p><strong>LinkedIn:</strong> ${cvInfo.personal_info.linkedin || 'Not found'}</p>`;
                html += '</div>';
            }
            
            // Educational Background
            if (cvInfo.educational_background && cvInfo.educational_background.length > 0) {
                html += '<div class="info-section">';
                html += '<h3>Educational Background</h3>';
                cvInfo.educational_background.forEach(edu => {
                    html += '<div style="margin-bottom: 15px; padding: 15px; border-left: 4px solid #007bff; background-color: #f8f9fa;">';
                    html += `<p><strong>Degree:</strong> ${edu.degree || 'Not specified'}`;
                    if (edu.degree_abbreviation) html += ` (${edu.degree_abbreviation})`;
                    html += '</p>';
                    html += `<p><strong>Field of Study:</strong> ${edu.field || 'Not specified'}</p>`;
                    if (edu.specialization) html += `<p><strong>Specialization:</strong> ${edu.specialization}</p>`;
                    html += `<p><strong>Institution:</strong> ${edu.institution || 'Not specified'}</p>`;
                    if (edu.location) html += `<p><strong>Location:</strong> ${edu.location}</p>`;
                    if (edu.start_year) html += `<p><strong>Start Year:</strong> ${edu.start_year}</p>`;
                    html += `<p><strong>Graduation Year:</strong> ${edu.graduation_year || 'Not specified'}</p>`;
                    if (edu.duration) html += `<p><strong>Duration:</strong> ${edu.duration}</p>`;
                    if (edu.gpa) html += `<p><strong>GPA/Score:</strong> ${edu.gpa}</p>`;
                    if (edu.grade) html += `<p><strong>Grade/Classification:</strong> ${edu.grade}</p>`;
                    if (edu.honors) html += `<p><strong>Honors:</strong> ${edu.honors}</p>`;
                    if (edu.thesis_title) html += `<p><strong>Thesis:</strong> ${edu.thesis_title}</p>`;
                    if (edu.relevant_coursework && edu.relevant_coursework.length > 0) {
                        html += `<p><strong>Relevant Coursework:</strong> ${edu.relevant_coursework.join(', ')}</p>`;
                    }
                    html += '</div>';
                });
                html += '</div>';
            }
            
            // Work Experience
            if (cvInfo.work_experience && cvInfo.work_experience.length > 0) {
                html += '<div class="info-section">';
                html += '<h3>Work Experience</h3>';
                cvInfo.work_experience.forEach(exp => {
                    html += '<div style="margin-bottom: 15px; padding: 15px; border-left: 4px solid #28a745; background-color: #f8f9fa;">';
                    html += `<p><strong>Job Title:</strong> ${exp.job_title || 'Not specified'}</p>`;
                    html += `<p><strong>Company:</strong> ${exp.company || 'Not specified'}`;
                    if (exp.company_type) html += ` (${exp.company_type})`;
                    html += '</p>';
                    if (exp.location) html += `<p><strong>Location:</strong> ${exp.location}</p>`;
                    if (exp.employment_type) html += `<p><strong>Employment Type:</strong> ${exp.employment_type}</p>`;
                    if (exp.start_date) html += `<p><strong>Start Date:</strong> ${exp.start_date}</p>`;
                    if (exp.end_date) html += `<p><strong>End Date:</strong> ${exp.end_date}</p>`;
                    html += `<p><strong>Duration:</strong> ${exp.duration || 'Not specified'}</p>`;
                    if (exp.team_size) html += `<p><strong>Team Size:</strong> ${exp.team_size}</p>`;
                    if (exp.reporting_to) html += `<p><strong>Reporting To:</strong> ${exp.reporting_to}</p>`;

                    if (exp.responsibilities && exp.responsibilities.length > 0) {
                        html += '<p><strong>Responsibilities:</strong></p><ul>';
                        exp.responsibilities.forEach(resp => {
                            html += `<li>${resp}</li>`;
                        });
                        html += '</ul>';
                    }

                    if (exp.achievements && exp.achievements.length > 0) {
                        html += '<p><strong>Achievements:</strong></p><ul>';
                        exp.achievements.forEach(ach => {
                            html += `<li>${ach}</li>`;
                        });
                        html += '</ul>';
                    }

                    if (exp.technologies_used && exp.technologies_used.length > 0) {
                        html += `<p><strong>Technologies Used:</strong> ${exp.technologies_used.join(', ')}</p>`;
                    }

                    html += '</div>';
                });
                html += '</div>';
            }
            
            // Technical Skills
            if (cvInfo.technical_skills) {
                html += '<div class="info-section">';
                html += '<h3>Technical Skills</h3>';

                if (cvInfo.technical_skills.programming_languages && cvInfo.technical_skills.programming_languages.length > 0) {
                    html += `<p><strong>Programming Languages:</strong> ${cvInfo.technical_skills.programming_languages.join(', ')}</p>`;
                }
                if (cvInfo.technical_skills.frameworks && cvInfo.technical_skills.frameworks.length > 0) {
                    html += `<p><strong>Frameworks:</strong> ${cvInfo.technical_skills.frameworks.join(', ')}</p>`;
                }
                if (cvInfo.technical_skills.databases && cvInfo.technical_skills.databases.length > 0) {
                    html += `<p><strong>Databases:</strong> ${cvInfo.technical_skills.databases.join(', ')}</p>`;
                }
                if (cvInfo.technical_skills.tools && cvInfo.technical_skills.tools.length > 0) {
                    html += `<p><strong>Tools:</strong> ${cvInfo.technical_skills.tools.join(', ')}</p>`;
                }
                if (cvInfo.technical_skills.cloud_platforms && cvInfo.technical_skills.cloud_platforms.length > 0) {
                    html += `<p><strong>Cloud Platforms:</strong> ${cvInfo.technical_skills.cloud_platforms.join(', ')}</p>`;
                }
                if (cvInfo.technical_skills.operating_systems && cvInfo.technical_skills.operating_systems.length > 0) {
                    html += `<p><strong>Operating Systems:</strong> ${cvInfo.technical_skills.operating_systems.join(', ')}</p>`;
                }
                if (cvInfo.technical_skills.methodologies && cvInfo.technical_skills.methodologies.length > 0) {
                    html += `<p><strong>Methodologies:</strong> ${cvInfo.technical_skills.methodologies.join(', ')}</p>`;
                }
                if (cvInfo.technical_skills.other_technical && cvInfo.technical_skills.other_technical.length > 0) {
                    html += `<p><strong>Other Technical:</strong> ${cvInfo.technical_skills.other_technical.join(', ')}</p>`;
                }

                html += '</div>';
            }
            
            // Soft Skills
            if (cvInfo.soft_skills && cvInfo.soft_skills.length > 0) {
                html += '<div class="info-section">';
                html += '<h3>Soft Skills</h3>';
                html += '<p>' + cvInfo.soft_skills.join(', ') + '</p>';
                html += '</div>';
            }

            // Projects
            if (cvInfo.projects && cvInfo.projects.length > 0) {
                html += '<div class="info-section">';
                html += '<h3>Projects</h3>';
                cvInfo.projects.forEach(project => {
                    html += '<div style="margin-bottom: 15px; padding: 15px; border-left: 4px solid #ffc107; background-color: #f8f9fa;">';
                    html += `<p><strong>Project Name:</strong> ${project.name || 'Not specified'}</p>`;
                    if (project.description) html += `<p><strong>Description:</strong> ${project.description}</p>`;
                    if (project.role) html += `<p><strong>Role:</strong> ${project.role}</p>`;
                    if (project.start_date) html += `<p><strong>Start Date:</strong> ${project.start_date}</p>`;
                    if (project.end_date) html += `<p><strong>End Date:</strong> ${project.end_date}</p>`;
                    if (project.duration) html += `<p><strong>Duration:</strong> ${project.duration}</p>`;
                    if (project.team_size) html += `<p><strong>Team Size:</strong> ${project.team_size}</p>`;

                    if (project.technologies && project.technologies.length > 0) {
                        html += `<p><strong>Technologies:</strong> ${project.technologies.join(', ')}</p>`;
                    }

                    if (project.achievements && project.achievements.length > 0) {
                        html += '<p><strong>Achievements:</strong></p><ul>';
                        project.achievements.forEach(ach => {
                            html += `<li>${ach}</li>`;
                        });
                        html += '</ul>';
                    }

                    if (project.url) html += `<p><strong>Project URL:</strong> <a href="${project.url}" target="_blank">${project.url}</a></p>`;
                    if (project.github_url) html += `<p><strong>GitHub:</strong> <a href="${project.github_url}" target="_blank">${project.github_url}</a></p>`;

                    html += '</div>';
                });
                html += '</div>';
            }

            // Certifications
            if (cvInfo.certifications && cvInfo.certifications.length > 0) {
                html += '<div class="info-section">';
                html += '<h3>Certifications</h3>';
                cvInfo.certifications.forEach(cert => {
                    html += '<div style="margin-bottom: 10px; padding: 10px; border-left: 4px solid #17a2b8; background-color: #f8f9fa;">';
                    html += `<p><strong>Certification:</strong> ${cert.name || 'Not specified'}</p>`;
                    if (cert.issuer) html += `<p><strong>Issuer:</strong> ${cert.issuer}</p>`;
                    if (cert.date_obtained) html += `<p><strong>Date Obtained:</strong> ${cert.date_obtained}</p>`;
                    if (cert.expiry_date) html += `<p><strong>Expiry Date:</strong> ${cert.expiry_date}</p>`;
                    if (cert.credential_id) html += `<p><strong>Credential ID:</strong> ${cert.credential_id}</p>`;
                    if (cert.verification_url) html += `<p><strong>Verification:</strong> <a href="${cert.verification_url}" target="_blank">Verify</a></p>`;
                    html += '</div>';
                });
                html += '</div>';
            }
            
            // Raw JSON for debugging
            html += '<div class="info-section">';
            html += '<h3>Raw Extracted Data (JSON)</h3>';
            html += '<pre>' + JSON.stringify(cvInfo, null, 2) + '</pre>';
            html += '</div>';
            
            resultsDiv.innerHTML = html;
            document.getElementById('results').style.display = 'block';
        }
    </script>
</body>
</html>
