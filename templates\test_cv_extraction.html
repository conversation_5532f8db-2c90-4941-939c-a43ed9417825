<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CV Extraction - Resume Analyzer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            background-color: #f8f9fa;
        }
        .upload-area:hover {
            background-color: #e9ecef;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .info-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background-color: white;
        }
        .info-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Enhanced CV Extraction</h1>
        <p>Upload a CV file to test the enhanced Gemini AI extraction capabilities.</p>
        
        <form id="cvForm" enctype="multipart/form-data">
            <div class="upload-area">
                <h3>Upload CV File</h3>
                <input type="file" id="cvFile" name="cv_file" accept=".pdf,.docx,.doc" required>
                <p>Supported formats: PDF, DOCX, DOC</p>
            </div>
            <button type="submit" class="btn">Extract CV Information</button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Analyzing CV with Gemini AI...</p>
        </div>
        
        <div id="message"></div>
        
        <div class="results" id="results">
            <h2>Extraction Results</h2>
            <div id="extractedInfo"></div>
        </div>
    </div>

    <script>
        document.getElementById('cvForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('cvFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showMessage('Please select a file', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('cv_file', file);
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            document.getElementById('message').innerHTML = '';
            
            fetch('/api/test_cv_extraction', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                
                if (data.status === 'success') {
                    showMessage('CV extracted successfully!', 'success');
                    displayResults(data.cv_info);
                } else {
                    showMessage('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                showMessage('Error: ' + error.message, 'error');
            });
        });
        
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        function displayResults(cvInfo) {
            const resultsDiv = document.getElementById('extractedInfo');
            let html = '';
            
            // Personal Information
            if (cvInfo.personal_info) {
                html += '<div class="info-section">';
                html += '<h3>Personal Information</h3>';
                html += `<p><strong>Name:</strong> ${cvInfo.personal_info.name || 'Not found'}</p>`;
                html += `<p><strong>Email:</strong> ${cvInfo.personal_info.email || 'Not found'}</p>`;
                html += `<p><strong>Phone:</strong> ${cvInfo.personal_info.phone || 'Not found'}</p>`;
                html += `<p><strong>LinkedIn:</strong> ${cvInfo.personal_info.linkedin || 'Not found'}</p>`;
                html += '</div>';
            }
            
            // Educational Background
            if (cvInfo.educational_background && cvInfo.educational_background.length > 0) {
                html += '<div class="info-section">';
                html += '<h3>Educational Background</h3>';
                cvInfo.educational_background.forEach(edu => {
                    html += '<div style="margin-bottom: 10px; padding: 10px; border-left: 3px solid #007bff;">';
                    html += `<p><strong>Degree:</strong> ${edu.degree || 'Not specified'}</p>`;
                    html += `<p><strong>Field:</strong> ${edu.field || 'Not specified'}</p>`;
                    html += `<p><strong>Institution:</strong> ${edu.institution || 'Not specified'}</p>`;
                    html += `<p><strong>Graduation Year:</strong> ${edu.graduation_year || 'Not specified'}</p>`;
                    if (edu.gpa) html += `<p><strong>GPA:</strong> ${edu.gpa}</p>`;
                    if (edu.honors) html += `<p><strong>Honors:</strong> ${edu.honors}</p>`;
                    html += '</div>';
                });
                html += '</div>';
            }
            
            // Work Experience
            if (cvInfo.work_experience && cvInfo.work_experience.length > 0) {
                html += '<div class="info-section">';
                html += '<h3>Work Experience</h3>';
                cvInfo.work_experience.forEach(exp => {
                    html += '<div style="margin-bottom: 10px; padding: 10px; border-left: 3px solid #28a745;">';
                    html += `<p><strong>Job Title:</strong> ${exp.job_title || 'Not specified'}</p>`;
                    html += `<p><strong>Company:</strong> ${exp.company || 'Not specified'}</p>`;
                    html += `<p><strong>Duration:</strong> ${exp.duration || 'Not specified'}</p>`;
                    if (exp.location) html += `<p><strong>Location:</strong> ${exp.location}</p>`;
                    html += '</div>';
                });
                html += '</div>';
            }
            
            // Technical Skills
            if (cvInfo.technical_skills && cvInfo.technical_skills.length > 0) {
                html += '<div class="info-section">';
                html += '<h3>Technical Skills</h3>';
                html += '<p>' + cvInfo.technical_skills.join(', ') + '</p>';
                html += '</div>';
            }
            
            // Soft Skills
            if (cvInfo.soft_skills && cvInfo.soft_skills.length > 0) {
                html += '<div class="info-section">';
                html += '<h3>Soft Skills</h3>';
                html += '<p>' + cvInfo.soft_skills.join(', ') + '</p>';
                html += '</div>';
            }
            
            // Raw JSON for debugging
            html += '<div class="info-section">';
            html += '<h3>Raw Extracted Data (JSON)</h3>';
            html += '<pre>' + JSON.stringify(cvInfo, null, 2) + '</pre>';
            html += '</div>';
            
            resultsDiv.innerHTML = html;
            document.getElementById('results').style.display = 'block';
        }
    </script>
</body>
</html>
