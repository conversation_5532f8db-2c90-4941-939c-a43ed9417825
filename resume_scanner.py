"""
Resume Scanner Module

This module provides advanced functionality for analyzing resumes and job descriptions,
calculating match scores, and providing optimization suggestions.

It's designed to work similarly to JobScan.co, helping job seekers optimize their
resumes for specific job descriptions.
"""

import re
import os
import logging
import json
from typing import Dict, List, Tuple, Any, Set, Optional
from collections import Counter
import datetime
import string
from pathlib import Path

# NLP related imports
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer
from textblob import TextBlob

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize NLTK components
lemmatizer = WordNetLemmatizer()
stop_words = set(stopwords.words('english'))

# Define skill categories and keywords
SKILL_CATEGORIES = {
    'Programming Languages': [
        'python', 'java', 'javascript', 'c++', 'c#', 'ruby', 'php', 'swift', 'kotlin', 
        'typescript', 'go', 'rust', 'scala', 'perl', 'r', 'matlab', 'bash', 'shell',
        'html', 'css', 'sql', 'nosql', 'dart', 'objective-c', 'assembly', 'fortran'
    ],
    'Frameworks & Libraries': [
        'react', 'angular', 'vue', 'django', 'flask', 'spring', 'express', 'node.js',
        'tensorflow', 'pytorch', 'keras', 'scikit-learn', 'pandas', 'numpy', 'bootstrap',
        'jquery', 'laravel', 'symfony', 'rails', 'asp.net', '.net', 'xamarin', 'flutter'
    ],
    'Databases': [
        'mysql', 'postgresql', 'mongodb', 'sqlite', 'oracle', 'sql server', 'redis',
        'cassandra', 'elasticsearch', 'dynamodb', 'firebase', 'mariadb', 'neo4j', 'couchdb'
    ],
    'DevOps & Tools': [
        'git', 'docker', 'kubernetes', 'jenkins', 'aws', 'azure', 'gcp', 'terraform',
        'ansible', 'puppet', 'chef', 'circleci', 'travis', 'jira', 'confluence', 'bitbucket',
        'github', 'gitlab', 'linux', 'unix', 'windows', 'macos', 'bash', 'powershell'
    ],
    'Design & UI/UX': [
        'photoshop', 'illustrator', 'sketch', 'figma', 'adobe xd', 'indesign', 'ui', 'ux',
        'wireframing', 'prototyping', 'user research', 'usability testing', 'responsive design'
    ],
    'Soft Skills': [
        'communication', 'teamwork', 'leadership', 'problem solving', 'critical thinking',
        'time management', 'adaptability', 'creativity', 'emotional intelligence', 'negotiation',
        'conflict resolution', 'presentation', 'public speaking', 'writing', 'mentoring'
    ],
    'Project Management': [
        'agile', 'scrum', 'kanban', 'waterfall', 'prince2', 'pmp', 'lean', 'six sigma',
        'risk management', 'budgeting', 'resource allocation', 'gantt', 'stakeholder management'
    ],
    'Data Science': [
        'machine learning', 'deep learning', 'data mining', 'data analysis', 'statistics',
        'big data', 'data visualization', 'natural language processing', 'computer vision',
        'predictive modeling', 'a/b testing', 'etl', 'data warehousing', 'business intelligence'
    ],
    'Marketing': [
        'seo', 'sem', 'social media', 'content marketing', 'email marketing', 'google analytics',
        'facebook ads', 'google ads', 'marketing automation', 'crm', 'brand management',
        'market research', 'copywriting', 'digital marketing', 'growth hacking'
    ],
    'Finance': [
        'accounting', 'financial analysis', 'budgeting', 'forecasting', 'financial modeling',
        'excel', 'quickbooks', 'sap', 'oracle financials', 'tax', 'audit', 'risk assessment',
        'financial reporting', 'investment analysis', 'portfolio management'
    ]
}

# Flatten the skills dictionary for easier lookup
ALL_SKILLS = {}
for category, skills in SKILL_CATEGORIES.items():
    for skill in skills:
        ALL_SKILLS[skill] = category

# Common resume sections
RESUME_SECTIONS = [
    'education', 'experience', 'work experience', 'employment', 'skills', 'technical skills',
    'projects', 'certifications', 'awards', 'publications', 'languages', 'interests',
    'summary', 'objective', 'professional summary', 'qualifications', 'achievements'
]

# Common job description sections
JOB_SECTIONS = [
    'responsibilities', 'requirements', 'qualifications', 'about us', 'about the company',
    'what you'll do', 'what we're looking for', 'benefits', 'perks', 'salary', 'location',
    'who you are', 'desired skills', 'preferred qualifications', 'essential functions'
]

def preprocess_text(text: str) -> str:
    """
    Preprocess text by removing special characters, extra whitespace, etc.
    
    Args:
        text: Raw text to preprocess
        
    Returns:
        Preprocessed text
    """
    # Convert to lowercase
    text = text.lower()
    
    # Remove punctuation
    text = text.translate(str.maketrans('', '', string.punctuation))
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def extract_keywords(text: str, is_job_description: bool = False) -> Dict[str, List[str]]:
    """
    Extract keywords from text and categorize them.
    
    Args:
        text: Text to extract keywords from
        is_job_description: Whether the text is a job description
        
    Returns:
        Dictionary of keyword categories and their keywords
    """
    # Preprocess text
    processed_text = preprocess_text(text)
    
    # Tokenize
    tokens = word_tokenize(processed_text)
    
    # Remove stopwords
    filtered_tokens = [token for token in tokens if token not in stop_words]
    
    # Lemmatize
    lemmatized_tokens = [lemmatizer.lemmatize(token) for token in filtered_tokens]
    
    # Extract n-grams (1-3)
    unigrams = lemmatized_tokens
    bigrams = [' '.join(lemmatized_tokens[i:i+2]) for i in range(len(lemmatized_tokens)-1)]
    trigrams = [' '.join(lemmatized_tokens[i:i+3]) for i in range(len(lemmatized_tokens)-2)]
    
    all_ngrams = unigrams + bigrams + trigrams
    
    # Find skills
    found_skills = {}
    for ngram in all_ngrams:
        for skill, category in ALL_SKILLS.items():
            if skill == ngram or (len(skill) > 3 and skill in ngram):
                if category not in found_skills:
                    found_skills[category] = []
                if skill not in found_skills[category]:
                    found_skills[category].append(skill)
    
    # Extract additional keywords for job descriptions
    if is_job_description:
        # Look for years of experience
        experience_patterns = [
            r'(\d+)\+?\s+years?\s+(?:of\s+)?experience',
            r'experience\s+(?:of\s+)?(\d+)\+?\s+years?',
            r'minimum\s+(?:of\s+)?(\d+)\+?\s+years?\s+(?:of\s+)?experience'
        ]
        
        years_of_experience = []
        for pattern in experience_patterns:
            matches = re.findall(pattern, text.lower())
            years_of_experience.extend(matches)
        
        if years_of_experience:
            found_skills['Experience Requirements'] = [f"{years} years" for years in years_of_experience]
        
        # Look for education requirements
        education_patterns = [
            r"bachelor'?s?\s+degree",
            r"master'?s?\s+degree",
            r"ph\.?d\.?",
            r"doctorate",
            r"mba",
            r"associate'?s?\s+degree",
            r"high\s+school\s+diploma",
            r"ged"
        ]
        
        education_requirements = []
        for pattern in education_patterns:
            if re.search(pattern, text.lower()):
                education_requirements.append(pattern)
        
        if education_requirements:
            found_skills['Education Requirements'] = education_requirements
    
    return found_skills

def calculate_match_score(resume_keywords: Dict[str, List[str]], job_keywords: Dict[str, List[str]]) -> Dict[str, Any]:
    """
    Calculate match score between resume and job description.
    
    Args:
        resume_keywords: Keywords extracted from resume
        job_keywords: Keywords extracted from job description
        
    Returns:
        Dictionary with match score and details
    """
    # Flatten keyword lists
    resume_kw_flat = []
    for category, keywords in resume_keywords.items():
        resume_kw_flat.extend(keywords)
    
    job_kw_flat = []
    for category, keywords in job_keywords.items():
        job_kw_flat.extend(keywords)
    
    # Count matches
    matches = []
    missing = []
    
    for keyword in job_kw_flat:
        if keyword in resume_kw_flat:
            matches.append(keyword)
        else:
            missing.append(keyword)
    
    # Calculate score
    if len(job_kw_flat) > 0:
        match_percentage = (len(matches) / len(job_kw_flat)) * 100
    else:
        match_percentage = 0
    
    # Prepare result
    result = {
        'match_score': round(match_percentage, 1),
        'matches': matches,
        'missing': missing,
        'total_job_keywords': len(job_kw_flat),
        'total_resume_keywords': len(resume_kw_flat),
        'matched_keywords': len(matches)
    }
    
    return result

def generate_improvement_suggestions(match_result: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Generate suggestions for improving resume match score.
    
    Args:
        match_result: Result from calculate_match_score
        
    Returns:
        List of improvement suggestions
    """
    suggestions = []
    
    # Suggest adding missing keywords
    if match_result['missing']:
        suggestions.append({
            'type': 'missing_keywords',
            'title': 'Add Missing Keywords',
            'description': 'Consider adding these keywords to your resume:',
            'items': match_result['missing'][:10]  # Limit to top 10
        })
    
    # Suggest formatting improvements
    suggestions.append({
        'type': 'formatting',
        'title': 'Optimize Resume Format',
        'description': 'Ensure your resume is properly formatted:',
        'items': [
            'Use a clean, professional layout',
            'Include clear section headings',
            'Use bullet points for readability',
            'Keep to 1-2 pages in length',
            'Use a professional font (Arial, Calibri, Times New Roman)'
        ]
    })
    
    # Suggest ATS optimization
    suggestions.append({
        'type': 'ats_optimization',
        'title': 'Optimize for ATS (Applicant Tracking Systems)',
        'description': 'Make your resume more ATS-friendly:',
        'items': [
            'Avoid using tables, headers, or footers',
            'Use standard section headings',
            'Avoid using images or graphics',
            'Use standard file formats (PDF, DOCX)',
            'Include the exact keywords from the job description'
        ]
    })
    
    return suggestions

def analyze_resume_and_job(resume_text: str, job_description: str) -> Dict[str, Any]:
    """
    Analyze resume and job description to provide match score and suggestions.
    
    Args:
        resume_text: Text content of the resume
        job_description: Text content of the job description
        
    Returns:
        Dictionary with analysis results
    """
    # Extract keywords
    resume_keywords = extract_keywords(resume_text)
    job_keywords = extract_keywords(job_description, is_job_description=True)
    
    # Calculate match score
    match_result = calculate_match_score(resume_keywords, job_keywords)
    
    # Generate suggestions
    suggestions = generate_improvement_suggestions(match_result)
    
    # Prepare result
    result = {
        'timestamp': datetime.datetime.now().isoformat(),
        'resume_keywords': resume_keywords,
        'job_keywords': job_keywords,
        'match_result': match_result,
        'suggestions': suggestions
    }
    
    return result
