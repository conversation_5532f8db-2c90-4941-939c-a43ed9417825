/**
 * Applicant Tracking System
 * Advanced JavaScript functionality for the ATS application
 *
 * This file contains client-side functionality for the Applicant Tracking System,
 * including form validation, file upload handling, and dynamic UI updates.
 *
 * <AUTHOR>
 * @version 1.0.0
 */

// Use strict mode for better error catching and performance
'use strict';

// Configuration
const CONFIG = {
    // Allowed file extensions for CV uploads
    ALLOWED_EXTENSIONS: ['pdf', 'docx'],

    // Maximum file size in bytes (5MB)
    MAX_FILE_SIZE: 5 * 1024 * 1024,

    // Animation duration for UI effects (in milliseconds)
    ANIMATION_DURATION: 300,

    // API endpoints
    API: {
        JOB_SEARCH: '/api/job_search'
    }
};

/**
 * Document ready function - executes when DOM is fully loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Application initialized');

    // Initialize all components
    initializeFormValidation();
    initializeFileUpload();
    initializeJobSearch();
    initializeAnimations();

    // Add event listeners for flash messages
    setupFlashMessages();
});

/**
 * Initialize form validation for all forms
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!validateForm(form)) {
                event.preventDefault();
            }
        });
    });

    // Add input validation on blur
    const inputs = document.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateInput(input);
        });
    });
}

/**
 * Validate an individual form
 * @param {HTMLFormElement} form - The form to validate
 * @returns {boolean} - Whether the form is valid
 */
function validateForm(form) {
    let isValid = true;

    // Check all required inputs
    const requiredInputs = form.querySelectorAll('[required]');
    requiredInputs.forEach(input => {
        if (!validateInput(input)) {
            isValid = false;
        }
    });

    // Check file upload if present
    const fileInput = form.querySelector('input[type="file"]');
    if (fileInput && fileInput.files.length > 0) {
        if (!validateFileUpload(fileInput)) {
            isValid = false;
        }
    }

    return isValid;
}

/**
 * Validate an individual input field
 * @param {HTMLInputElement} input - The input to validate
 * @returns {boolean} - Whether the input is valid
 */
function validateInput(input) {
    // Skip if the input is not required and empty
    if (!input.hasAttribute('required') && !input.value.trim()) {
        removeErrorMessage(input);
        return true;
    }

    // Check if empty
    if (input.hasAttribute('required') && !input.value.trim()) {
        showErrorMessage(input, 'This field is required');
        return false;
    }

    // Email validation
    if (input.type === 'email') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(input.value)) {
            showErrorMessage(input, 'Please enter a valid email address');
            return false;
        }
    }

    // All validations passed
    removeErrorMessage(input);
    return true;
}

/**
 * Show an error message for an input
 * @param {HTMLElement} input - The input with the error
 * @param {string} message - The error message to display
 */
function showErrorMessage(input, message) {
    // Remove any existing error message
    removeErrorMessage(input);

    // Create error message element
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    errorElement.style.color = 'var(--error-color)';
    errorElement.style.fontSize = '0.85rem';
    errorElement.style.marginTop = '-15px';
    errorElement.style.marginBottom = '15px';
    errorElement.textContent = message;

    // Insert error message after the input
    input.parentNode.insertBefore(errorElement, input.nextSibling);

    // Add error class to input
    input.classList.add('input-error');
    input.style.borderColor = 'var(--error-color)';
}

/**
 * Remove error message from an input
 * @param {HTMLElement} input - The input to remove the error from
 */
function removeErrorMessage(input) {
    // Find and remove error message if it exists
    const errorElement = input.nextSibling;
    if (errorElement && errorElement.className === 'error-message') {
        errorElement.parentNode.removeChild(errorElement);
    }

    // Remove error class from input
    input.classList.remove('input-error');
    input.style.borderColor = '';
}

/**
 * Initialize file upload functionality
 */
function initializeFileUpload() {
    const fileInput = document.getElementById('file');
    if (!fileInput) return;

    fileInput.addEventListener('change', function() {
        validateFileUpload(fileInput);
    });
}

/**
 * Validate file upload
 * @param {HTMLInputElement} fileInput - The file input element
 * @returns {boolean} - Whether the file is valid
 */
function validateFileUpload(fileInput) {
    const uploadStatus = document.getElementById('upload-status');
    if (!uploadStatus) return true;

    // Clear previous status
    uploadStatus.innerHTML = '';

    const file = fileInput.files[0];
    if (!file) {
        createStatusMessage(uploadStatus, 'No file selected.', 'error');
        return false;
    }

    // Check file extension
    const fileName = file.name;
    const fileExtension = fileName.split('.').pop().toLowerCase();

    if (!CONFIG.ALLOWED_EXTENSIONS.includes(fileExtension)) {
        createStatusMessage(
            uploadStatus,
            `Invalid file type. Please upload a ${CONFIG.ALLOWED_EXTENSIONS.join(' or ')} file.`,
            'error'
        );
        return false;
    }

    // Check file size
    if (file.size > CONFIG.MAX_FILE_SIZE) {
        createStatusMessage(
            uploadStatus,
            `File too large. Maximum size is ${CONFIG.MAX_FILE_SIZE / (1024 * 1024)}MB.`,
            'error'
        );
        return false;
    }

    // File is valid
    createStatusMessage(uploadStatus, 'File selected successfully!', 'success');
    return true;
}

/**
 * Create a status message in the specified container
 * @param {HTMLElement} container - The container for the message
 * @param {string} message - The message text
 * @param {string} type - The message type (success, error, warning)
 */
function createStatusMessage(container, message, type) {
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `alert alert-${type}`;
    messageElement.textContent = message;

    // Add to container
    container.innerHTML = '';
    container.appendChild(messageElement);
}

/**
 * Initialize job search functionality
 */
function initializeJobSearch() {
    const searchForm = document.getElementById('job-search-form');
    if (!searchForm) return;

    searchForm.addEventListener('submit', function(event) {
        event.preventDefault();

        const searchInput = document.getElementById('job-search-input');
        if (!searchInput) return;

        const query = searchInput.value.trim();
        searchJobs(query);
    });
}

/**
 * Search for jobs using the API
 * @param {string} query - The search query
 */
function searchJobs(query) {
    const resultsContainer = document.getElementById('job-search-results');
    if (!resultsContainer) return;

    // Show loading indicator
    resultsContainer.innerHTML = '<div class="loading">Searching...</div>';

    // Make API request
    fetch(`${CONFIG.API.JOB_SEARCH}?query=${encodeURIComponent(query)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            displayJobResults(data, resultsContainer);
        })
        .catch(error => {
            console.error('Error searching jobs:', error);
            resultsContainer.innerHTML = `<div class="alert alert-error">Error searching jobs: ${error.message}</div>`;
        });
}

/**
 * Display job search results
 * @param {Object} data - The job search results data
 * @param {HTMLElement} container - The container to display results in
 */
function displayJobResults(data, container) {
    if (data.count === 0) {
        container.innerHTML = '<div class="alert alert-warning">No jobs found matching your search criteria.</div>';
        return;
    }

    let html = `<p>Found ${data.count} job(s):</p>`;
    html += '<div class="job-list">';

    data.jobs.forEach(job => {
        html += `
            <div class="card job-card">
                <div class="card-header">${job.title}</div>
                <div class="job-company">${job.company}</div>
                <div class="job-location">${job.location}</div>
                <p>${job.description}</p>
                <div class="job-date">Posted: ${job.posted_date}</div>
                <button class="button job-apply-btn" data-job-id="${job.id}">Apply Now</button>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;

    // Add event listeners to apply buttons
    const applyButtons = container.querySelectorAll('.job-apply-btn');
    applyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const jobId = this.getAttribute('data-job-id');
            applyForJob(jobId);
        });
    });
}

/**
 * Apply for a job
 * @param {string} jobId - The ID of the job to apply for
 */
function applyForJob(jobId) {
    alert(`Redirecting to application form for job ID: ${jobId}`);
    // In a real application, this would redirect to an application form
    window.location.href = `/applicant_portal?job_id=${jobId}`;
}

/**
 * Initialize animations for UI elements
 */
function initializeAnimations() {
    // Animate cards on hover
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 6px rgba(0, 0, 0, 0.1)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
}

/**
 * Set up flash messages to auto-dismiss
 */
function setupFlashMessages() {
    const flashMessages = document.querySelectorAll('.alert');

    flashMessages.forEach(message => {
        // Add close button
        const closeButton = document.createElement('span');
        closeButton.innerHTML = '&times;';
        closeButton.className = 'alert-close';
        closeButton.style.float = 'right';
        closeButton.style.cursor = 'pointer';
        closeButton.style.fontWeight = 'bold';

        closeButton.addEventListener('click', function() {
            fadeOut(message);
        });

        message.appendChild(closeButton);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            fadeOut(message);
        }, 5000);
    });
}

/**
 * Fade out an element and remove it from the DOM
 * @param {HTMLElement} element - The element to fade out
 */
function fadeOut(element) {
    let opacity = 1;
    const timer = setInterval(function() {
        if (opacity <= 0.1) {
            clearInterval(timer);
            element.style.display = 'none';
            element.parentNode.removeChild(element);
        }
        element.style.opacity = opacity;
        opacity -= opacity * 0.1;
    }, 10);
}
