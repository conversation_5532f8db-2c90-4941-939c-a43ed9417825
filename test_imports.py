#!/usr/bin/env python3
"""
Test script to check imports.
"""

print("Testing imports...")

try:
    import flask
    print("✓ Flask imported successfully")
except ImportError as e:
    print(f"✗ Flask import failed: {e}")

try:
    import PyPDF2
    print("✓ PyPDF2 imported successfully")
except ImportError as e:
    print(f"✗ PyPDF2 import failed: {e}")

try:
    import pdfplumber
    print("✓ pdfplumber imported successfully")
except ImportError as e:
    print(f"✗ pdfplumber import failed: {e}")

try:
    import docx
    print("✓ python-docx imported successfully")
except ImportError as e:
    print(f"✗ python-docx import failed: {e}")

try:
    import nltk
    print("✓ NLTK imported successfully")
    
    # Try to access NLTK data
    try:
        from nltk.corpus import stopwords
        stop_words = stopwords.words('english')
        print(f"✓ NLTK stopwords loaded: {len(stop_words)} words")
    except Exception as e:
        print(f"✗ NLTK stopwords failed: {e}")
        
except ImportError as e:
    print(f"✗ NLTK import failed: {e}")

try:
    from textblob import TextBlob
    print("✓ TextBlob imported successfully")
except ImportError as e:
    print(f"✗ TextBlob import failed: {e}")

print("Import test completed!")
