#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ML Resume Analyzer

This module provides machine learning functionality for analyzing resumes
and predicting their fit for job descriptions.

Author: Adam
Version: 1.0.0
"""

import os
import pickle
import logging
from typing import List, Dict, Any, Tuple, Optional
import numpy as np

# ML related imports
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Path for saving the trained model and vectorizer
MODEL_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'models')
MODEL_PATH = os.path.join(MODEL_DIR, 'resume_fit_model.pkl')
VECTORIZER_PATH = os.path.join(MODEL_DIR, 'tfidf_vectorizer.pkl')

# Ensure model directory exists
os.makedirs(MODEL_DIR, exist_ok=True)

class ResumeMLAnalyzer:
    """Machine learning analyzer for resume-job fit prediction."""
    
    def __init__(self):
        """Initialize the ML analyzer."""
        self.vectorizer = None
        self.model = None
        self._load_or_train_model()
    
    def _load_or_train_model(self) -> None:
        """Load existing model or train a new one if not available."""
        try:
            if os.path.exists(MODEL_PATH) and os.path.exists(VECTORIZER_PATH):
                logger.info("Loading existing ML model and vectorizer")
                with open(MODEL_PATH, 'rb') as f:
                    self.model = pickle.load(f)
                with open(VECTORIZER_PATH, 'rb') as f:
                    self.vectorizer = pickle.load(f)
            else:
                logger.info("Training new ML model")
                self._train_initial_model()
        except Exception as e:
            logger.error(f"Error loading/training model: {str(e)}")
            # Fall back to training a new model
            self._train_initial_model()
    
    def _train_initial_model(self) -> None:
        """Train an initial model with sample data."""
        # Sample CV texts and labels
        texts = [
            "I am a software engineer with Python experience. Developed web applications using Flask and Django. Proficient in JavaScript, HTML, CSS.",
            "Retail experience, sales, customer support. Managed inventory and handled customer complaints. Cashier experience.",
            "Data analyst with Excel and SQL skills. Created dashboards and reports. Experience with data visualization.",
            "Full stack developer with React and Node.js experience. Built RESTful APIs and single page applications.",
            "Marketing specialist with social media experience. Created content for Facebook, Instagram, and Twitter.",
            "Project manager with Agile methodology experience. Led teams of 5-10 people. Scrum master certified.",
            "DevOps engineer with AWS and Docker experience. Implemented CI/CD pipelines using Jenkins.",
            "UX/UI designer with Figma and Adobe XD experience. Created wireframes and prototypes.",
            "Machine learning engineer with TensorFlow and PyTorch experience. Built and deployed ML models.",
            "Technical writer with documentation experience. Created user manuals and API documentation."
        ]
        
        # 1 = fit for tech job, 0 = not fit for tech job
        labels = [1, 0, 1, 1, 0, 1, 1, 1, 1, 1]
        
        # Create and fit the vectorizer
        self.vectorizer = TfidfVectorizer(max_features=1000)
        X = self.vectorizer.fit_transform(texts)
        
        # Train the model
        self.model = LogisticRegression(max_iter=1000)
        self.model.fit(X, labels)
        
        # Save the model and vectorizer
        with open(MODEL_PATH, 'wb') as f:
            pickle.dump(self.model, f)
        with open(VECTORIZER_PATH, 'wb') as f:
            pickle.dump(self.vectorizer, f)
        
        logger.info("Initial ML model trained and saved")
    
    def predict_resume_fit(self, resume_text: str, job_description: str) -> Dict[str, Any]:
        """
        Predict if a resume is a good fit for a job description.
        
        Args:
            resume_text: The text content of the resume
            job_description: The text content of the job description
            
        Returns:
            Dict with prediction results
        """
        if not self.model or not self.vectorizer:
            logger.error("Model or vectorizer not available")
            return {
                "fit_score": 0.5,
                "fit_label": "Unknown",
                "confidence": 0.0
            }
        
        try:
            # Combine resume and job description for analysis
            combined_text = f"{resume_text} {job_description}"
            
            # Transform the text using the vectorizer
            X = self.vectorizer.transform([combined_text])
            
            # Get prediction and probability
            prediction = self.model.predict(X)[0]
            probabilities = self.model.predict_proba(X)[0]
            confidence = probabilities[prediction]
            
            # Prepare result
            result = {
                "fit_score": float(probabilities[1]),  # Probability of being a good fit
                "fit_label": "Good Fit" if prediction == 1 else "Not a Good Fit",
                "confidence": float(confidence)
            }
            
            logger.info(f"ML prediction: {result['fit_label']} with {result['confidence']:.2f} confidence")
            return result
            
        except Exception as e:
            logger.error(f"Error predicting resume fit: {str(e)}")
            return {
                "fit_score": 0.5,
                "fit_label": "Error in prediction",
                "confidence": 0.0
            }
    
    def update_model(self, texts: List[str], labels: List[int]) -> Dict[str, float]:
        """
        Update the model with new training data.
        
        Args:
            texts: List of text samples
            labels: List of corresponding labels (1 = fit, 0 = not fit)
            
        Returns:
            Dict with model performance metrics
        """
        try:
            # Update vectorizer with new texts
            X = self.vectorizer.fit_transform(texts)
            
            # Split data for training and evaluation
            X_train, X_test, y_train, y_test = train_test_split(
                X, labels, test_size=0.2, random_state=42
            )
            
            # Train model
            self.model = LogisticRegression(max_iter=1000)
            self.model.fit(X_train, y_train)
            
            # Evaluate model
            predictions = self.model.predict(X_test)
            metrics = {
                "accuracy": accuracy_score(y_test, predictions),
                "precision": precision_score(y_test, predictions, zero_division=0),
                "recall": recall_score(y_test, predictions, zero_division=0),
                "f1": f1_score(y_test, predictions, zero_division=0)
            }
            
            # Save updated model and vectorizer
            with open(MODEL_PATH, 'wb') as f:
                pickle.dump(self.model, f)
            with open(VECTORIZER_PATH, 'wb') as f:
                pickle.dump(self.vectorizer, f)
            
            logger.info(f"Model updated with {len(texts)} samples. Accuracy: {metrics['accuracy']:.2f}")
            return metrics
            
        except Exception as e:
            logger.error(f"Error updating model: {str(e)}")
            return {
                "accuracy": 0.0,
                "precision": 0.0,
                "recall": 0.0,
                "f1": 0.0
            }

# Singleton instance
analyzer = ResumeMLAnalyzer()

def predict_resume_fit(resume_text: str, job_description: str) -> Dict[str, Any]:
    """
    Predict if a resume is a good fit for a job description.
    
    Args:
        resume_text: The text content of the resume
        job_description: The text content of the job description
        
    Returns:
        Dict with prediction results
    """
    return analyzer.predict_resume_fit(resume_text, job_description)
