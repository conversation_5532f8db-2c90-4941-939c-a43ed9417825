"""
Database models for the Applicant Tracking System.

This module defines the database models for the application, including:
- User: Represents a user in the system (admin or regular user)
- Applicant: Represents a job applicant
- CV: Represents a CV/resume uploaded by an applicant
- Skill: Represents a skill extracted from a CV
- JobApplication: Represents a job application submitted by an applicant
- Job: Represents a job listing
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(db.Model):
    """
    Represents a user in the system (admin or regular user).
    """
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='user')  # 'user' or 'admin'
    first_name = db.Column(db.String(50))
    last_name = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    # Relationship with Applicant
    applicant_id = db.Column(db.Integer, db.ForeignKey('applicant.id'), nullable=True)
    applicant = db.relationship('Applicant', backref=db.backref('user', uselist=False), lazy=True)

    def __init__(self, username, email, password, role='user', first_name=None, last_name=None):
        """Initialize a new user."""
        self.username = username
        self.email = email
        self.set_password(password)
        self.role = role
        self.first_name = first_name
        self.last_name = last_name

    def set_password(self, password):
        """Set the password hash for the user."""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check if the provided password matches the stored hash."""
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        """Check if the user is an admin."""
        return self.role == 'admin'

    def update_last_login(self):
        """Update the last login timestamp."""
        self.last_login = datetime.utcnow()
        db.session.commit()

    def to_dict(self):
        """Convert user object to dictionary."""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'applicant_id': self.applicant_id
        }

    def __repr__(self):
        """String representation of the user."""
        return f'<User {self.username}>'

# Association tables for many-to-many relationships
cv_skills = db.Table('cv_skills',
    db.Column('cv_id', db.Integer, db.ForeignKey('cv.id'), primary_key=True),
    db.Column('skill_id', db.Integer, db.ForeignKey('skill.id'), primary_key=True),
    db.Column('confidence', db.Float, default=1.0),  # Confidence score for the skill extraction
    db.Column('years_experience', db.Integer, nullable=True)  # Years of experience with this skill
)

job_skills = db.Table('job_skills',
    db.Column('job_id', db.Integer, db.ForeignKey('job.id'), primary_key=True),
    db.Column('skill_id', db.Integer, db.ForeignKey('skill.id'), primary_key=True),
    db.Column('importance', db.Integer, default=1)  # Importance of this skill for the job (1-5)
)

class Applicant(db.Model):
    """
    Represents a job applicant in the system.
    """
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    cvs = db.relationship('CV', backref='applicant', lazy=True)
    applications = db.relationship('JobApplication', backref='applicant', lazy=True)

    def __repr__(self):
        return f'<Applicant {self.name}>'

    def to_dict(self):
        """Convert the applicant to a dictionary for JSON serialization."""
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'cv_count': len(self.cvs),
            'application_count': len(self.applications)
        }

class CV(db.Model):
    """
    Represents a CV/resume uploaded by an applicant.
    """
    id = db.Column(db.Integer, primary_key=True)
    applicant_id = db.Column(db.Integer, db.ForeignKey('applicant.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)  # Size in bytes
    content_type = db.Column(db.String(100), nullable=False)  # MIME type
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Extracted information
    extracted_name = db.Column(db.String(100), nullable=True)
    extracted_email = db.Column(db.String(100), nullable=True)
    extracted_phone = db.Column(db.String(20), nullable=True)
    education = db.Column(db.Text, nullable=True)
    experience = db.Column(db.Text, nullable=True)
    raw_text = db.Column(db.Text, nullable=True)  # Raw text extracted from the CV
    analysis_data = db.Column(db.Text, nullable=True)  # JSON string with additional analysis data

    # Analysis metadata
    analysis_version = db.Column(db.String(20), nullable=True)  # Version of the analysis algorithm
    analysis_date = db.Column(db.DateTime, nullable=True)
    analysis_duration = db.Column(db.Float, nullable=True)  # Time taken to analyze in seconds
    confidence_score = db.Column(db.Float, default=0.0)  # Overall confidence in the analysis

    # Relationships
    skills = db.relationship('Skill', secondary=cv_skills, lazy='subquery',
                            backref=db.backref('cvs', lazy=True))
    applications = db.relationship('JobApplication', backref='cv', lazy=True)

    def __repr__(self):
        return f'<CV {self.filename}>'

    def to_dict(self):
        """Convert the CV to a dictionary for JSON serialization."""
        skill_data = [
            {
                'id': skill.id,
                'name': skill.name,
                'category': skill.category,
                'confidence': 1.0,  # Default confidence
                'years_experience': None  # Default years experience
            }
            for skill in self.skills
        ]

        analysis_data_dict = {}
        if self.analysis_data:
            try:
                analysis_data_dict = json.loads(self.analysis_data)
            except json.JSONDecodeError:
                analysis_data_dict = {}

        return {
            'id': self.id,
            'applicant_id': self.applicant_id,
            'filename': self.filename,
            'file_size': self.file_size,
            'content_type': self.content_type,
            'uploaded_at': self.uploaded_at.isoformat(),
            'extracted_name': self.extracted_name,
            'extracted_email': self.extracted_email,
            'extracted_phone': self.extracted_phone,
            'education': self.education,
            'experience': self.experience,
            'skills': skill_data,
            'analysis_version': self.analysis_version,
            'analysis_date': self.analysis_date.isoformat() if self.analysis_date else None,
            'analysis_duration': self.analysis_duration,
            'confidence_score': self.confidence_score,
            'analysis_data': analysis_data_dict
        }

class Skill(db.Model):
    """
    Represents a skill that can be associated with CVs and jobs.
    """
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    category = db.Column(db.String(50), nullable=True)  # e.g., "Programming", "Language", "Soft Skill"
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Skill {self.name}>'

    def to_dict(self):
        """Convert the skill to a dictionary for JSON serialization."""
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'description': self.description
        }

class Job(db.Model):
    """
    Represents a job listing in the system.
    """
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    company = db.Column(db.String(100), nullable=False)
    location = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    requirements = db.Column(db.Text, nullable=True)
    salary_min = db.Column(db.Float, nullable=True)
    salary_max = db.Column(db.Float, nullable=True)
    job_type = db.Column(db.String(50), nullable=True)  # e.g., "Full-time", "Part-time", "Contract"
    experience_level = db.Column(db.String(50), nullable=True)  # e.g., "Entry", "Mid", "Senior"
    posted_date = db.Column(db.DateTime, default=datetime.utcnow)
    closing_date = db.Column(db.DateTime, nullable=True)
    is_active = db.Column(db.Boolean, default=True)

    # Relationships
    skills = db.relationship('Skill', secondary=job_skills, lazy='subquery',
                            backref=db.backref('jobs', lazy=True))
    applications = db.relationship('JobApplication', backref='job', lazy=True)

    def __repr__(self):
        return f'<Job {self.title} at {self.company}>'

    def to_dict(self):
        """Convert the job to a dictionary for JSON serialization."""
        skill_data = [
            {
                'id': skill.id,
                'name': skill.name,
                'category': skill.category,
                'importance': 1  # Default importance
            }
            for skill in self.skills
        ]

        return {
            'id': self.id,
            'title': self.title,
            'company': self.company,
            'location': self.location,
            'description': self.description,
            'requirements': self.requirements,
            'salary_min': self.salary_min,
            'salary_max': self.salary_max,
            'job_type': self.job_type,
            'experience_level': self.experience_level,
            'posted_date': self.posted_date.isoformat(),
            'closing_date': self.closing_date.isoformat() if self.closing_date else None,
            'is_active': self.is_active,
            'skills': skill_data,
            'application_count': len(self.applications)
        }

class JobApplication(db.Model):
    """
    Represents a job application submitted by an applicant.
    """
    id = db.Column(db.Integer, primary_key=True)
    applicant_id = db.Column(db.Integer, db.ForeignKey('applicant.id'), nullable=False)
    job_id = db.Column(db.Integer, db.ForeignKey('job.id'), nullable=False)
    cv_id = db.Column(db.Integer, db.ForeignKey('cv.id'), nullable=False)
    status = db.Column(db.String(50), default='Applied')  # e.g., "Applied", "Screening", "Interview", "Offer", "Rejected"
    application_date = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    cover_letter = db.Column(db.Text, nullable=True)
    match_score = db.Column(db.Float, default=0.0)  # Score indicating how well the applicant matches the job
    notes = db.Column(db.Text, nullable=True)

    def __repr__(self):
        return f'<JobApplication {self.id} - {self.status}>'

    def to_dict(self):
        """Convert the job application to a dictionary for JSON serialization."""
        return {
            'id': self.id,
            'applicant_id': self.applicant_id,
            'job_id': self.job_id,
            'cv_id': self.cv_id,
            'status': self.status,
            'application_date': self.application_date.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'match_score': self.match_score,
            'notes': self.notes
        }

class Settings(db.Model):
    """
    Represents system settings.
    """
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=True)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Settings {self.key}>'

    def to_dict(self):
        """Convert the settings to a dictionary for JSON serialization."""
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }


class AnalysisResult(db.Model):
    """
    Represents a stored CV analysis result for state management.
    """
    id = db.Column(db.Integer, primary_key=True)
    applicant_id = db.Column(db.Integer, db.ForeignKey('applicant.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    analysis_data = db.Column(db.Text, nullable=False)  # JSON string of analysis result
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship with Applicant
    applicant = db.relationship('Applicant', backref=db.backref('analysis_results', lazy=True))

    def __repr__(self):
        return f'<AnalysisResult {self.id} for Applicant {self.applicant_id}>'

    def to_dict(self):
        """Convert the analysis result to a dictionary for JSON serialization."""
        try:
            analysis_data = json.loads(self.analysis_data) if self.analysis_data else {}
        except json.JSONDecodeError:
            analysis_data = {}

        return {
            'id': self.id,
            'applicant_id': self.applicant_id,
            'filename': self.filename,
            'analysis_data': analysis_data,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

    def get_analysis_data(self):
        """Get the parsed analysis data as a dictionary."""
        try:
            return json.loads(self.analysis_data) if self.analysis_data else {}
        except json.JSONDecodeError:
            return {}

    def set_analysis_data(self, data):
        """Set the analysis data from a dictionary."""
        self.analysis_data = json.dumps(data) if data else '{}'
